name: Update Documentation
on:
  push:
    branches:
      - main

jobs:
  update-docs:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          ref: docs/update
          fetch-depth: 0

      - name: Set up Git
        run: |
          git config user.name "GitHub Actions Bot"
          git config user.email "<EMAIL>"

      - name: Fetch main branch
        run: git fetch origin main:main

      - name: Rebase onto main
        id: rebase
        continue-on-error: true
        run: |
          git checkout docs/update
          if ! git rebase main; then
            echo "Rebase conflict detected"
            git rebase --abort
            echo "rebase_failed=true" >> $GITHUB_OUTPUT
          fi

      # - name: Notify on rebase conflict
      #   if: steps.rebase.outputs.rebase_failed == 'true'
      #   uses: slackapi/slack-github-action@v1.26.0
      #   with:
      #     slack-bot-token: ${{ secrets.SLACK_BOT_TOKEN }}
      #     channel-id: 'C0700000000'
      #     text: 'Rebase conflict in docs/update! Please resolve manually.'

      - name: Run Cursor Background Agent
        if: steps.rebase.outputs.rebase_failed != 'true'
        run: |
          # Replace with <PERSON>ursor's CLI/API command to regenerate docs
          cursor-agent --branch docs/update --task "regenerate docs"
          git add .
          if ! git diff --cached --quiet; then
            git commit -m "Update docs for main@$(git rev-parse --short main)"
            git push origin docs/update
            echo "Documentation updated and pushed"
          else
            echo "No changes to commit"
          fi

      - name: Trigger CodeRabbit and BugBot
        if: steps.rebase.outputs.rebase_failed != 'true'
        run: |
          # CodeRabbit and BugBot auto-trigger on commit
          echo "CodeRabbit and BugBot reviews triggered via commit"
