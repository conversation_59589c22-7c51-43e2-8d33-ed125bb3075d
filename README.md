# SupaMoto USSD Server

A production-ready USSD server for SupaMoto's clean energy platform in Zambia. Built with **Fastify** and **XState v5** state machines for robust, scalable USSD menu flows.

## ✨ Key Features

- **XState v5 Architecture**: Modular, hierarchical state machines with comprehensive patterns
- **USSD Menu System**: Complete \*2233# service implementation
- **Multi-tier Storage**: Redis-based session management with PostgreSQL persistence
- **Type Safety**: Full TypeScript integration with zero-error requirement
- **Comprehensive Testing**: Unit tests, integration tests, and interactive demo files
- **Developer Experience**: Automated validation, pre-commit hooks, and extensive documentation

## 🚀 Quick Start

### Prerequisites

- Node.js 20+
- pnpm 8+
- Docker & Docker Compose (recommended)
- OR PostgreSQL + Redis (local development)

### Installation

```bash
git clone https://github.com/ixoworld/ixo-ussd-server.git
cd ixo-ussd-server
pnpm install
```

### Database Setup (Docker - Recommended)

```bash
# Start PostgreSQL + Redis services
docker-compose up -d

# Run database migration
NODE_OPTIONS='--loader ts-node/esm' pnpm exec ts-node migrations/run-migrations.ts

# Verify setup
docker exec -it ixo-ussd-postgres psql -U ixo_ussd -d ixo_ussd -c "SELECT COUNT(*) FROM phones;"
```

### Environment Setup

Create `.env` file with required variables:

```env
# Database (Docker)
PG_USER=ixo_ussd
PG_PASSWORD=ixo_ussd_pass
PG_DATABASE=ixo_ussd
PG_HOST=localhost
PG_PORT=5432

# Redis (Docker)
REDIS_EPHEMERAL_DSN=redis://localhost:6379/1
REDIS_PERSISTENT_DSN=redis://localhost:6379/2

# Application
LOG_LEVEL=info
PIN_ENCRYPTION_KEY=your-32-byte-key
ZM_SERVICE_CODES=*2233#
```

### Development

```bash
# Start development server
pnpm dev

# Test database services
pnpm test:progressive-data    # Test customer creation flow
pnpm test:customer-id         # Test ID generation

# Run quality checks (required before commits)
pnpm tsc --noEmit && pnpm lint && pnpm test

# Test USSD endpoint
curl -X POST http://localhost:3000/api/ussd \
  -H "Content-Type: application/json" \
  -d '{"sessionId":"123","serviceCode":"*2233#","phoneNumber":"************","text":""}'
```

### Database Development

**Progressive Data Architecture**: Phone → Customer → Wallet (IXO Profile + Account) → Matrix Vault

```bash
# Database management
docker-compose up -d postgres redis    # Start services
docker-compose down                     # Stop services
docker-compose logs postgres           # View logs

# Migration workflow
NODE_OPTIONS='--loader ts-node/esm' pnpm exec ts-node migrations/run-migrations.ts

# Testing customer ID generation (deterministic, collision-resistant)
pnpm test:customer-id

# Testing progressive data creation (10 test customers)
pnpm test:progressive-data
```

## 🏗️ Architecture

**Modular XState v5 State Machines** with domain-driven design:

```
src/machines/supamoto-wallet/
├── core/                    # Entry point & routing
├── information/             # "Know More" flows
├── user-services/           # Authenticated user operations
├── agent/                   # Agent-specific workflows
├── guards/                  # Modular business logic
└── shared/                  # Reusable components
```

**Three-tier Storage**: Ephemeral sessions (Redis), persistent user profiles (Redis), transaction history (PostgreSQL)

## 📚 Documentation

**Comprehensive development guides:**

- **[Architecture Patterns Guide](./docs/development/Architecture-Patterns-Guide.md)** - Definitive architectural patterns with KnowMore Machine as reference
- **[XState Implementation Guide](./docs/development/XState-Implementation-Guide.md)** - Complete patterns and best practices
- **[State Machine Patterns](./docs/development/STATE_MACHINE_PATTERNS.md)** - Development workflow and standards
- **[Demo Files Guide](./docs/development/DEMO_FILES_GUIDE.md)** - Interactive development methodology
- **[Machine README](./src/machines/README.md)** - Architecture and usage patterns

**Development workflow:**

```bash
# Required pre-commit checks
pnpm tsc --noEmit && pnpm lint && pnpm test && pnpm validate:machines

# Interactive development
pnpm tsx src/machines/supamoto-wallet/core/welcomeMachine-demo.ts
```

## 🧪 Testing

**Multiple testing strategies:**

```bash
# Unit tests (every machine has .test.ts)
pnpm test

# Interactive demos (visual development)
pnpm tsx src/machines/supamoto-wallet/core/welcomeMachine-demo.ts

# Integration tests
pnpm test:interactive

# Recorded flow validation (manual)
pnpm test:replay
```

**Quality assurance:**

- Automated pattern validation (`pnpm validate:machines`)
- Pre-commit hooks with lint-staged
- Comprehensive TypeScript coverage
- Demo files for every machine

## 🔧 Development Patterns

**Established XState v5 patterns** (strictly enforced):

- ✅ Use `setup()` function with TypeScript types
- ✅ Handle input in `context: ({ input }) => ({ ... })`
- ✅ Always use `.js` extensions in imports
- ✅ Create tests for every machine (`machineName.test.ts`)
- ✅ Follow domain-based organization (core, information, user-services, agent)

**Creating new machines:**

```bash
# 1. Copy template
cp src/machines/supamoto-wallet/MACHINE_TEMPLATE.ts src/machines/supamoto-wallet/newMachine.ts

# 2. Follow established patterns
# 3. Add to domain index.ts
# 4. Run validation
pnpm validate:machines
```

## 🚀 Production Features

- **Session Management**: Redis-based with automatic cleanup and timeout handling
- **Error Recovery**: Comprehensive error handling with circuit breakers
- **Performance**: Memory management, clustering support, metrics endpoint
- **Security**: Rate limiting, input validation, secure session handling
- **Monitoring**: Prometheus metrics, structured logging, health checks
- **Internationalization**: Multi-language support with typesafe-i18n

## 📄 License

AGPL-3.0-or-later

---

**Ready to contribute?** Start with the [State Machine Patterns Guide](./docs/development/STATE_MACHINE_PATTERNS.md) and explore the [comprehensive documentation](./docs/development/) to understand the established patterns and architecture.
