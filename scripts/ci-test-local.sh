#!/bin/bash

# Local CI Test Script - mirrors GitHub Actions workflow
set -e

echo "🚀 Starting local CI test..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_step() {
    echo -e "${YELLOW}📋 $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Step 1: Check Docker and start services
print_step "Checking Docker daemon..."
if ! docker info > /dev/null 2>&1; then
    print_error "Docker is not running. Please start Docker Desktop and try again."
    exit 1
fi

print_step "Starting Redis and PostgreSQL services..."
docker-compose up -d redis postgres

# Wait for services to be healthy
print_step "Waiting for services to be ready..."
max_attempts=30
attempt=0

while [ $attempt -lt $max_attempts ]; do
    if docker-compose exec -T redis redis-cli ping > /dev/null 2>&1 && \
       docker-compose exec -T postgres pg_isready -U ixo_ussd > /dev/null 2>&1; then
        print_success "Services are ready!"
        break
    fi
    
    echo "Waiting for services... ($((attempt + 1))/$max_attempts)"
    sleep 2
    ((attempt++))
done

if [ $attempt -eq $max_attempts ]; then
    print_error "Services failed to start within timeout"
    exit 1
fi

# Step 2: Setup test environment (mirror GitHub Actions)
print_step "Setting up test environment..."

# Create .env.test file (similar to GitHub Actions setup)
cat > .env.test << EOF
REDIS_URL=redis://localhost:6379
DATABASE_URL=postgres://ixo_ussd:ixo_ussd_pass@localhost:5432/ixo_ussd_test
NODE_ENV=test
EOF

print_success "Test environment configured"

# Step 3: Install dependencies
print_step "Installing dependencies..."
pnpm install --no-frozen-lockfile

# Step 4: Build the project
print_step "Building project..."
pnpm build

# Step 5: Run database migrations
print_step "Running database migrations..."
export DATABASE_URL=postgres://ixo_ussd:ixo_ussd_pass@localhost:5432/ixo_ussd_test
node dist/migrations/run-migrations.js || echo "No migrations to run"

# Step 6: Run tests with coverage
print_step "Running tests with coverage..."
export NODE_OPTIONS="--experimental-vm-modules"
export REDIS_URL="redis://localhost:6379"
export DATABASE_URL="postgres://ixo_ussd:ixo_ussd_pass@localhost:5432/ixo_ussd_test"

pnpm test:coverage

# Step 7: Check coverage thresholds (mirror GitHub Actions logic)
print_step "Checking coverage thresholds..."

node -e "
const fs = require('fs');
const path = './coverage/coverage-summary.json';

if (!fs.existsSync(path)) {
    console.error('Coverage report not found!');
    process.exit(1);
}

const coverage = require(path);
const threshold = 0; // Same as GitHub Actions
const metrics = ['statements', 'branches', 'functions', 'lines'];
let failed = false;

console.log('Coverage Thresholds Check (minimum: ' + threshold + '%)');
console.log('=====================================');

metrics.forEach(metric => {
    const pct = coverage.total[metric].pct;
    const status = pct >= threshold ? '✅' : '❌';
    console.log(status + ' ' + metric + ': ' + pct + '%');
    if (pct < threshold) failed = true;
});

if (failed) {
    console.error('\nCoverage threshold not met!');
    process.exit(1);
} else {
    console.log('\nAll coverage thresholds met!');
}
"

# Step 8: Display coverage summary
print_step "Coverage Summary:"
if [ -f "coverage/coverage-summary.json" ]; then
    cat coverage/coverage-summary.json | jq -r '.total | to_entries | map("\(.key): \(.value.pct)%") | .[]'
else
    print_error "Coverage report not found"
fi

print_success "Local CI test completed successfully! 🎉"

echo ""
echo "📊 Coverage report available at: ./coverage/lcov-report/index.html"
echo "🧹 To clean up services: docker-compose down" 