#!/bin/bash

# Quick Test Script - for rapid iteration
set -e

echo "⚡ Running quick tests..."

# Check if Dock<PERSON> is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker Desktop and try again."
    echo "💡 Alternative: Run 'pnpm test' (without coverage) if you don't need Docker services"
    exit 1
fi

# Check if services are running
if ! docker-compose ps redis | grep -q "Up" || ! docker-compose ps postgres | grep -q "Up"; then
    echo "🔄 Starting services..."
    docker-compose up -d redis postgres
    echo "⏳ Waiting for services to be ready..."
    sleep 10
fi

# Export test environment
export NODE_OPTIONS="--experimental-vm-modules"
export REDIS_URL="redis://localhost:6379"
export DATABASE_URL="postgres://ixo_ussd:ixo_ussd_pass@localhost:5432/ixo_ussd_test"

# Run tests with coverage
echo "🧪 Running tests..."
pnpm test:coverage

echo "✅ Quick test completed!"
echo "📊 Coverage report: ./coverage/lcov-report/index.html" 