{"name": "ixo-ussd-server", "version": "0.1.0", "description": "A USSD server implementation for ixo platform integration", "repository": "https://github.com/alwyn-ixo/ixo-ussd-server", "license": "AGPL-3.0-or-later", "type": "module", "scripts": {"build": "rimraf dist && tsc --project tsconfig.json && tsc-alias -p tsconfig.json && pnpm copy-sql", "copy-sql": "copyfiles -u 1 -f \"migrations/**/*.sql\" dist/src/migrations/postgres", "dev": "NODE_ENV=dev ZM_SERVICE_CODES=*2233# node --watch --env-file=.env --loader ts-node/esm src/index.ts", "start": "NODE_ENV=production ZM_SERVICE_CODES=*2233# dotenv -e .env node dist/src/migrations/run-migrations.js && sleep 2 && node dist/src/index.js", "start:without-migrations": "NODE_ENV=production dotenv -e .env node dist/src/index.js", "test": "vitest --run --reporter=verbose", "test:watch": "vitest", "test:coverage": "vitest --coverage --run", "test:interactive": "NODE_OPTIONS='--loader ts-node/esm' ts-node --require dotenv/config src/test/interactive/interactive.ts", "test:standalone": "NODE_OPTIONS='--loader ts-node/esm' ts-node --require dotenv/config src/test/simple-test.ts", "test:replay": "TEST_ENVIRONMENT=real vitest --run src/test/e2e/recorded-flows.test.ts", "view:db-queries": "NODE_OPTIONS='--loader ts-node/esm' ts-node --require dotenv/config src/test/scripts/db-queries.ts", "test:progressive-data": "NODE_OPTIONS='--loader ts-node/esm' ts-node --require dotenv/config src/test/scripts/test-progressive-data.ts", "test:customer-id": "NODE_OPTIONS='--loader ts-node/esm' ts-node --require dotenv/config src/test/scripts/test-customer-id.ts", "test:wallet-creation": "NODE_OPTIONS='--loader ts-node/esm' ts-node --require dotenv/config src/test/scripts/test-wallet-creation.ts", "test:ussd-journey": "src/test/scripts/test-ussd-customer-journey.sh", "test:ixo-account": "NODE_OPTIONS='--loader ts-node/esm' ts-node --require dotenv/config src/test/scripts/test-ixo-account-refactored.ts", "test:ixo-modules": "NODE_OPTIONS='--loader ts-node/esm' ts-node --require dotenv/config src/test/scripts/test-ixo-account-refactored.ts --modules", "test:original-core": "NODE_OPTIONS='--loader ts-node/esm' ts-node --require dotenv/config src/test/scripts/test-original-core.ts", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,md}\" \"*.{ts,tsx,js,jsx,json,md}\"", "format:check": "prettier --check \"src/**/*.{ts,tsx,js,jsx,json,md}\" \"*.{ts,tsx,js,jsx,json,md}\"", "lint": "eslint \"src/**/*.{ts,tsx,js,jsx}\" --fix", "validate:machines": "node scripts/validate-machines.js", "prepare": "husky install"}, "lint-staged": {"src/**/*.{ts,tsx,js,jsx}": ["prettier --write", "eslint --fix"], "*.{ts,tsx,js,jsx,json,md}": ["prettier --write"]}, "engines": {"node": ">=20"}, "dependencies": {"@cosmjs/amino": "0.34.0", "@cosmjs/crypto": "^0.34.0", "@cosmjs/encoding": "0.34.0", "@cosmjs/proto-signing": "0.34.0", "@cosmjs/stargate": "0.34.0", "@fastify/cors": "^11.1.0", "@fastify/formbody": "^8.0.2", "@fastify/helmet": "^13.0.1", "@fastify/postgres": "^6.0.2", "@fastify/rate-limit": "^10.3.0", "@fastify/sensible": "^6.0.3", "@ixo/impactxclient-sdk": "2.4.1", "@ixo/matrixclient-sdk": "^0.3.4", "@types/blocked-at": "^1.0.4", "@types/md5": "^2.3.5", "@types/node": "^24.2.0", "@types/pg": "^8.15.5", "@types/react-dom": "^19.1.7", "africastalking": "^0.7.3", "axios": "1.11.0", "bcrypt": "^6.0.0", "bignumber.js": "9.3.1", "blocked-at": "^1.2.0", "bs58": "6.0.0", "cbor": "10.0.10", "copyfiles": "^2.4.1", "crypto-js": "4.2.0", "eciesjs": "^0.4.15", "ethers": "^6.15.0", "fastify": "5.4.0", "fastify-metrics": "^12.1.0", "fastify-plugin": "^5.0.1", "graphql-request": "^7.2.0", "iconv-lite": "^0.6.3", "kysely": "^0.28.4", "libphonenumber-js": "^1.12.10", "long": "^5.3.2", "matrix-js-sdk": "37.12.0", "md5": "^2.3.0", "moment-timezone": "^0.6.0", "nats": "^2.29.3", "newman": "^6.2.1", "pg": "^8.16.3", "pino": "^9.7.0", "protobufjs": "^7.5.3", "secure-web-storage": "1.0.2", "ts-essentials": "^10.1.1", "ts-node": "^10.9.2", "typesafe-i18n": "^5.26.2", "xstate": "^5.20.1", "zod": "^4.0.14"}, "devDependencies": {"@faker-js/faker": "^9.9.0", "@release-it/conventional-changelog": "^10.0.1", "@testing-library/jest-dom": "^6.6.4", "@types/autocannon": "^7.12.7", "@types/crypto-js": "4.2.2", "@types/node": "^18.0.0", "@vitest/coverage-v8": "^3.2.4", "auto-changelog": "^2.5.0", "autocannon": "^8.0.0", "dotenv-cli": "^10.0.0", "husky": "^9.1.7", "lint-staged": "^16.1.4", "madge": "^8.0.0", "release-it": "^19.0.4", "rimraf": "^6.0.1", "testcontainers": "^11.5.0", "ts-node-dev": "^2.0.0", "tsc-alias": "^1.8.16", "tsconfig-paths": "^4.2.0", "tsx": "^4.20.3", "typescript": "^5.9.2", "vitest": "^3.2.4"}, "optionalDependencies": {"@typescript-eslint/eslint-plugin": "^8.38.0", "@typescript-eslint/parser": "^8.38.0", "@xstate/cli": "^0.5.17", "dotenv": "^17.2.1", "eslint": "^9.32.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-prettier": "^5.5.3", "pino-debug": "^3.0.0", "pino-pretty": "^13.1.1", "prettier": "^3.6.2", "ts-standard": "^12.0.2"}}