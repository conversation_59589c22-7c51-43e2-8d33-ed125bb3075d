{"env": {"es2022": true, "node": true}, "extends": ["eslint:recommended"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": "latest", "sourceType": "module"}, "plugins": ["@typescript-eslint"], "rules": {"no-console": "error", "prefer-const": "error", "no-var": "error", "no-unused-vars": "off", "@typescript-eslint/no-unused-vars": "error", "@typescript-eslint/no-explicit-any": "off", "no-multiple-empty-lines": ["error", {"max": 1, "maxEOF": 0, "maxBOF": 0}], "eol-last": ["error", "always"]}, "overrides": [{"files": ["**/__tests__/**/*", "**/*.test.ts", "**/*.test.tsx", "**/*.spec.ts", "**/*.spec.tsx", "**/test/**/*", "**/tests/**/*"], "env": {"node": true, "es2022": true}, "globals": {"describe": "readonly", "it": "readonly", "test": "readonly", "expect": "readonly", "beforeAll": "readonly", "afterAll": "readonly", "beforeEach": "readonly", "afterEach": "readonly", "vi": "readonly"}, "rules": {"no-console": "off"}}], "ignorePatterns": ["dist/", "node_modules/", "coverage/", "*.js", "*.d.ts"]}