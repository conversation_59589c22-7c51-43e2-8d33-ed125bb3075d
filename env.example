# Server Configuration
NODE_ENV=development
PORT=3000
HOST=0.0.0.0
SERVER_DISABLE_REQUEST_LOGGING=false
TRUST_PROXY_ENABLED=false
USSD_MACHINE_TYPE=supamoto-wallet
LOG_LEVEL=debug
LOG_NAME=ixo-ussd-server

# Database
# PostgreSQL connection variables
DATABASE_URL=postgres://username:password@localhost:5432/database
PG_USER=your-user-name
PG_PASSWORD=your-user-password
PG_HOST=localhost
PG_PORT=5432
PG_DATABASE=ixo-ussd-dev

# IXO Platform
# Network type controls all blockchain URLs (devnet, testnet, mainnet, local)
CHAIN_NETWORK=devnet

# Matrix Configuration
SKIP_MATRIX_ONBOARDING=false
MATRIX_HOME_SERVER='https://devmx.ixo.earth'
MATRIX_BOT_URL='https://rooms.bot.devmx.ixo.earth'
MATRIX_STATE_BOT_URL='https://state.bot.devmx.ixo.earth'
MATRIX_REGISTRATION_TOKEN=your_matrix_token

# Feegrant Configuration
FEEGRANT_URL=https://feegrant.devnet.ixo.earth
FEEGRANT_AUTH="your_feegrant_token"
FEEGRANT_GRANTER="your_feegrant_granter"

# System
SYSTEM_SECRET=your_system_secret
PIN_ENCRYPTION_KEY=your_pin_encryption_key

# Metrics
METRICS_ENABLED=true

# SupaMoto-specific configuration
SUPAMOTO_TEST_WALLET_ID=your_test_wallet_id  # Test wallet ID for SupaMoto USSD flow similar to C21009802
AGENT_TEST_PIN=your_test_agent_pin  # Test agent PIN (5 digits) for SupaMoto Agent Menu similar to 54321
ZM_SERVICE_CODES=*2233#
ZM_SUPPORT_PHONE=0700000000
TIMEZONE=Africa/Lusaka

# Test Data
## BASE_URL = "http://localhost:3000/api/ussd"; # Localhost
BASE_URL = "https://ixo-ussd-server.onrender.com/api/ussd";
SESSION_ID = "123456";
PHONE_NUMBER = "+265888234567";
## SERVICE_CODE = "*384*46361#"; # Africa's Talking Simulator at https://developers.africastalking.com/simulator using Private Browser
SERVICE_CODE = "*2233#";
