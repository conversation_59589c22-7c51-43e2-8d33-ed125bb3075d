# Task List: Specification-Driven Development Workflow

Based on PRD: `tasks/prd-specification-driven-development.md`

## Relevant Files

- `src/generators/mermaid-parser.ts` - Core parser for extracting machine specifications from Mermaid diagrams
- `src/generators/mermaid-parser.test.ts` - Unit tests for Mermaid parser functionality
- `src/generators/machine-generator.ts` - XState v5 machine code generation engine
- `src/generators/machine-generator.test.ts` - Unit tests for machine generation logic
- `src/generators/test-generator.ts` - Test suite generation for generated machines
- `src/generators/test-generator.test.ts` - Unit tests for test generation functionality
- `src/generators/templates/machine-template.ts` - TypeScript templates for XState v5 machine structure
- `src/generators/templates/test-template.ts` - TypeScript templates for test suite generation
- `src/generators/templates/demo-template.ts` - TypeScript templates for demo file generation
- `src/generators/cli/generate-machines.ts` - CLI command implementation for `pnpm generate:machines`
- `src/generators/cli/generate-tests.ts` - CLI command implementation for `pnpm generate:tests`
- `src/generators/utils/file-utils.ts` - File system utilities for code generation
- `src/generators/utils/file-utils.test.ts` - Unit tests for file utilities
- `src/generators/utils/validation.ts` - Validation utilities for Mermaid syntax and machine structure
- `src/generators/utils/validation.test.ts` - Unit tests for validation utilities
- `src/generators/types/generator-types.ts` - TypeScript interfaces for generator system
- `package.json` - Updated with new CLI scripts for code generation
- `docs/development/Code-Generation-Guide.md` - Developer documentation for the generation system

### Notes

- Generated machine files will use `.generated.ts` extension to indicate auto-generation
- All generated code must pass TypeScript compilation and follow existing XState v5 patterns
- Use `pnpm test` to run all tests including new generator tests
- CLI commands will be available as `pnpm generate:machines` and `pnpm generate:tests`

## Tasks

- [ ] 1.0 Create Mermaid Parser Infrastructure
  - [ ] 1.1 Create `src/generators/types/generator-types.ts` with TypeScript interfaces for machine specifications, states, transitions, and CSS class annotations
  - [ ] 1.2 Implement `src/generators/mermaid-parser.ts` to parse `docs/requirements/USSD-menu-mermaid.md` and extract machine definitions
  - [ ] 1.3 Add support for CSS class detection (info-machine, user-machine, agent-machine, account-machine) to categorize machines
  - [ ] 1.4 Implement state and transition extraction from Mermaid syntax with proper error handling for malformed diagrams
  - [ ] 1.5 Create comprehensive unit tests in `src/generators/mermaid-parser.test.ts` covering valid parsing, error cases, and edge scenarios
  - [ ] 1.6 Add file system utilities in `src/generators/utils/file-utils.ts` for reading Mermaid files and managing output directories

- [ ] 2.0 Build XState v5 Machine Generator
  - [ ] 2.1 Create machine template system in `src/generators/templates/machine-template.ts` following established XState v5 patterns (setup function, context from input)
  - [ ] 2.2 Implement `src/generators/machine-generator.ts` to convert parsed machine specifications into TypeScript code
  - [ ] 2.3 Generate proper TypeScript interfaces for machine context, events, and state definitions
  - [ ] 2.4 Create state definitions with transitions, guards, and action placeholders following project conventions
  - [ ] 2.5 Implement directory structure creation (`src/machines/supamoto-wallet/[category]/`) with proper file naming
  - [ ] 2.6 Add `.generated.ts` extension handling to clearly mark auto-generated files
  - [ ] 2.7 Generate demo files (`*-demo.ts`) using `src/generators/templates/demo-template.ts` for interactive machine testing
  - [ ] 2.8 Create comprehensive unit tests in `src/generators/machine-generator.test.ts` validating generated code structure and TypeScript compliance

- [ ] 3.0 Implement Test Suite Generation
  - [ ] 3.1 Create test template system in `src/generators/templates/test-template.ts` following established Vitest patterns
  - [ ] 3.2 Implement `src/generators/test-generator.ts` to generate comprehensive test suites for each machine
  - [ ] 3.3 Generate smoke tests for basic machine functionality (creation, startup, initial state verification)
  - [ ] 3.4 Create state transition tests covering all defined flows and event handling scenarios
  - [ ] 3.5 Add error handling test scenarios for invalid inputs and edge cases
  - [ ] 3.6 Generate service class templates in `src/services/` for External Service Pattern integration
  - [ ] 3.7 Create unit tests in `src/generators/test-generator.test.ts` ensuring generated tests are valid and executable

- [ ] 4.0 Create CLI Commands and Integration
  - [ ] 4.1 Implement `src/generators/cli/generate-machines.ts` CLI command for `pnpm generate:machines` with proper argument parsing
  - [ ] 4.2 Implement `src/generators/cli/generate-tests.ts` CLI command for `pnpm generate:tests` with incremental update support
  - [ ] 4.3 Add CLI scripts to `package.json` with proper script definitions and help documentation
  - [ ] 4.4 Implement incremental updates to only regenerate machines that have changed in the Mermaid diagram
  - [ ] 4.5 Add progress reporting and verbose logging options for CLI commands
  - [ ] 4.6 Create integration with existing build system ensuring generated files are included in TypeScript compilation
  - [ ] 4.7 Add CLI help documentation and usage examples for both generation commands

- [ ] 5.0 Add Validation and Error Handling
  - [ ] 5.1 Implement `src/generators/utils/validation.ts` with comprehensive Mermaid syntax validation
  - [ ] 5.2 Add business rule validation ensuring generated machines follow established XState v5 patterns
  - [ ] 5.3 Create semantic validation for state transitions and event handling consistency
  - [ ] 5.4 Implement clear error messages with line numbers and suggestions for fixing Mermaid syntax issues
  - [ ] 5.5 Add validation for directory structure conflicts and file naming conventions
  - [ ] 5.6 Create unit tests in `src/generators/utils/validation.test.ts` covering all validation scenarios
  - [ ] 5.7 Add TypeScript compilation validation for all generated code before file output

- [ ] 6.0 Prepare to Commit changes
  - [ ] 6.1 Run `pnpm tsc --noEmit` to check that no TypeScript errors were introduced
  - [ ] 6.2 Run `pnpm self-update && pnpm install && pnpm build` to confirm that no compilation errors were introduced
  - [ ] 6.3 Run `pnpm format` to find linter issues and to pretty format the code
  - [ ] 6.4 Run `pnpm lint` to find linter issues
  - [ ] 6.5 Run `pnpm test` to ensure all tests pass

## Implementation Notes

### Phase-Based Development

- **Phase 1**: Focus on basic parsing and simple machine generation first
- **Phase 2**: Add complete machine generation with full XState v5 features
- **Phase 3**: Implement comprehensive test generation and CLI integration

### Key Technical Decisions

- **External Service Pattern**: Generated machines remain pure structure with business logic in separate service classes
- **Incremental Updates**: Only regenerate machines that have changed to optimize performance
- **Full Validation**: Implement comprehensive validation including business rule checks
- **Generated File Indicators**: Use `.generated.ts` extension to prevent manual editing

### Success Criteria

- All generated code passes TypeScript compilation without errors
- Generated machines follow established XState v5 patterns exactly
- CLI commands provide clear feedback and error messages
- Test suites provide comprehensive coverage of generated machine functionality
- Documentation enables junior developers to use the system effectively

### Integration Requirements

- Must work with existing XState v5 setup and TypeScript configuration
- Should integrate with current Vitest testing framework
- Must respect existing directory organization and naming conventions
- Generated files must integrate seamlessly with existing build process
