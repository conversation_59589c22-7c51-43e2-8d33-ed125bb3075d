# PRD: Specification-Driven Development Workflow

## Introduction/Overview

The current USSD state machine development workflow requires manual creation of XState machines, tests, and documentation updates whenever changes are made to the Mermaid diagram specification. This manual process is time-consuming, error-prone, and creates risk of the specification and implementation drifting out of sync.

This feature will implement an automated code generation system that uses the `USSD-menu-mermaid.md` diagram as the single source of truth, automatically generating XState v5 machines and test suites when the specification changes.

**Goal:** Transform the development workflow from manual and error-prone to automated and consistent, enabling rapid feature development while maintaining code quality and specification alignment.

## Goals

1. **Eliminate Manual Boilerplate**: Automatically generate XState machine structure, context, events, and basic actions from Mermaid specifications
2. **Ensure Specification Consistency**: Keep Mermaid diagram and implementation perfectly synchronized
3. **Accelerate Feature Development**: Reduce new machine creation time from hours to seconds
4. **Maintain Code Quality**: Generate consistent, well-structured machines following established patterns
5. **Reduce Implementation Bugs**: Eliminate manual coding errors through automated generation

## User Stories

**As a developer**, I want to add new states to the Mermaid diagram and have the corresponding XState machines automatically generated, so that I can focus on business logic instead of boilerplate code.

**As a developer**, I want to modify existing flows in the Mermaid diagram and have the changes propagated to the implementation, so that the specification and code never drift out of sync.

**As a developer**, I want generated machines to follow the established XState v5 patterns (setup function, context initialization, proper typing), so that all machines maintain consistency.

**As a developer**, I want comprehensive test suites automatically generated for new machines, so that I have immediate confidence in the generated code.

**As a developer**, I want clear error messages when the Mermaid diagram has syntax issues, so that I can quickly fix specification problems.

## Functional Requirements

### Phase 1: Basic Mermaid Parser + Simple Machine Generation

1. The system must parse the `docs/requirements/USSD-menu-mermaid.md` file and extract machine specifications
2. The system must identify different machine types using CSS class annotations (info-machine, user-machine, agent-machine, account-machine)
3. The system must generate basic XState v5 machine files with proper TypeScript interfaces
4. The system must create machines following the established patterns (setup function, context from input, proper exports)
5. The system must place generated files in the correct directory structure (`src/machines/supamoto-wallet/[category]/`)
6. The system must provide a CLI command `pnpm generate:machines` to trigger generation
7. The system must fail fast with clear error messages for invalid Mermaid syntax or conflicts
8. The system must not modify existing hand-written code or complex business logic

### Phase 2: Complete Machine Generation

9. The system must generate complete state definitions with proper transitions and event handling
10. The system must create context interfaces based on inferred data requirements
11. The system must generate event type definitions for all possible machine interactions
12. The system must include proper guards and actions placeholders following project patterns
13. The system must generate routing states that integrate with the parent supamotoWalletMachine
14. The system must create demo files (`*-demo.ts`) for interactive testing of generated machines

### Phase 3: Test Generation

15. The system must generate comprehensive test suites for each generated machine
16. The system must create smoke tests verifying basic machine functionality (creation, startup, event handling)
17. The system must generate state transition tests for all defined flows
18. The system must include error handling test scenarios
19. The system must follow the established test patterns and naming conventions
20. The system must provide a CLI command `pnpm generate:tests` for test generation
21. The system must generate machines using the External Service Pattern with Promise actors for business logic integration
22. The system must create service class templates in `src/services/` for custom business logic implementation

## Non-Goals (Out of Scope)

- **Complex Business Logic**: Will not generate actual business logic implementations, only structure and placeholders
- **Existing Code Modification**: Will not modify hand-written code or existing machine implementations
- **Database Schema Changes**: Will not handle database migrations or schema updates
- **Real-time File Watching**: Will not include automatic regeneration on file changes (CLI-only approach)
- **Git Integration**: Will not include git hooks or automated commits
- **UI/Frontend Generation**: Will only generate backend state machine code

## Technical Considerations

- **Integration**: Must work with existing XState v5 setup and TypeScript configuration
- **Dependencies**: Should use existing project dependencies where possible (avoid adding heavy parsing libraries)
- **File Structure**: Must respect current directory organization and naming conventions
- **Type Safety**: All generated code must pass TypeScript compilation without errors
- **Testing**: Generated tests must integrate with existing Vitest setup and pass validation
- **Patterns**: Must follow established machine patterns (context from input, setup function, proper exports)

## Success Metrics

1. **Reduced Implementation Bugs**: Eliminate manual coding errors through consistent code generation
2. **Faster Feature Development**: Reduce new machine creation time from hours to seconds
3. **Better Consistency**: All generated machines follow identical patterns and structure
4. **Specification Alignment**: Zero drift between Mermaid diagram and implementation
5. **Developer Confidence**: Generated tests provide immediate validation of machine behavior

## Architecture Decisions

### Regeneration Strategy

The system will use **incremental updates** - only regenerating machines that have changed in the Mermaid diagram to optimize performance and minimize unnecessary file changes.

### Custom Business Logic Preservation

The system will use the **External Service Pattern** - generated machines will remain pure structure with business logic implemented in separate service classes. This ensures:

- Safe regeneration without overwriting custom logic
- Clean separation of concerns (machines handle flow, services handle logic)
- Testable business logic through independent service unit tests
- Integration via XState Promise actors for external calls

### Validation Level

The system will implement **full validation** including business rule checks to ensure generated machines are semantically correct and follow established patterns.

### Generated File Indicators

Generated files will use the **`.generated.ts` extension** to clearly indicate auto-generation and prevent manual editing.

### Pattern Evolution

**Manual migration** approach - generator templates will be updated when XState v5 patterns change, with clear documentation for any required updates.
