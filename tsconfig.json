{"compilerOptions": {"target": "ES2022", "module": "NodeNext", "moduleResolution": "NodeNext", "esModuleInterop": true, "sourceMap": true, "outDir": "dist", "strict": true, "lib": ["ES2022"], "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "skipLibCheck": true, "baseUrl": ".", "paths": {"@/*": ["src/*"]}, "isolatedModules": true, "types": ["vitest/globals", "node", "@ixo/impactxclient-sdk"]}, "include": ["src/**/*", "test/**/*"], "exclude": ["node_modules", "dist", "src/reference/**/*", "src/**/*-demo.ts", "src/services/ixo-account.ts"]}