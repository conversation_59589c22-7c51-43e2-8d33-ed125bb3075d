```mermaid
---
config:
  theme: neo
  layout: dagre
  look: classic
---

flowchart LR
Start(["Start"]) --> PreMenu["*2233# Pre-Menu"]
SMS(["Send SMS"]) --> Back["Back"]
Back --> Close(["Close Session"])
PreMenu --> KnowMore["1.Know More"] & WalletID["2.Enter Wallet ID"] & AgentMenu["3.Agent Menu"]
KnowMore --> M1["1.Interested in Stove"] & M2["2.Pellet Bag Prices & Accessories"] & M3["3.Can we deliver to you?"] & M4["4.Can a stove be fixed?"] & M5["5.What is Performance?"] & M6["6.What is a Digital Voucher?"] & M7["7.What is a Contract?"]
M1 --> SMS
M2 --> SMS
M3 --> SMS
M4 --> SMS
M5 --> SMS
M6 --> SMS
M7 --> SMS
WalletID --> Verify["Verify that MSISDN belongs to Wallet ID"]
Verify -- NO --> WalletID
Verify -- YES --> UserMenu["User Menu"]
UserMenu --> U1["1.Top up & Balance"] & U2["2.Purchase Pellet Bag & Accessory"] & U3["3.Order & Tracking"] & U4["4.Report Faulty Stove"] & U5["5.Performance"] & U6["6.Digital Voucher"] & U7["7.Account Details"]
U1 --> Topup["1.Top Up Balance on Wallet"] & Balance["2.Check Balance"]
Topup --> AmountTopup["1.Enter Top-Up Amount"]
AmountTopup --> PayWithMobile["1.Pay with Mobile Money"]
PayWithMobile --> MobileMoneyNumber["1.Enter Phone Number for Mobile Money"]
MobileMoneyNumber --> VerifyNumber["Verify the Phone Number"]
VerifyNumber -- NO --> MobileMoneyNumber
VerifyNumber -- YES --> ConfirmPayment["Confirm Payment"]
ConfirmPayment -- NO --> MobileMoneyNumber
ConfirmPayment -- YES --> SMSU11(["Send SMS"])
SMSU11 --> Back
Balance --> PINU1["1.Enter PIN"]
PINU1 --> VerifyPINU1["Verify PIN"]
VerifyPINU1 -- NO --> PINU1
VerifyPINU1 -- YES --> ShowBalanceU1["Show Balance"]
ShowBalanceU1 --> SMSU1(["Send SMS"])
SMSU1 --> Back
U2 --> PurchaseBag["1.Select Pellet Bag Purchase"] & Accessory["2.Select Accessory"]
PurchaseBag --> P2["1.ZMW = Item<br>"]
P2 --> P3["Enter Quantity"]
PayWithWalletU21["1.Pay with Wallet Account"] --> PINU21["Display Item and Total Price<br>1.Enter PIN"]
PINU21 --> VerifyPINU21["Verify PIN"]
VerifyPINU21 -- NO --> PINU21
VerifyPINU21 -- YES --> ShowBalanceU21["Show Balance"]
ShowBalanceU21 --> SMSU211(["Send SMS"])
SMSU211 --> Back
PayWithMobileU21["2.Pay with Mobile Money"] --> MobileMoneyNumberU21["Enter Phone Number for Mobile Money"]
MobileMoneyNumberU21 --> VerifyNumberU21["Verify the Phone Number"]
VerifyNumberU21 -- NO --> MobileMoneyNumberU21
VerifyNumberU21 -- YES --> ConfirmPaymentU21["Display Item and Total Price<br>Confirm Payment"]
ConfirmPaymentU21 -- NO --> MobileMoneyNumberU21
ConfirmPaymentU21 -- YES --> SMSU212(["Send SMS"])
SMSU212 --> Back
PayWithWalletU22["1.Pay with Wallet Account"] --> PINU22["Display Item and Total Price<br>1.Enter PIN"]
PINU22 --> VerifyPINU22["Verify PIN"]
VerifyPINU22 -- NO --> PINU22
VerifyPINU22 -- YES --> ShowBalanceU22["Show Balance"]
ShowBalanceU22 --> SMSU221(["Send SMS"])
SMSU221 --> Back
PayWithMobileU22["2.Pay with Mobile Money"] --> MobileMoneyNumberU22["Enter Phone Number for Mobile Money"]
MobileMoneyNumberU22 --> VerifyNumberU22["Verify the Phone Number"]
VerifyNumberU22 -- NO --> MobileMoneyNumberU22
VerifyNumberU22 -- YES --> ConfirmPaymentU22["Display Item and Total Price<br>Confirm Payment"]
ConfirmPaymentU22 -- NO --> MobileMoneyNumberU22
ConfirmPaymentU22 -- YES --> SMSU222(["Send SMS"])
SMSU222 --> Back
A2["Enter Quantity"] --> PayWithMobileU22 & PayWithWalletU22
U3 --> ScheduleOrder["1.Schedule an Order"] & TrackDelivery["2.Track Delivery Status"]
ScheduleOrder --> DisplayOrders1["1.Select which order to view"]
DisplayOrders1 --> Pickup["1.Pick up<br>2. Delivery"]
SMSU31(["Send SMS"]) --> Back
Pickup -- 1 --> LeadGenerators["1.Select Lead Generator, Shop, or Reseller"]
Pickup -- 2 --> n10["Enter PIN"]
LeadGenerators --> CollectionPoint["1.Enter Collection ID"]
CollectionPoint --> Confirm["Confirm Collection Point 1.Continue 2.Back"]
Confirm --> CollectionPoint
Confirm -- PIN --> UpdateStatus2["Update Order Status"]
UpdateStatus2 --> SMSU32(["Send SMS"])
SMSU32 --> Back
TrackDelivery --> DisplayOrders2["1.Select which order to view"]
DisplayOrders2 --> SMSU33(["Send SMS"])
SMSU33 --> Back
U4 --> SelectProvince["1.Select Province to Lodge Complaint"] & n17["Track Fault"]
SelectProvince --> Area["2.Type area name"]
Area --> DescribeFault["3.Briefly describe fault"]
DescribeFault --> Ticket["Create New Support Ticket"]
Ticket --> SMSU4(["Send SMS"])
SMSU4 --> Back
U5 -- First check PIN for Wallet --> PINU5["1.Enter PIN"]
PINU5 --> VerifyPINU5["Verify PIN"]
VerifyPINU5 -- NO --> PINU5
VerifyPINU5 -- YES --> DisplayContracts["1.Display Contracts"]
DisplayContracts --> Contract1["1.Stove 1 Contract"] & Contract2["2.Stove 2 Contract"]
DisplayContracts -- YES --> Contract3["3.Stove 3 Contract"] & Contract4["4.Stove 4 Contract"]
Contract1 --> SMSU5(["Send SMS"])
Contract2 --> SMSU5
Contract3 --> SMSU5
Contract4 --> SMSU5
SMSU5 --> Back
PINU6["1.Enter PIN"] --> VerifyPINU6["Verify PIN"]
VerifyPINU6 -- NO --> PINU6
VerifyPINU6 -- YES --> V1["1.Voucher Status"]
V1 --> SMSU6(["Send SMS"])
SMSU6 --> Back
U6 --> Redeem["2.Redeem with Voucher"] & n22["Check Voucher Details"] & PINU6
Redeem --> RE1["1.Redeem Beans"] & RE2["2.Redeem Pellets"] & RE1 & RE2
RE1 --> PayWithVoucher["3.Pay with Voucher"] & PayWithWalletU6["1.Pay with Wallet Account"] & PayWithMobileU6["2.Pay with Mobile Money"]
RE2 --> PayWithVoucher & PayWithWalletU6 & PayWithMobileU6
PayWithWalletU6 --> n15["Select Offer"]
PINU62["1.Enter PIN"] --> VerifyPINU62["Verify PIN"]
VerifyPINU62 -- NO --> PINU62
VerifyPINU62 -- YES --> ShowBalanceU62["Show Balance"]
ShowBalanceU62 --> SMSU62(["Send SMS"])
SMSU62 --> Back
PayWithMobileU6 --> n16["Select Offier"]
MobileMoneyNumberU6["Enter Phone Number for Mobile Money"] --> VerifyNumberU6["Verify the Phone Number"]
VerifyNumberU6 -- NO --> MobileMoneyNumberU6
VerifyNumberU6 -- YES --> ConfirmPaymentU6["Confirm Payment"]
ConfirmPaymentU6 -- NO --> MobileMoneyNumberU6
ConfirmPaymentU6 -- YES --> SMSU6
U7 --> ViewAccount["1.View Account Details"] & ViewContract["2.View Contract Details"] & EditAccount["3.Edit Account Details"] & ResetPIN["4.Reset SupaMoto Wallet PIN"]
ViewAccount --> PINU71["1.Enter PIN"]
PINU71 --> VerifyPINU71["Verify PIN"]
VerifyPINU71 -- NO --> PINU71
VerifyPINU71 -- YES --> ShowAccount["{DATA} 1.Exit"]
ShowAccount --> SMSU71(["Send SMS"])
SMSU71 --> Back
ViewContract --> PINU72["1.Enter PIN"]
PINU72 --> VerifyPINU72["Verify PIN"]
VerifyPINU72 -- NO --> PINU72
VerifyPINU72 -- YES --> ViewConDetails["1.Select which contract to view"]
ViewConDetails --> ShowContracts["{DATA} 1.Exit"]
ShowContracts --> SMSU72(["Send SMS"])
SMSU72 --> Back
EditAccount --> PINU73["1.Enter PIN"]
PINU73 --> VerifyPINU73["Verify PIN"]
VerifyPINU73 -- NO --> PINU73
VerifyPINU73 -- YES --> EditDetails["1.Select what to edit"]
EditDetails --> EA1["1.Edit Phone Number"] & EA2["2.Edit Province"] & EA3["3.Edit District"] & EA4["4.Edit Area"] & EA5["5.Edit Home Address"]
EA1 --> E1["1.Enter New Phone Number"]
EA2 --> E2["1.Enter New Province"]
EA3 --> E3["1.Enter New District"]
EA4 --> E4["1.Enter New Area"]
EA5 --> E5["1.Enter New Home Address"]
E1 --> SMSU73(["Send SMS"])
E2 --> SMSU73
E3 --> SMSU73
E4 --> SMSU73
E5 --> SMSU73
SMSU73 --> Back
ResetPIN --> PINU74["1.Enter PIN"]
PINU74 --> VerifyPINU74["Verify PIN"]
VerifyPINU74 -- NO --> PINU74
VerifyPINU74 -- YES --> EditPIN["1.Select how to edit your PIN"]
EditPIN --> RP1["1.Change SupaMoto Wallet PIN"] & RP2["2.Reset SupaMoto Wallet PIN"]
RP1 --> R1["1.Enter New SupaMoto Wallet PIN"]
R1 --> SMSU74(["Send SMS"])
SMSU74 --> Back
RP2 --> TicketU74["Create New Support Ticket"]
TicketU74 --> SMSU75(["Send SMS"])
SMSU75 --> Back
n4["Verify PIN"] -- YES --> n5["Confirm Voucher Use"]
n5 --> SMSU62
PayWithVoucher --> n14["Select Offer"]
n6["Enter PIN"] --> n4
n4 -- NO --> n6
AgentMenu --> AgentID["1.Enter Agent ID"]
AgentID --> ConfirmAgent["Verify Agent"]
ConfirmAgent --> PINAgent["1.Enter PIN"]
PINAgent --> VerifyPINAgent["Verify PIN"]
VerifyPINAgent -- NO --> PINAgent
VerifyPINAgent -- YES --> CheckStock["Welcome Agent ABC.<br>1.Check Stock<br>2.Manage Orders<br>3.Request OTP"]
CheckStock -- 1 --> SMSAgent(["Send SMS"])
CheckStock -- 2 --> n7["Manage Order<br>1.Approve Order"]
SMSAgent --> Back
P3 --> PayWithWalletU21 & PayWithMobileU21
n7 -- 1 --> n8["Enter Order ID"]
n8 -- Confirmed ID --> n9["Order Details<br><br>1. Approve<br>2.Back"]
n9 --> SMSAgent
n9 -- Back --> n8
Accessory --> A2
n10 --> n11["Display Location<br>1.Confirm Location<br>2.New Location"]
n11 --> n12["Display Order Details<br>1.Confirm Delivery<br>"]
n12 --> n11 & n13(["Send SMS"])
n13 --> Back
n14 --> n6
n15 --> PINU62
n16 --> MobileMoneyNumberU6
n19["Display Ticket Status"] --> SMSU4
n22 --> n23["Enter Voucher Number"]
n23 --> n24["Enter PIN"]
n24 --> n25["Confirm PIN"]
n25 --> n26["Display details"]
n26 --> SMSU62
n17 --> n27["Display Last 5 Tickets"]
n27 --> SMSU33
CheckStock --> n28["Request OTP<br>1.Order Number"]
n28 --> n29["Display Details<br>1.Confirm"]
n29 --> SMSAgent
CollectionPoint@{ shape: rect}
n9@{ shape: rect}
```
