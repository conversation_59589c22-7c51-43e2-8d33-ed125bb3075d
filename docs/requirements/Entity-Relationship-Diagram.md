```mermaid
erDiagram
  Phone {
    string phoneNumber
    string firstSeen
    string lastSeen
    int numberOfVisits
  }
  Customer {
    string customerID
    string encryptedPin
    string dateAdded
    string preferredLanguage
    string lastCompletedAction
  }
  IXO-Profile {
    string did
  }
  Matrix-Vault {
    string username
    string encryptedPassword
  }
  IXO-Account {
    string address
    string encryptedMnemonic
  }
  Household {
    int numberOfMembers
    string contractID
  }
  Phone ||--o{ Customer : "used by"
  Customer ||--|{ Phone : "has"
  Customer ||--o{ IXO-Profile : "has"
  Customer ||--o| Household : "belongs to"
  Household ||--o| IXO-Profile : "has"
  IXO-Profile ||--|| Matrix-Vault : "has"
  IXO-Profile ||--|{ IXO-Account : "has"
```
