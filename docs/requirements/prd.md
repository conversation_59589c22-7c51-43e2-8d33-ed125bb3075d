# SupaMoto USSD Interface - Product Requirements

## Overview

USSD interface for SupaMoto customers to manage energy needs via basic mobile phones. Enables wallet top-ups, fuel pellet purchases, delivery scheduling, and order tracking without smartphones or internet access.

**Target Markets**: Rural Zambia, Malawi, and Mozambique
**Service Code**: \*2233#
**Core Value**: Self-service capabilities for customers with basic phones

## Key Problems Solved

- **Manual Processes**: 65% of call center volume is basic transactions (ordering, balance inquiries)
- **Access Barriers**: 70% of customers use basic phones without internet access
- **Delayed Service**: Up to 48-hour delays for order confirmations
- **Limited Visibility**: No real-time access to wallet balances or order status

## Target Users

**Primary**: Rural customers with basic phones

- Limited formal education, multi-language needs (English, Bemba, Nyanja)
- Monthly income: $60-90 USD
- Use SupaMoto pellets for home and small business cooking

**Secondary**: SupaMoto agents

- Manage distribution points serving 50-75 customers
- Need efficient order processing and payment confirmation

**Key Use Cases**:

- Check wallet balance and transaction history
- Top up wallet via mobile money
- Purchase fuel pellets (5kg, 20kg, 30kg, 50kg bags)
- Schedule deliveries and track order status

## Core Features

### Must Have Features

**1. USSD Menu Navigation**

- Access via \*2233# service code
- Numbered menu options with back/home navigation
- 3-second response time, 2-minute session timeout

**2. Multi-Language Support**

- English, Bemba, Nyanja language options
- Language preference saved for future sessions

**3. Wallet Management**

- Balance inquiry with transaction history
- Mobile money top-up (MTN, Airtel, Zamtel)
- Real-time balance updates (60-second sync)

**4. Fuel Pellet Ordering**

- Purchase 5kg, 20kg, 30kg, 50kg bags
- Price display and quantity selection
- Order confirmation with SMS receipt

**5. PIN Authentication**

- Secure PIN setup and verification
- Account lockout after 3 failed attempts
- PIN reset via agent assistance

### Should Have Features

**6. Delivery Scheduling**

- 7-day delivery window with morning/afternoon slots
- SMS reminder one day before delivery

**7. Order Tracking**

- Order status display (processing, dispatched, delivered)
- Real-time status updates (15-minute sync)

**8. Transaction History**

- Last 5 transactions with date, type, amount
- SMS option for older transaction requests

### Nice to Have Features

**9. Feedback Collection**

- Post-transaction surveys (max 3 questions)
- 1-5 rating scale with SMS text feedback option

### Out of Scope

- Loyalty program integration
- Agent inventory management interfaces
- Stove monitoring integration
- Marketing campaigns and promotions

## Technical Constraints

- **USSD Limitations**: 160 characters per screen, 2-minute session timeout
- **Response Time**: Under 5 seconds for all interactions
- **Mobile Money**: Support MTN, Airtel, Zamtel providers
- **Compatibility**: Must work on all basic feature phones
- **Security**: PIN encryption, audit logs, ISO 27001 compliance

## Business Requirements

- **Launch Date**: Operational before May 2025 peak season
- **ROI Target**: 40% reduction in call center volume
- **Budget**: $75,000 initial development cap
- **Expansion**: Future support for Malawi and Mozambique markets

## UX Design Principles

**Navigation Pattern**:

- Numbered options (1-9) for selections
- "0" to return to previous menu, "#" to main menu, "\*" to exit
- Max 8 options per screen (160-character limit)

**Key User Flows**:

1. **Language Selection**: First-time users choose English/Bemba/Nyanja
2. **Wallet Top-Up**: Provider selection → Amount → PIN → Confirmation
3. **Pellet Purchase**: Size selection → Quantity → PIN → Order confirmation
4. **Order Tracking**: Order list → Select order → Status details

**Design Requirements**:

- Simple language (6th-grade reading level)
- Clear error messages with next steps
- Confirmation required for critical actions
- 90-second session timeout warnings

## Success Metrics

**User Adoption**: 30% of customers registered within 3 months
**Operational Efficiency**: 40% reduction in call center volume
**Customer Satisfaction**: 4.2/5 rating, <5% session abandonment
**Financial Impact**: ROI breakeven within 12 months

## Release Criteria

**Performance**: <5 second response time, 100 concurrent sessions
**Reliability**: 99.5% uptime during business hours
**Security**: Pass penetration testing, banking-grade PIN encryption
**Usability**: 85% task completion without assistance

## MVP Requirements

1. USSD menu navigation system
2. Wallet balance inquiry and mobile money top-up
3. Basic pellet ordering with PIN authentication
4. SMS transaction receipts
5. English language support (minimum)

## Implementation Timeline

**Phase 1 (Mar-May 2025)**: Requirements, architecture, USSD provider setup
**Phase 2 (May-Jun 2025)**: MVP development (navigation, wallet, ordering, PIN auth)
**Phase 3 (Jun-Jul 2025)**: Testing, pilot launch with 50 customers
**Phase 4 (Aug-Sep 2025)**: Full Zambia launch, additional features

**Key Dependencies**:

- USSD short code allocation (4-6 week lead time)
- Mobile money API approvals (3-4 week process)
- ERP system integration access

## Related Documentation

- [USSD Menu Flow Diagram](./USSD-menu-mermaid.md) - Complete menu structure
- [Technical Architecture](../development/) - Implementation guides
