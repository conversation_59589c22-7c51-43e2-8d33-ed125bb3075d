# USSD Testing Strategy Guide

Essential testing patterns for USSD applications built with XState v5 and Node.js/TypeScript.

## Testing Framework Setup

**Vitest** is the recommended testing framework for this project, offering native TypeScript support and fast execution.

```typescript
// vitest.config.ts
import { defineConfig } from "vitest/config";

export default defineConfig({
  test: {
    globals: true,
    environment: "node",
    setupFiles: ["./src/test/setup.ts"],
    testTimeout: 30000, // Extended for USSD session tests
  },
});
```

## Testing Architecture

**Three-tier approach**: unit tests for state machines, integration tests for session management, and end-to-end tests for complete USSD flows.

## XState v5 Testing Patterns

**Test state machines as both pure logic and running actors**. Pure logic testing validates transitions and guards, while actor testing verifies real-world behavior.

```typescript
// XState v5 actor testing example
import { createActor, createMachine } from "xstate";

test("USSD actor behavior", async () => {
  const mockFetchBalance = vi.fn().mockResolvedValue({ balance: 1000 });

  const actor = createActor(
    ussdMachine.provide({
      actors: { fetchBalance: mockFetchBalance },
    })
  );

  actor.start();
  actor.send({ type: "SELECT_BALANCE" });

  await waitFor(() => {
    expect(actor.getSnapshot().value).toBe("showBalance");
  });

  expect(mockFetchBalance).toHaveBeenCalled();
});
```

### Navigation Flow Testing

```typescript
// Test hierarchical USSD navigation
test("USSD navigation with back commands", () => {
  const actor = createActor(ussdMenuMachine);
  actor.start();

  actor.send({ type: "SELECT_1" });
  expect(actor.getSnapshot().value).toEqual({ services: "listServices" });

  actor.send({ type: "BACK" });
  expect(actor.getSnapshot().value).toEqual({ mainMenu: "showing" });
});
```

## Session Testing

### Redis Session Management

Use shortened TTL values in test environments to verify session expiration without waiting.

```typescript
test("session timeout handling", async () => {
  await redisClient.setEx("session:test-123", 2, JSON.stringify(sessionData));
  await new Promise(resolve => setTimeout(resolve, 3000));

  const expired = await redisClient.get("session:test-123");
  expect(expired).toBeNull();
});
```

### Concurrent Sessions

```typescript
test("concurrent USSD sessions", async () => {
  const sessions = Array.from({ length: 100 }, (_, i) => `session-${i}`);

  await Promise.all(
    sessions.map(sessionId =>
      sessionManager.createSession(sessionId, {
        phoneNumber: `+25470000${sessionId}`,
        currentState: "main_menu",
      })
    )
  );

  const allSessions = await Promise.all(
    sessions.map(id => sessionManager.getSession(id))
  );

  expect(allSessions).toHaveLength(100);
});
```

## USSD Webhook Testing

```typescript
test("USSD webhook handling", async () => {
  const webhookPayload = {
    sessionId: "ATUid_test123",
    serviceCode: "*2233#",
    phoneNumber: "+************",
    text: "1",
  };

  const response = await app.inject({
    method: "POST",
    url: "/api/ussd",
    payload: webhookPayload,
  });

  expect(response.statusCode).toBe(200);
  expect(response.payload).toMatch(/^(CON|END)/);
});
```

## End-to-End Testing

### USSD Response Validation

```typescript
function validateUSSDResponse(response: string) {
  expect(response).toMatch(/^(CON|END)\s/);
  expect(response.length).toBeLessThanOrEqual(182);

  if (response.startsWith("CON")) {
    expect(response).toMatch(/\n\d+\./); // Numbered options
  }
}
```

## Security Testing

### PIN Security Testing

```typescript
test("PIN lockout mechanism", async () => {
  const phoneNumber = "+************";

  // Simulate 3 failed attempts
  for (let i = 0; i < 3; i++) {
    const result = await pinValidator.validatePin(
      phoneNumber,
      "wrong",
      hashedPin
    );
    expect(result.attemptsRemaining).toBe(2 - i);
  }

  // Account should be locked
  const result = await pinValidator.validatePin(
    phoneNumber,
    "correct",
    hashedPin
  );
  expect(result.locked).toBe(true);
});
```

## Essential Commands

```bash
# Run all tests
pnpm test

# Run specific test types
pnpm test:unit
pnpm test:integration
pnpm test:interactive

# Database testing
pnpm test:progressive-data    # Test customer creation flow (10 customers)
pnpm test:customer-id         # Test ID generation (collision resistance)

# Test with coverage
pnpm test --coverage

# Run demo files for manual testing
pnpm tsx src/machines/supamoto-wallet/core/welcomeMachine-demo.ts
```

## Database Testing

### Progressive Data Testing

Tests the complete customer creation pipeline:

```bash
# Prerequisites: Docker PostgreSQL running
docker-compose up -d postgres

# Run migration
NODE_OPTIONS='--loader ts-node/esm' pnpm exec ts-node migrations/run-migrations.ts

# Test progressive data creation
pnpm test:progressive-data
```

**What it tests:**

- Phone record creation/updates
- Customer record creation with unique IDs
- Customer-phone relationship linking
- Database integrity and statistics

### Customer ID Testing

Tests the deterministic ID generation system:

```bash
pnpm test:customer-id
```

**What it tests:**

- Rapid ID generation (10 IDs)
- Delayed generation (5 IDs with delays)
- Concurrent generation (20 IDs simultaneously)
- Format validation (C + 8 hex characters)
- Collision resistance analysis

## Key Testing Principles

- **Test state machines as pure logic and running actors**
- **Use shortened TTL values for session timeout testing**
- **Validate USSD responses for CON/END format and character limits**
- **Test PIN security with lockout mechanisms**
- **Use demo files for interactive development and validation**

## Related Documentation

- [XState Implementation Guide](./XState-Implementation-Guide.md) - Comprehensive XState v5 patterns
- [Demo Files Guide](./DEMO_FILES_GUIDE.md) - Interactive development methodology
- [State Machine Patterns](./STATE_MACHINE_PATTERNS.md) - Development workflow standards
