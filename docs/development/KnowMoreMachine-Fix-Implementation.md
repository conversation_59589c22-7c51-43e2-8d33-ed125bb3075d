# KnowMoreMachine Fix Implementation Plan

## 🎯 Objective

Fix the immediate issue where `knowMoreMachine` expects a `START` event but XState v5 invoked machines should start automatically in their functional state.

## 🔍 Current Problem Analysis

### Issue Location

File: `src/machines/supamoto-wallet/information/knowMoreMachine.ts`

### Current Problematic Pattern

```typescript
export const knowMoreMachine = setup({
  // ...
}).createMachine({
  id: "knowMoreMachine",
  initial: "idle", // ❌ Problem: Starts in idle state

  states: {
    idle: {
      on: {
        START: {
          target: "infoMenu", // ❌ Requires manual START event
          actions: "initializeContext",
        },
      },
    },
    infoMenu: {
      // Main functionality here
    },
    // ...
  },
});
```

### Why This Is Wrong

1. **XState v5 Invocation:** When a machine is invoked, it starts automatically
2. **Unnecessary State:** The `idle` state serves no functional purpose
3. **Manual Triggering:** Requires parent to send START event, which never happens
4. **Poor UX:** User sees no response when selecting "Know More" option

## 🛠️ Implementation Steps

### Step 1: Update Initial State

Change the initial state from `idle` to `infoMenu`:

```typescript
export const knowMoreMachine = setup({
  // ... existing setup
}).createMachine({
  id: "knowMoreMachine",
  initial: "infoMenu", // ✅ Start directly in functional state

  context: ({ input }) => ({
    sessionId: input?.sessionId || "",
    phoneNumber: input?.phoneNumber || "",
    serviceCode: input?.serviceCode || "",
    selectedInfo: undefined,
    currentInfoPage: 1,
    totalInfoPages: 1,
    infoContent:
      "Welcome to SupaMoto Information Center\n1. Product Information\n2. Service Information\n3. About SupaMoto\n4. Contact Information",
    error: undefined,
  }),

  states: {
    // Remove idle state entirely
    infoMenu: {
      // Main functionality starts immediately
      on: {
        SELECT_1: {
          target: "displayingInfo",
          actions: ["setSelectedInfo", "loadProductInfo"],
        },
        SELECT_2: {
          target: "displayingInfo",
          actions: ["setSelectedInfo", "loadServiceInfo"],
        },
        SELECT_3: {
          target: "displayingInfo",
          actions: ["setSelectedInfo", "loadAboutInfo"],
        },
        SELECT_4: {
          target: "displayingInfo",
          actions: ["setSelectedInfo", "loadContactInfo"],
        },
        BACK_TO_MAIN: "routeToMain",
      },
    },
    // ... rest of states remain the same
  },
});
```

### Step 2: Remove Idle State and START Event Handling

1. Delete the `idle` state completely
2. Remove all `START` event handlers
3. Move initialization logic from `initializeContext` action to `context` function

### Step 3: Update Context Initialization

Move the initialization logic from actions to the context function:

```typescript
// ❌ Remove this action
actions: {
  initializeContext: assign({
    selectedInfo: undefined,
    currentInfoPage: 1,
    totalInfoPages: 1,
    infoContent: "Welcome to SupaMoto Information Center\n1. Product Information\n2. Service Information\n3. About SupaMoto\n4. Contact Information",
    error: undefined,
  }),
  // ... other actions
}

// ✅ Move to context function
context: ({ input }) => ({
  sessionId: input?.sessionId || "",
  phoneNumber: input?.phoneNumber || "",
  serviceCode: input?.serviceCode || "",
  selectedInfo: undefined,
  currentInfoPage: 1,
  totalInfoPages: 1,
  infoContent: "Welcome to SupaMoto Information Center\n1. Product Information\n2. Service Information\n3. About SupaMoto\n4. Contact Information",
  error: undefined,
}),
```

### Step 4: Update Parent Machine (Optional)

The parent machine (`supamotoWalletMachine.ts`) should work without changes, but we can simplify it:

```typescript
// Current invocation (should work as-is)
knowMoreService: {
  invoke: {
    src: "knowMoreMachine",
    input: ({ context }) => ({
      sessionId: context.sessionId,
      phoneNumber: context.phoneNumber,
      serviceCode: context.serviceCode,
    }),
    onDone: {
      target: "preMenu",
      actions: ["setChildMachineOutput", "clearErrors"],
    },
    onError: {
      target: "error",
      actions: "setError",
    },
  },
},
```

## 🧪 Testing Plan

### Test 1: Standalone Machine Test

```typescript
import { createActor } from "xstate";
import { knowMoreMachine } from "./knowMoreMachine.js";

describe("knowMoreMachine - Fixed Implementation", () => {
  it("should start in infoMenu state immediately", () => {
    const actor = createActor(knowMoreMachine, {
      input: {
        sessionId: "test-123",
        phoneNumber: "+260971234567",
        serviceCode: "*2233#",
      },
    });

    actor.start();
    const snapshot = actor.getSnapshot();

    expect(snapshot.value).toBe("infoMenu");
    expect(snapshot.context.infoContent).toContain("Welcome to SupaMoto");
  });

  it("should not have idle state", () => {
    const actor = createActor(knowMoreMachine, {
      input: {
        sessionId: "test-123",
        phoneNumber: "+260971234567",
        serviceCode: "*2233#",
      },
    });

    actor.start();
    const snapshot = actor.getSnapshot();

    expect(snapshot.value).not.toBe("idle");
  });
});
```

### Test 2: Integration Test with Parent Machine

```typescript
import { createActor } from "xstate";
import { supamotoWalletMachine } from "../supamotoWalletMachine.js";

describe("knowMoreMachine Integration", () => {
  it("should work when invoked by parent machine", async () => {
    const actor = createActor(supamotoWalletMachine, {
      input: {
        sessionId: "test-123",
        phoneNumber: "+260971234567",
        serviceCode: "*2233#",
      },
    });

    actor.start();

    // Navigate to Know More
    actor.send({ type: "INPUT", input: "1" });

    // Wait for invocation to complete
    await new Promise(resolve => setTimeout(resolve, 100));

    const snapshot = actor.getSnapshot();
    expect(snapshot.value).toBe("knowMoreService");

    // Check that child machine is running
    const childActor = snapshot.children.knowMoreMachine;
    expect(childActor).toBeDefined();
    expect(childActor.getSnapshot().value).toBe("infoMenu");
  });
});
```

### Test 3: Interactive Test

Run the interactive test to verify user experience:

```bash
# Start server
pnpm dev

# In another terminal
pnpm test:interactive

# Test flow:
# 1. Dial *2233# (should see main menu)
# 2. Enter "1" (should immediately see Know More menu)
# 3. Enter "1" (should see product information)
```

## 🚀 Expected Results

After implementing this fix:

1. **Immediate Response:** Users selecting "Know More" will immediately see the information menu
2. **Proper State Flow:** Machine starts in functional state, not idle
3. **Better Testing:** Machine can be tested in isolation
4. **Cleaner Code:** Removes unnecessary idle state and START event handling
5. **XState v5 Compliance:** Follows proper invocation patterns

## 📋 Implementation Checklist

- [ ] Update `initial` state from `"idle"` to `"infoMenu"`
- [ ] Remove `idle` state from states object
- [ ] Remove all `START` event handlers
- [ ] Move `initializeContext` logic to `context` function
- [ ] Remove `initializeContext` action
- [ ] Update unit tests
- [ ] Run integration tests
- [ ] Test with interactive script
- [ ] Verify parent machine still works
- [ ] Update any related documentation

## 🔄 Rollback Plan

If issues arise, the rollback is simple:

1. Revert `initial` state to `"idle"`
2. Add back the `idle` state
3. Restore `START` event handling
4. Move context initialization back to actions

However, this fix addresses a fundamental architectural issue and should be permanent once properly tested.

---

_This implementation plan provides a clear path to fix the immediate knowMoreMachine issue while laying groundwork for broader architectural improvements._
