# Environment Configuration Refactoring

## Overview

This document describes the refactoring of environment variable access throughout the codebase to establish a single source of truth for `NODE_ENV` detection and environment-based configuration.

## Problem Statement

### Before Refactoring

- **33+ places** accessing `process.env.NODE_ENV` directly
- **Inconsistent environment detection** across modules
- **Code duplication** in environment checking logic
- **No centralized configuration** for environment-based settings
- **Maintenance burden** when changing environment logic

### Issues Identified

```typescript
// ❌ Multiple inconsistent patterns throughout codebase
const isDev = process.env.NODE_ENV === "development";
const isTest = process.env.NODE_ENV === "test";
const env = process.env.NODE_ENV || "development";
const isProd = process.env.NODE_ENV !== "production";
```

## Solution: Centralized Environment Configuration

### ✅ Single Source of Truth

**Location**: `src/config.ts`

```typescript
// Environment detection and validation
export type Environment = "production" | "development" | "test";

export function getCurrentEnvironment(): Environment {
  const nodeEnv = process.env.NODE_ENV?.toLowerCase();

  switch (nodeEnv) {
    case "production":
    case "prod":
      return "production";
    case "test":
    case "testing":
      return "test";
    case "development":
    case "dev":
    default:
      return "development";
  }
}

export const ENVIRONMENT = getCurrentEnvironment();

export const ENV = {
  CURRENT: ENVIRONMENT,
  IS_PRODUCTION: ENVIRONMENT === "production",
  IS_DEVELOPMENT: ENVIRONMENT === "development",
  IS_TEST: ENVIRONMENT === "test",
  IS_DEV_OR_TEST: ENVIRONMENT !== "production",
} as const;
```

## Refactored Files

### 1. **Core Configuration**

- ✅ `src/config.ts` - Added centralized environment detection
- ✅ `src/config/actors.ts` - Updated to use `ENV.CURRENT`
- ✅ `src/config/index.ts` - Updated guard limits configuration

### 2. **Application Files**

- ✅ `src/app.ts` - Updated Fastify configuration and metrics
- ✅ `src/index.ts` - Updated startup logging

### 3. **Security Plugins**

- ✅ `src/plugins/security.ts` - Updated security configuration
- ✅ `src/plugins/advanced-security.ts` - Updated advanced security

### 4. **Test Setup**

- ✅ `test/setup.ts` - Added documentation for NODE_ENV setting
- ✅ `src/test/helpers/environment-setup.ts` - Updated test environment

## Usage Patterns

### ✅ Recommended Usage

```typescript
import { ENV } from "../config.js";

// Environment checks
if (ENV.IS_PRODUCTION) {
  // Production-only code
}

if (ENV.IS_DEVELOPMENT) {
  // Development-only code
}

if (ENV.IS_TEST) {
  // Test-only code
}

if (ENV.IS_DEV_OR_TEST) {
  // Non-production code
}

// Environment-based configuration
const config = {
  logLevel: ENV.IS_TEST ? "silent" : "info",
  enableMetrics: !ENV.IS_TEST,
  strictSecurity: ENV.IS_PRODUCTION,
};
```

### ❌ Deprecated Patterns

```typescript
// Don't do this anymore
const isDev = process.env.NODE_ENV === "development";
const isTest = process.env.NODE_ENV === "test";
const env = process.env.NODE_ENV || "development";
```

## Benefits Achieved

### 1. **Single Source of Truth**

- ✅ All environment detection happens in one place
- ✅ Consistent behavior across all modules
- ✅ Easy to modify environment logic globally

### 2. **Type Safety**

- ✅ `Environment` type ensures valid values
- ✅ Compile-time checking of environment usage
- ✅ IntelliSense support for environment flags

### 3. **Robust Environment Detection**

- ✅ Handles case variations (`prod`, `dev`, `testing`)
- ✅ Graceful fallback to `development` for invalid values
- ✅ Consistent normalization across all modules

### 4. **Maintainability**

- ✅ Easy to add new environment types
- ✅ Simple to modify environment-based logic
- ✅ Clear documentation of environment usage

### 5. **Testing**

- ✅ Comprehensive test coverage for environment detection
- ✅ Validation of edge cases and invalid inputs
- ✅ Consistent test environment setup

## Migration Guide

### For New Code

```typescript
// ✅ Always import from config
import { ENV } from "../config.js";

// ✅ Use the centralized flags
if (ENV.IS_PRODUCTION) {
  // Your code here
}
```

### For Existing Code

1. **Replace direct NODE_ENV access**:

   ```typescript
   // Before
   const isDev = process.env.NODE_ENV === "development";

   // After
   import { ENV } from "../config.js";
   const isDev = ENV.IS_DEVELOPMENT;
   ```

2. **Use appropriate flags**:

   ```typescript
   // Before
   const isNotProd = process.env.NODE_ENV !== "production";

   // After
   const isNotProd = ENV.IS_DEV_OR_TEST;
   ```

## Validation

### ✅ All Tests Pass

- **112/112 tests** passing
- **TypeScript compilation** clean
- **ESLint** passes without errors
- **Actor system** working correctly

### ✅ Environment Detection Tested

- Handles all valid NODE_ENV values
- Graceful fallback for invalid values
- Consistent flag logic
- Edge case coverage

## Future Considerations

### 1. **Additional Environments**

If new environments are needed (e.g., `staging`):

```typescript
export type Environment = "production" | "staging" | "development" | "test";

// Add to getCurrentEnvironment() switch statement
case "staging":
case "stage":
  return "staging";

// Add to ENV object
IS_STAGING: ENVIRONMENT === "staging",
```

### 2. **Environment-Specific Configurations**

Consider creating environment-specific config files:

```typescript
// src/config/environments/production.ts
// src/config/environments/development.ts
// src/config/environments/test.ts
```

### 3. **Runtime Environment Changes**

For advanced use cases requiring runtime environment changes, consider:

```typescript
export function setEnvironment(env: Environment): void {
  // Update global environment state
  // Trigger configuration reloads
}
```

## Summary

✅ **Centralized**: Single source of truth in `src/config.ts`  
✅ **Consistent**: All modules use the same environment detection  
✅ **Type-Safe**: Full TypeScript support with proper types  
✅ **Robust**: Handles edge cases and invalid inputs gracefully  
✅ **Maintainable**: Easy to modify and extend environment logic  
✅ **Tested**: Comprehensive validation of all functionality

The codebase now has a clean, maintainable approach to environment configuration that eliminates duplication and ensures consistency across all modules.
