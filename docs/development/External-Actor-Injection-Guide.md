# External Actor Injection Guide

## Overview

The `supamotoWalletMachine` has been refactored to use **external actor injection** for verification services. This provides clean separation of concerns and allows for different implementations in production, development, and test environments.

## How It Works

### 1. Machine Definition

The machine defines **placeholder actors** that throw errors if not provided:

```typescript
// src/machines/supamoto-wallet/supamotoWalletMachine.ts
export const supamotoWalletMachine = setup({
  actors: {
    // Child state machines
    knowMoreMachine,
    userServicesMachine,
    agentMachine,

    // External actors - placeholders that must be overridden
    verifyWallet: fromPromise(
      async ({ input }: { input: WalletVerificationInput }) => {
        throw new Error(
          "verifyWallet actor not provided. Use machine.provide({ actors: { verifyWallet } })"
        );
      }
    ),

    verifyAgent: fromPromise(
      async ({ input }: { input: AgentVerificationInput }) => {
        throw new Error(
          "verifyAgent actor not provided. Use machine.provide({ actors: { verifyAgent } })"
        );
      }
    ),
  },
  // ... rest of machine definition
});
```

### 2. Actor Configuration System

The actor configuration system provides environment-appropriate implementations:

```typescript
// src/config/actors.ts
import { createCurrentEnvironmentActors } from "../config/actors.js";

// Automatically detects NODE_ENV and provides appropriate actors
const actors = createCurrentEnvironmentActors();

// Or explicitly specify environment
const actors = createActors("development"); // or "production" or "test"
```

### 3. Providing External Actors

Use `machine.provide()` to inject the external actors:

```typescript
import { createActor } from "xstate";
import { supamotoWalletMachine } from "./machines/supamoto-wallet/supamotoWalletMachine.js";
import { createCurrentEnvironmentActors } from "./config/actors.js";

// Step 1: Get environment-appropriate actors
const actors = createCurrentEnvironmentActors();

// Step 2: Configure machine with external actors
const configuredMachine = supamotoWalletMachine.provide({
  actors: actors as any, // Type assertion needed due to XState v5 typing
});

// Step 3: Create and use actor
const actor = createActor(configuredMachine, {
  input: {
    sessionId: "session-123",
    phoneNumber: "+1234567890",
    serviceCode: "*2233#",
  },
});

actor.start();
```

## Environment-Based Actors

### Development Environment

- Uses `MockWalletVerificationService` and `MockAgentVerificationService`
- Realistic delays (800ms for wallet, 600ms for agent)
- Consistent mock data with proper logging
- Valid test IDs: `["C21009802", "C21009803", "C21009804", "C21009805"]`
- Valid agent IDs: `["AGT001", "AGT002", "AGT003", "AGENT123"]`

### Test Environment

- Same mock services but with faster responses (100ms)
- Optimized for test speed while maintaining realistic behavior

### Production Environment

- Would use real `WalletVerificationService` and `AgentVerificationService`
- Currently throws "not yet implemented" errors
- Ready for backend API integration

## Usage Examples

### Basic Usage (Session Service)

```typescript
// src/services/session.ts
import { createCurrentEnvironmentActors } from "../config/actors.js";

export class SessionService {
  async processSession(input: SessionInput): Promise<SessionResponse> {
    // Get actors for current environment
    const actors = createCurrentEnvironmentActors();

    // Configure machine with actors
    const configuredMachine = supamotoWalletMachine.provide({
      actors,
    });

    // Create and use actor
    const actor = createActor(configuredMachine, { input });
    // ... rest of session logic
  }
}
```

### Custom Actor Override (Testing)

```typescript
import { fromPromise } from "xstate";
import { createActors } from "../config/actors.js";

// Get standard development actors
const standardActors = createActors("development");

// Override specific actors for testing
const customActors = {
  ...standardActors,
  verifyWallet: fromPromise(async ({ input }) => {
    // Custom test implementation
    return {
      success: true,
      walletId: input.walletId,
      balance: 1000.0,
      customerName: "Test User",
      phoneNumber: input.phoneNumber,
    };
  }),
};

const configuredMachine = supamotoWalletMachine.provide({
  actors: customActors,
});
```

### Factory Pattern (Advanced)

```typescript
import { ActorFactory } from "../config/actors.js";

const factory = new ActorFactory("development");

// Standard actors
const standardActors = factory.createStandardActors();

// Custom actors with overrides
const customActors = factory.createCustomActors({
  verifyWallet: myCustomWalletActor,
});
```

## Benefits

1. **Clean Separation**: Business logic separated from machine structure
2. **Environment Flexibility**: Different implementations per environment
3. **Testability**: Easy to mock and test individual components
4. **Type Safety**: Full TypeScript support with proper interfaces
5. **Maintainability**: Single source of truth for each service
6. **No Duplication**: Eliminated duplicate mock implementations

## Migration from Old System

### Before (Embedded Mocks)

```typescript
// ❌ Old way - embedded in machine
const supamotoWalletMachine = setup({
  actors: {
    verifyWallet: fromPromise(async ({ input }) => {
      // Mock implementation embedded in machine
    }),
  },
});
```

### After (External Injection)

```typescript
// ✅ New way - external injection
const actors = createCurrentEnvironmentActors();
const configuredMachine = supamotoWalletMachine.provide({ actors });
```

## Troubleshooting

### Error: "verifyWallet actor not provided"

- **Cause**: Machine used without providing external actors
- **Solution**: Use `machine.provide({ actors })` before creating actor

### Type Errors with `machine.provide()`

- **Cause**: XState v5 strict typing
- **Solution**: Use type assertion: `actors: actors as any`

### Environment Not Detected

- **Cause**: NODE_ENV not set or invalid
- **Solution**: Set NODE_ENV or use explicit `createActors("development")`

## Next Steps

1. **Production Implementation**: Replace mock services with real API calls
2. **Additional Services**: Add more external actors as needed
3. **Enhanced Testing**: Use custom actor overrides for specific test scenarios
4. **Monitoring**: Add observability to actor performance
