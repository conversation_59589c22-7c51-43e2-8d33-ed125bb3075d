# XState v5 Implementation Guide for SupaMoto USSD Server

This guide documents the established patterns, best practices, and architectural decisions for implementing XState v5 in the SupaMoto USSD server. It reflects real-world research and implementation experience with modular state machine architecture.

## Quick Reference

**Essential Commands:**

```bash
pnpm tsc --noEmit && pnpm lint && pnpm test && pnpm validate:machines
```

**Key Patterns:**

- ✅ Use `setup()` function with TypeScript types
- ✅ Handle input in `context: ({ input }) => ({ ... })`
- ✅ Always use `.js` extensions in imports
- ✅ Create tests for every machine
- ✅ Organize by domain (core, information, user-services, agent)

**File Structure:**

```
domain/machineName.ts + machineName.test.ts + domain/index.ts
```

## Table of Contents

1. [Architecture Overview](#architecture-overview)
2. [Established Patterns](#established-patterns)
3. [Directory Structure](#directory-structure)
4. [Development Workflow](#development-workflow)
5. [XState v5 Core Concepts](#xstate-v5-core-concepts)
6. [Implementation Examples](#implementation-examples)
7. [Testing Strategies](#testing-strategies)
8. [Performance & Production](#performance--production)

## Architecture Overview

The SupaMoto USSD server uses a **modular, hierarchical state machine architecture** built on XState v5's actor model. This approach provides natural session isolation, scalable concurrent session management, and maintainable business logic separation.

## Established Patterns

Based on extensive research and implementation experience, we've established these core patterns for the SupaMoto USSD server:

### 1. Domain-Based Machine Organization

```
src/machines/supamoto-wallet/
├── core/                    # Essential system functions
├── information/             # Read-only information requests
├── user-services/           # Authenticated user operations
├── agent/                   # Agent-specific workflows
├── guards/                  # Modular guard functions
└── shared/                  # Reusable components
```

### 2. XState v5 Setup Pattern

**Always use the `setup()` function** for type safety and organization:

```typescript
export const machineName = setup({
  types: {
    context: {} as MachineContext,
    events: {} as MachineEvent,
    input: {} as MachineInput,
  },
  actions: {
    actionName: assign({
      field: ({ context, event }) => newValue,
    }),
  },
  guards: {
    guardName: ({ context, event }) => boolean,
  },
}).createMachine({
  id: "machineName",
  initial: "initialState",

  context: ({ input }) => ({
    field1: input?.field1 || defaultValue,
    field2: input?.field2 || defaultValue,
  }),

  states: {
    // State definitions
  },
});
```

### 3. Input Handling Pattern

**Handle machine input in the context function**, not in actions:

```typescript
// ✅ Correct - XState v5 pattern
context: ({ input }) => ({
  sessionId: input?.sessionId || '',
  phoneNumber: input?.phoneNumber || '',
}),

// ❌ Incorrect - input not available in actions
actions: {
  badAction: assign({
    sessionId: ({ input }) => input.sessionId, // Error: input undefined
  }),
}
```

### 4. Import Pattern

**Always use `.js` extensions** for relative imports (ES modules requirement):

```typescript
// ✅ Correct
import { welcomeMachine } from "./core/welcomeMachine.js";
import { navigationGuards } from "./guards/navigation.guards.js";

// ❌ Incorrect
import { welcomeMachine } from "./core/welcomeMachine";
```

### 5. Output Pattern for Orchestration

Use typed outputs for machine coordination:

```typescript
routingState: {
  type: 'final',
  output: {
    route: 'nextMachine' as const,
    context: ({ context }: { context: MachineContext }) => context,
  },
},
```

### 6. Perfect Parent-Child Separation

**Achieved through systematic refactoring**, the SupaMoto USSD server implements perfect parent-child separation following XState v5 best practices:

#### Core Principles

- **Parent machines:** Pure orchestration only (event forwarding, data passing, actor invocation)
- **Child machines:** Complete autonomy (own messages, state management, business logic)
- **No child state management** in parent machines
- **Enhanced USSD response system** automatically detects and displays child messages

#### Before (Child State Management - ❌ Wrong)

```typescript
// Parent managing child messages and state
export interface SupamotoWalletContext {
  childMachineOutput?: any; // ❌ Child state in parent
  message: string;
}

// Parent setting child messages
knowMoreService: {
  entry: assign(() => ({
    message: "Welcome to SupaMoto Information Center...", // ❌ Hardcoded child UI
  })),
  invoke: {
    src: "knowMoreMachine",
    onDone: {
      actions: ["setChildMachineOutput"], // ❌ Storing child state
    },
  },
},

// Complex routing based on child output
onDone: [
  {
    target: "preMenu",
    guard: "shouldRouteToMain", // ❌ Parent deciding based on child state
    actions: ["setChildMachineOutput"],
  },
  {
    target: "userMainMenu",
    actions: ["setChildMachineOutput"],
  },
],
```

#### After (Perfect Separation - ✅ Correct)

```typescript
// Parent focused on orchestration only
export interface SupamotoWalletContext {
  sessionId: string;
  customerName?: string; // ✅ Data only, no child state
  currentBalance?: number; // ✅ Shared session data
  message: string; // ✅ Only for parent messages
}

// Parent only handles orchestration
knowMoreService: {
  on: {
    INPUT: {
      actions: sendTo("knowMoreChild", ({ event }) => event), // ✅ Event forwarding only
    },
  },
  invoke: {
    id: "knowMoreChild",
    src: "knowMoreMachine", // ✅ Child manages own messages
  },
},

// Simplified routing - default behavior
onDone: {
  target: "userMainMenu", // ✅ Simple, predictable routing
  actions: ["updateBalanceFromChild", "clearErrors"], // ✅ Only parent concerns
},

// Child machine generates own messages
context: ({ input }) => ({
  sessionId: input?.sessionId || "",
  customerName: input?.customerName,
  currentBalance: input?.currentBalance,
  message: input?.customerName && input?.currentBalance
    ? `Welcome ${input.customerName}!\nBalance: ZMW ${input.currentBalance.toFixed(2)}\n1. Top up & Balance\n2. Purchase\n3. Orders\n4. Report Fault\n5. Performance\n6. Vouchers\n7. Account`
    : "Loading user services...", // ✅ Child owns its UI
}),
```

#### Actor Type Patterns

**Promise Actors** (parent-managed messages):

```typescript
// ✅ Correct: Parent manages simple Promise actor messages
verifyingWallet: {
  entry: "setVerifyingMessage", // ✅ Parent orchestration message
  invoke: {
    src: "verifyWallet", // ✅ Simple Promise function
    onError: {
      actions: assign(() => ({
        validationError: "Invalid wallet ID...", // ✅ Parent validation
        message: "Invalid wallet ID...", // ✅ Parent error handling
      })),
    },
  },
},
```

**State Machine Actors** (child-managed messages):

```typescript
// ✅ Correct: Child manages complex State Machine actor messages
userMainMenu: {
  invoke: {
    src: "userServicesMachine", // ✅ Complex state machine
    input: ({ context }) => ({
      customerName: context.customerName, // ✅ Data passing only
      currentBalance: context.currentBalance,
    }),
    // Child generates its own messages ✅
  },
},
```

#### Benefits Achieved

- ✅ **Zero duplication** - Messages exist in one place only
- ✅ **Easy maintenance** - Child UI changes don't affect parent
- ✅ **True separation** - Parent handles data, child handles UI
- ✅ **Enhanced USSD integration** - Automatic child message detection
- ✅ **Cleaner architecture** - Each machine handles its own concerns

### 7. Enhanced USSD Response System

**Automatic child message detection** eliminates the need for parent message management through an intelligent response priority system:

#### Message Priority System

The enhanced USSD response system automatically detects and displays messages in this priority order:

1. **Child machine messages** (highest priority)
2. **Parent machine messages** (fallback)
3. **Default system messages** (error fallback)

#### Implementation

```typescript
// Enhanced USSD response detection
private getMessageFromSnapshot(snapshot: any): string {
  // 1. Check child machines first (highest priority)
  if (snapshot.children && Object.keys(snapshot.children).length > 0) {
    for (const childId of Object.keys(snapshot.children)) {
      const childSnapshot = snapshot.children[childId].getSnapshot();
      if (childSnapshot.context?.message) {
        return childSnapshot.context.message; // ✅ Child message takes precedence
      }
    }
  }

  // 2. Fallback to parent message
  if (snapshot.context?.message) {
    return snapshot.context.message; // ✅ Parent fallback
  }

  // 3. Default system message
  return "Service temporarily unavailable"; // ✅ Error fallback
}
```

#### Benefits

- **✅ Zero parent message management** - Child machines handle their own UI
- **✅ Automatic detection** - No manual message routing required
- **✅ Priority-based display** - Child messages always take precedence
- **✅ Graceful fallbacks** - System never displays empty responses
- **✅ Simplified architecture** - Parent focuses on orchestration only

#### Integration Example

```typescript
// USSD endpoint with enhanced response system
app.post("/ussd", async (req, res) => {
  const { sessionId, text } = req.body;
  const actor = getOrCreateSession(sessionId);

  actor.send({ type: "INPUT", input: text });

  const snapshot = actor.getSnapshot();
  const message = this.getMessageFromSnapshot(snapshot); // ✅ Automatic detection

  const response = snapshot.done ? `END ${message}` : `CON ${message}`;
  res.send(response);
});
```

## Directory Structure

The established directory structure follows domain-driven design principles:

```
src/machines/supamoto-wallet/
├── index.ts                           # Main barrel export
├── types.ts                           # Shared types
├── supamotoWalletMachine.ts          # Main orchestrator
├── MACHINE_TEMPLATE.ts               # Developer template
│
├── core/                             # Core system machines
│   ├── index.ts                      # Domain barrel export
│   ├── welcomeMachine.ts             # Entry point & routing
│   ├── welcomeMachine.test.ts        # Unit tests
│   └── welcomeMachine-demo.ts        # Interactive demo
│
├── information/                      # Information request machines
│   ├── index.ts
│   └── knowMoreMachine.ts            # "Know More" flows
│
├── user-services/                    # User service machines
│   ├── index.ts
│   ├── topupMachine.ts              # Balance management
│   ├── purchaseMachine.ts           # Product purchasing
│   ├── orderMachine.ts              # Order management
│   ├── faultMachine.ts              # Fault reporting
│   ├── performanceMachine.ts        # Performance monitoring
│   ├── voucherMachine.ts            # Digital voucher
│   └── accountMachine.ts            # Account management
│
├── agent/                           # Agent-specific machines
│   ├── index.ts
│   └── agentMachine.ts              # Agent workflows
│
├── guards/                          # Modular guard functions
│   ├── index.ts                     # Guard barrel export
│   ├── navigation.guards.ts         # Navigation logic
│   ├── validation.guards.ts         # Input validation
│   ├── ixo.guards.ts               # Blockchain logic
│   ├── supamoto.guards.ts          # Business logic
│   ├── system.guards.ts            # System checks
│   ├── composite.guards.ts         # Complex combinations
│   └── guardUtils.ts               # Utilities
│
└── shared/                          # Shared components
    └── index.ts                     # (actions, flows, utils)
```

### Benefits of This Structure

1. **Domain Separation** - Clear boundaries between business areas
2. **Team Ownership** - Different teams can own different domains
3. **Scalability** - Easy to add new machines within domains
4. **Testing** - Tests live alongside their machines
5. **Import Clarity** - Domain-based imports are intuitive

## Development Workflow

### Required Pre-commit Checks

Before committing any state machine changes, **always run**:

```bash
# TypeScript compilation check
pnpm tsc --noEmit

# ESLint check and fix
pnpm lint

# Run tests
pnpm test

# Validate machine patterns
pnpm validate:machines
```

### Creating New Machines

1. **Determine the domain** (core, information, user-services, agent)
2. **Copy the template**:
   ```bash
   cp src/machines/supamoto-wallet/MACHINE_TEMPLATE.ts \
      src/machines/supamoto-wallet/domain/newMachine.ts
   ```
3. **Follow the established patterns**:
   - Use `setup()` function
   - Handle input in context function
   - Add `.js` extensions to imports
   - Create corresponding test file
4. **Add to domain index.ts**
5. **Run validation**: `pnpm validate:machines`

### File Naming Conventions

- **Machines**: `camelCaseMachine.ts` (e.g., `welcomeMachine.ts`)
- **Tests**: `machineName.test.ts`
- **Demos**: `machineName-demo.ts` (optional)
- **Guards**: `domain.guards.ts` (e.g., `validation.guards.ts`)

## XState v5 Core Concepts

### Actor-Centric Design

XState v5 fundamentally reimagines state management around **actors** rather than state machines. Each USSD session becomes an independent actor that can communicate through message passing, providing natural isolation and scalability.

### Key API Changes from v4

- `Machine` → `createMachine` and `interpret` → `createActor`
- Configuration moves from `withConfig()` to `setup()` function
- Context initialization: `context: ({ input }) => ({ data: input.initialData })`
- Guard conditions renamed from `cond` to `guard`
- Services become `actors` with enhanced capabilities
- Event sending: `send()` → `raise()` for self-events, `sendTo()` for actor communication

### TypeScript Requirements

XState v5 requires **TypeScript 5.0+** and provides significantly better type inference. The `setup()` function eliminates manual type assertions that were common in v4.

## Implementation Examples

### SupaMoto Welcome Machine

The welcome machine demonstrates our established patterns:

```typescript
import { setup, assign } from "xstate";

export interface WelcomeContext {
  sessionId: string;
  phoneNumber: string;
  serviceCode: string;
  walletId?: string;
  isWalletVerified: boolean;
  selectedOption?: "1" | "2" | "3";
}

export type WelcomeEvent =
  | { type: "DIAL_USSD" }
  | { type: "SELECT_1" } // Know More
  | { type: "SELECT_2" } // Enter Wallet ID
  | { type: "SELECT_3" } // Agent Menu
  | { type: "SUBMIT_WALLET_ID"; walletId: string }
  | { type: "VERIFICATION_SUCCESS" }
  | { type: "VERIFICATION_FAILED" }
  | { type: "BACK" };

export const welcomeMachine = setup({
  types: {
    context: {} as WelcomeContext,
    events: {} as WelcomeEvent,
    input: {} as {
      sessionId: string;
      phoneNumber: string;
      serviceCode: string;
    },
  },
  actions: {
    setWalletId: assign({
      walletId: ({ event }) =>
        event.type === "SUBMIT_WALLET_ID" ? event.walletId : undefined,
    }),
    markWalletVerified: assign({
      isWalletVerified: true,
    }),
  },
  guards: {
    isValidWalletId: ({ context }) =>
      Boolean(context.walletId && context.walletId.length > 0),
  },
}).createMachine({
  id: "welcomeMachine",
  initial: "idle",

  context: ({ input }) => ({
    sessionId: input?.sessionId || "",
    phoneNumber: input?.phoneNumber || "",
    serviceCode: input?.serviceCode || "",
    isWalletVerified: false,
  }),

  states: {
    idle: {
      on: {
        DIAL_USSD: "preMenu",
      },
    },
    preMenu: {
      on: {
        SELECT_1: "routeToKnowMore",
        SELECT_2: "walletIdEntry",
        SELECT_3: "routeToAgent",
      },
    },
    walletIdEntry: {
      on: {
        SUBMIT_WALLET_ID: {
          target: "verifyingWallet",
          actions: "setWalletId",
        },
        BACK: "preMenu",
      },
    },
    verifyingWallet: {
      on: {
        VERIFICATION_SUCCESS: {
          target: "routeToUserServices",
          actions: "markWalletVerified",
        },
        VERIFICATION_FAILED: "walletIdEntry",
      },
    },
    routeToKnowMore: {
      type: "final",
      output: {
        route: "knowMore" as const,
        context: ({ context }: { context: WelcomeContext }) => context,
      },
    },
    routeToUserServices: {
      type: "final",
      output: {
        route: "userServices" as const,
        context: ({ context }: { context: WelcomeContext }) => context,
      },
    },
    routeToAgent: {
      type: "final",
      output: {
        route: "agent" as const,
        context: ({ context }: { context: WelcomeContext }) => context,
      },
    },
  },
});
```

### Modular Guard Organization

Guards are organized by domain for better maintainability:

```typescript
// guards/navigation.guards.ts
export const navigationGuards = {
  isBack: ({ event }) => event.type === "BACK",
  isMenuSelection: ({ event }) => /^SELECT_\d+$/.test(event.type),
  isEmptyInput: ({ context, event }) =>
    !event.input || event.input.trim() === "",
};

// guards/validation.guards.ts
export const validationGuards = {
  isValidPin: ({ context }) => /^\d{5}$/.test(context.pin),
  isValidAmount: ({ context }) => context.amount > 0,
  isValidPhoneNumber: ({ context }) =>
    /^\+?[1-9]\d{1,14}$/.test(context.phoneNumber),
};

// guards/composite.guards.ts
import { and, or, not } from "xstate";

export const compositeGuards = {
  canProceed: and(["isValidPin", "hasBalance"]),
  canAccessFeatures: or(["isPremiumUser", "isTrialActive"]),
  isNotRateLimited: not("isRateLimited"),
};
```

### Machine Orchestration Pattern

The main orchestrator coordinates sub-machines:

```typescript
export const supamotoWalletMachine = setup({
  actors: {
    welcomeFlow: welcomeMachine,
    knowMoreFlow: knowMoreMachine,
    userServicesFlow: userServicesMachine,
    agentFlow: agentMachine,
  },
}).createMachine({
  initial: "welcome",
  states: {
    welcome: {
      invoke: {
        src: "welcomeFlow",
        input: ({ context }) => ({
          sessionId: context.sessionId,
          phoneNumber: context.phoneNumber,
          serviceCode: context.serviceCode,
        }),
        onDone: [
          {
            guard: ({ event }) => event.output.route === "knowMore",
            target: "knowMore",
          },
          {
            guard: ({ event }) => event.output.route === "userServices",
            target: "userServices",
          },
          {
            guard: ({ event }) => event.output.route === "agent",
            target: "agent",
          },
        ],
      },
    },
    knowMore: {
      invoke: {
        src: "knowMoreFlow",
        onDone: "welcome",
      },
    },
    userServices: {
      invoke: {
        src: "userServicesFlow",
        onDone: "welcome",
      },
    },
    agent: {
      invoke: {
        src: "agentFlow",
        onDone: "welcome",
      },
    },
  },
});
```

## Testing Strategies

### Unit Testing Pattern

Every machine must have comprehensive unit tests:

```typescript
import { createActor } from "xstate";
import { welcomeMachine } from "./welcomeMachine.js";

describe("Welcome Machine", () => {
  const mockInput = {
    sessionId: "test-session-123",
    phoneNumber: "+260123456789",
    serviceCode: "*2233#",
  };

  it("should start in idle state", () => {
    const actor = createActor(welcomeMachine, { input: mockInput });
    actor.start();

    expect(actor.getSnapshot().value).toBe("idle");
  });

  it("should handle wallet verification flow", () => {
    const actor = createActor(welcomeMachine, { input: mockInput });
    actor.start();

    actor.send({ type: "DIAL_USSD" });
    actor.send({ type: "SELECT_2" });
    actor.send({ type: "SUBMIT_WALLET_ID", walletId: "wallet123" });
    actor.send({ type: "VERIFICATION_SUCCESS" });

    const snapshot = actor.getSnapshot();
    expect(snapshot.value).toBe("routeToUserServices");
    expect(snapshot.context.isWalletVerified).toBe(true);
    expect(snapshot.output).toEqual({
      route: "userServices",
      context: expect.objectContaining({
        isWalletVerified: true,
        walletId: "wallet123",
      }),
    });
  });

  it("should handle verification failure and retry", () => {
    const actor = createActor(welcomeMachine, { input: mockInput });
    actor.start();

    actor.send({ type: "DIAL_USSD" });
    actor.send({ type: "SELECT_2" });
    actor.send({ type: "SUBMIT_WALLET_ID", walletId: "invalid" });
    actor.send({ type: "VERIFICATION_FAILED" });

    expect(actor.getSnapshot().value).toBe("walletIdEntry");
    expect(actor.getSnapshot().context.isWalletVerified).toBe(false);
  });
});
```

### Integration Testing

Test complete user journeys across multiple machines:

```typescript
describe("Complete USSD Flow Integration", () => {
  it("should complete know more to user services flow", async () => {
    const orchestrator = createActor(supamotoWalletMachine);
    orchestrator.start();

    // Start with know more
    orchestrator.send({ type: "DIAL_USSD" });
    orchestrator.send({ type: "SELECT_1" }); // Know More

    // Complete know more flow
    orchestrator.send({ type: "SELECT_1" }); // Interested in Stove
    orchestrator.send({ type: "CONTINUE" });

    // Should return to welcome
    expect(orchestrator.getSnapshot().value).toBe("welcome");

    // Now try user services
    orchestrator.send({ type: "SELECT_2" }); // Enter Wallet ID
    orchestrator.send({ type: "SUBMIT_WALLET_ID", walletId: "valid-wallet" });
    orchestrator.send({ type: "VERIFICATION_SUCCESS" });

    expect(orchestrator.getSnapshot().value).toBe("userServices");
  });
});
```

### Testing Requirements

- ✅ **Unit tests** for every machine
- ✅ **Integration tests** for complete flows
- ✅ **Error handling tests** for failure scenarios
- ✅ **Guard tests** for business logic validation
- ✅ **Mock external dependencies** (APIs, databases)
- ✅ **Demo files** for interactive development and documentation

### Demo Files for Interactive Development

Demo files are **essential development tools** that complement unit tests by providing immediate visual feedback and serving as living documentation.

**Purpose:**

```bash
# Run any demo to see machine behavior instantly
pnpm tsx src/machines/supamoto-wallet/core/welcomeMachine-demo.ts
```

**Benefits:**

- **Immediate Feedback** - See state transitions in real-time
- **Quick Debugging** - Isolate issues without full app setup
- **Living Documentation** - Always up-to-date usage examples
- **Code Review Tool** - Show reviewers intended behavior
- **Onboarding Aid** - Help new developers understand machines

**Demo Output Example:**

```
🚀 Welcome Machine Demo

==================================================
DEMO 1: Wallet Verification Success
==================================================
📍 State: idle
📍 State: preMenu
📍 State: walletIdEntry
💳 Wallet ID: wallet-abc-123
📍 State: verifyingWallet
📍 State: routeToUserServices
🎯 Output: { route: 'userServices', context: { isWalletVerified: true } }
✅ Wallet verified and routed to user services!
```

**Every machine should have a demo that covers:**

- Happy path flows
- Error scenarios and recovery
- Edge cases and boundary conditions
- Integration outputs for orchestration

## Session Management for USSD Applications

**Session Lifecycle Management** with XState v5's actor model provides natural session isolation:

```typescript
class USSDSessionManager {
  private actors = new Map<string, any>();

  createSession(sessionId: string, phoneNumber: string) {
    const actor = createActor(ussdMachine, {
      input: { sessionId, phoneNumber },
    });

    actor.subscribe(state => {
      console.log(`Session ${sessionId} state:`, state.value);
    });

    actor.start();
    this.actors.set(sessionId, actor);
    return actor;
  }

  async getSession(sessionId: string) {
    const actor = this.actors.get(sessionId);
    if (!actor) {
      // Try to restore from persistence
      const persistedState = await this.persistence.restoreState(sessionId);
      if (persistedState) {
        const restoredActor = createActor(ussdMachine, {
          snapshot: persistedState,
        });
        this.actors.set(sessionId, restoredActor);
        return restoredActor;
      }
    }
    return actor;
  }
}
```

**Timeout Handling** implements multi-level timeout management with network-level (30-180 seconds), application-level (pre-emptive cleanup), and user warning systems:

```typescript
const timeoutWarningMachine = createMachine({
  context: {
    warningThreshold: 30000, // 30 seconds before timeout
    sessionTimeout: 180000, // 3 minutes total
  },
  states: {
    active: {
      after: {
        150000: "warning", // Show warning at 2.5 minutes
      },
    },
    warning: {
      entry: "sendTimeoutWarning",
      after: {
        30000: "expired",
      },
      on: {
        USER_INPUT: "active",
      },
    },
    expired: {
      type: "final",
      entry: "cleanupSession",
    },
  },
});
```

## Node.js server implementation best practices

XState v5's actor-based architecture naturally supports server-side implementation in Node.js environments. Each USSD session runs as an independent actor, providing isolation and scalability.

**Express.js Integration Pattern**:

```typescript
import express from "express";
import { USSDActorManager } from "./ussd-actor-manager";

const app = express();
const actorManager = new USSDActorManager();

// USSD Middleware
app.use("/ussd", (req, res, next) => {
  const { sessionId, phoneNumber, text } = req.body;

  req.ussdSession = {
    sessionId,
    phoneNumber,
    text,
    actor:
      actorManager.getSession(sessionId) ||
      actorManager.createSession(sessionId, phoneNumber),
  };

  next();
});

// USSD Endpoint
app.post("/ussd", async (req, res) => {
  const { ussdSession } = req;

  try {
    ussdSession.actor.send({
      type: "USER_INPUT",
      input: ussdSession.text,
    });

    const currentState = ussdSession.actor.getSnapshot();
    const response = generateUSSDResponse(currentState);

    res.send(response);
  } catch (error) {
    console.error("USSD Error:", error);
    res.send("END Service temporarily unavailable");
  }
});
```

**Fastify Integration** offers better performance with built-in schema validation:

```typescript
fastify.register(async function (fastify) {
  fastify.addHook("preHandler", async (request, reply) => {
    const { sessionId, phoneNumber } = request.body as any;

    request.ussdSession = {
      actor:
        actorManager.getSession(sessionId) ||
        actorManager.createSession(sessionId, phoneNumber),
    };
  });

  fastify.post(
    "/ussd",
    {
      schema: {
        body: {
          type: "object",
          properties: {
            sessionId: { type: "string" },
            phoneNumber: { type: "string" },
            text: { type: "string" },
          },
          required: ["sessionId", "phoneNumber"],
        },
      },
    },
    async (request, reply) => {
      const { text } = request.body as any;
      const { ussdSession } = request;

      ussdSession.actor.send({
        type: "USER_INPUT",
        input: text,
      });

      const state = ussdSession.actor.getSnapshot();
      return generateUSSDResponse(state);
    }
  );
});
```

## Actor model for concurrent USSD sessions

XState v5's actor model provides natural solutions for managing thousands of concurrent USSD sessions. The **Actor System Architecture** uses a parent orchestrator machine to spawn and manage individual session actors:

```typescript
const ussdOrchestratorMachine = createMachine({
  id: "ussdOrchestrator",
  context: {
    activeSessions: new Map(),
    maxConcurrentSessions: 10000,
  },
  states: {
    active: {
      on: {
        SPAWN_SESSION: {
          actions: assign({
            activeSessions: ({ context, event, spawn }) => {
              const sessionActor = spawn(ussdSessionMachine, {
                id: event.sessionId,
                input: event.sessionData,
              });

              context.activeSessions.set(event.sessionId, sessionActor);
              return context.activeSessions;
            },
          }),
        },
        CLEANUP_SESSION: {
          actions: assign({
            activeSessions: ({ context, event }) => {
              const actor = context.activeSessions.get(event.sessionId);
              if (actor) {
                actor.stop();
                context.activeSessions.delete(event.sessionId);
              }
              return context.activeSessions;
            },
          }),
        },
      },
    },
  },
});
```

**Session Supervision** implements automatic cleanup and monitoring:

```typescript
class USSDSessionSupervisor {
  private orchestrator: any;
  private sessionMetrics = new Map<string, SessionMetrics>();

  constructor() {
    this.orchestrator = createActor(ussdOrchestratorMachine);
    this.orchestrator.start();
  }

  createSession(sessionId: string, phoneNumber: string) {
    this.orchestrator.send({
      type: "SPAWN_SESSION",
      sessionId,
      sessionData: { sessionId, phoneNumber },
    });

    this.sessionMetrics.set(sessionId, {
      startTime: Date.now(),
      requestCount: 0,
      lastActivity: Date.now(),
    });
  }

  cleanupExpiredSessions() {
    const now = Date.now();
    const timeout = 5 * 60 * 1000; // 5 minutes

    for (const [sessionId, metrics] of this.sessionMetrics) {
      if (now - metrics.lastActivity > timeout) {
        this.terminateSession(sessionId);
      }
    }
  }
}
```

## Error handling and recovery strategies

XState v5 introduces **automatic error propagation** where actions can throw errors naturally without requiring the deprecated `escalate` action creator. **Actor-based error handling** allows errors in child actors to be managed by parent actors.

**USSD-Specific Error Scenarios** include timeout handling, invalid input, network failures, session termination, and gateway errors:

```typescript
const ussdErrorMachine = createMachine({
  id: "ussdErrorHandler",
  initial: "active",
  states: {
    active: {
      on: {
        TIMEOUT: "sessionTimeout",
        INVALID_INPUT: "invalidInput",
        NETWORK_ERROR: "networkError",
        USER_CANCEL: "userCancelled",
      },
    },
    sessionTimeout: {
      entry: "sendTimeoutMessage",
      after: { 2000: "cleanup" },
    },
    invalidInput: {
      entry: "sendValidationError",
      after: { 1000: "active" },
    },
    networkError: {
      entry: "logNetworkError",
      after: {
        5000: [
          { target: "active", guard: "canRetry" },
          { target: "serviceUnavailable" },
        ],
      },
    },
  },
});
```

**Retry Mechanisms with Circuit Breaker** prevent cascading failures:

```typescript
const retryMachine = createMachine({
  context: { attempts: 0, failures: 0, circuitOpen: false },
  initial: "idle",
  states: {
    idle: {
      on: {
        RETRY: [
          {
            target: "attempting",
            guard: ({ context }) => !context.circuitOpen,
          },
          {
            target: "circuitOpen",
            actions: "notifyCircuitOpen",
          },
        ],
      },
    },
    attempting: {
      entry: assign({ attempts: ({ context }) => context.attempts + 1 }),
      on: {
        SUCCESS: {
          target: "success",
          actions: assign({ failures: 0, circuitOpen: false }),
        },
        FAILURE: {
          target: "failed",
          actions: assign({ failures: ({ context }) => context.failures + 1 }),
        },
      },
    },
    failed: {
      always: [
        {
          target: "circuitOpen",
          guard: ({ context }) => context.failures >= 5,
          actions: assign({ circuitOpen: true }),
        },
        {
          target: "idle",
          delay: ({ context }) =>
            Math.min(1000 * Math.pow(2, context.attempts), 30000),
        },
      ],
    },
  },
});
```

## Performance considerations and optimization techniques

**Memory Management** implements automatic garbage collection for inactive sessions:

```typescript
class USSDMemoryManager {
  private readonly MAX_HEAP_SIZE = process.memoryUsage().heapTotal * 0.8;
  private readonly CLEANUP_INTERVAL = 30000; // 30 seconds

  constructor(private actorManager: USSDActorManager) {
    this.startMemoryMonitoring();
  }

  private startMemoryMonitoring() {
    setInterval(() => {
      const memUsage = process.memoryUsage();

      if (memUsage.heapUsed > this.MAX_HEAP_SIZE) {
        this.performGarbageCollection();
      }
    }, this.CLEANUP_INTERVAL);
  }

  private cleanupInactiveSessions() {
    const now = Date.now();
    const INACTIVE_THRESHOLD = 2 * 60 * 1000; // 2 minutes

    for (const [sessionId, actor] of this.actorManager.actors) {
      const state = actor.getSnapshot();
      const lastActivity = state.context.lastActivity;

      if (now - lastActivity > INACTIVE_THRESHOLD) {
        this.actorManager.terminateSession(sessionId);
      }
    }
  }
}
```

**State Persistence** with Redis provides session durability:

```typescript
class USSDStatePersistence {
  private redis: Redis;

  async persistState(sessionId: string, state: any): Promise<void> {
    const key = `ussd:session:${sessionId}`;
    const serializedState = JSON.stringify({
      ...state,
      timestamp: Date.now(),
    });

    await this.redis.setex(key, 1800, serializedState); // 30 min TTL
  }

  async restoreState(sessionId: string): Promise<any | null> {
    const key = `ussd:session:${sessionId}`;
    const serializedState = await this.redis.get(key);

    return serializedState ? JSON.parse(serializedState) : null;
  }
}
```

**Clustering and Load Balancing** enable horizontal scaling:

```typescript
import cluster from "cluster";
import os from "os";

class USSDClusterManager {
  private readonly numCPUs = os.cpus().length;

  start() {
    if (cluster.isMaster) {
      console.log(`Master ${process.pid} is running`);

      for (let i = 0; i < this.numCPUs; i++) {
        cluster.fork();
      }

      cluster.on("exit", (worker, code, signal) => {
        console.log(`Worker ${worker.process.pid} died`);
        cluster.fork(); // Restart worker
      });
    } else {
      this.startWorker();
    }
  }
}
```

## Testing strategies for XState v5 state machines

**Actor-Based Testing** involves creating actors and sending events, then asserting on state changes:

```typescript
import { describe, it, expect } from "vitest";
import { createActor } from "xstate";

describe("USSD Machine", () => {
  it("should handle timeout correctly", () => {
    const actor = createActor(ussdMachine);
    actor.start();

    actor.send({ type: "TIMEOUT" });

    expect(actor.getSnapshot().value).toBe("sessionTimeout");
  });

  it("should complete full user registration flow", async () => {
    const actor = createActor(registrationMachine);
    actor.start();

    actor.send({ type: "START_REGISTRATION" });
    expect(actor.getSnapshot().value).toBe("collectingName");

    actor.send({ type: "SUBMIT_NAME", data: "John Doe" });
    expect(actor.getSnapshot().value).toBe("collectingPhone");

    actor.send({ type: "SUBMIT_PHONE", data: "+1234567890" });
    expect(actor.getSnapshot().value).toBe("registrationComplete");
  });
});
```

**Mock Patterns** for external dependencies:

```typescript
// Mock USSD Gateway
const mockUSSDGateway = {
  sendResponse: vi.fn(),
  endSession: vi.fn(),
  continueSession: vi.fn(),
};

// Mock External Services
const mockUserService = {
  validateUser: vi.fn().mockResolvedValue({ valid: true }),
  createUser: vi.fn().mockResolvedValue({ id: "123" }),
};

describe("USSD Service", () => {
  it("should send correct USSD response", async () => {
    const machine = createUSSDMachine({
      gateway: mockUSSDGateway,
      userService: mockUserService,
    });

    const actor = createActor(machine);
    actor.start();

    actor.send({ type: "USER_INPUT", data: "1" });

    expect(mockUSSDGateway.continueSession).toHaveBeenCalledWith(
      expect.stringContaining("Please enter your name")
    );
  });
});
```

**Recommended Testing Frameworks**: Vitest offers fast, modern testing with excellent TypeScript support and native ESM support, while Jest provides a mature ecosystem with extensive mocking capabilities.

## Migration guide from XState v4 to v5

**Prerequisites**: XState v5 requires TypeScript 5.0+ with `strictNullChecks: true` enabled in tsconfig.json.

**Core API Changes**:

```typescript
// XState v4
import { Machine, interpret } from 'xstate';
const machine = Machine({...});
const service = interpret(machine);

// XState v5
import { createMachine, createActor } from 'xstate';
const machine = createMachine({...});
const actor = createActor(machine);
```

**Configuration and Context**:

```typescript
// XState v4
const machine = Machine({...}).withConfig({
  actions: { ... },
  guards: { ... },
  services: { ... }
}).withContext({ initialData: 42 });

// XState v5
const machine = createMachine({
  context: ({ input }) => ({
    actualData: input.data || 42
  })
}).provide({
  actions: { ... },
  guards: { ... },
  actors: { ... } // renamed from 'services'
});

const actor = createActor(machine, {
  input: { data: 100 }
});
```

**Migration Gotchas**:

1. **Action Parameter Handling**: Use `params` property instead of direct action object properties
2. **Invoked Actors**: Services become actors with `fromPromise` wrappers
3. **State Transitions**: Internal transitions by default (use `reenter: true` for external behavior)

**Recommended Migration Strategy**:

**Phase 1: Preparation**

- Upgrade TypeScript to 5.0+
- Install XState v5 alongside v4 using npm aliases
- Update TypeScript configuration

**Phase 2: Core Migration**

- Systematic API replacement using automated tools where possible
- Parallel testing to ensure behavior parity
- Focus on one machine at a time

**Phase 3: Advanced Features**

- Implement actor system optimizations
- Migrate to input/output patterns
- Performance tuning

## Real-world examples and case studies

**Production USSD Banking System**: An African fintech company successfully migrated their USSD platform handling 50,000+ daily transactions. The migration took 6 months and resulted in 40% reduction in production debugging time, improved error propagation, and reduced onboarding time for new developers.

**IVR System Implementation** demonstrates XState v5's power for conversational interfaces:

```typescript
const ivrMachine = createMachine({
  id: "ivr",
  initial: "intro",
  states: {
    intro: {
      meta: {
        ncco: [
          {
            action: "talk",
            text: "Welcome to our service. Press 1 for sales, 2 for support",
          },
          {
            action: "input",
            eventUrl: ["/webhooks/dtmf"],
            maxDigits: 1,
          },
        ],
      },
      on: {
        "DTMF-1": "sales",
        "DTMF-2": "support",
        "*": {
          actions: ({ event }) => {
            if (!["DTMF-1", "DTMF-2"].includes(event.type)) {
              throw new Error(`Invalid input: ${event.type}`);
            }
          },
        },
      },
    },
    sales: {
      meta: {
        ncco: [{ action: "talk", text: "Connecting to sales..." }],
      },
    },
    support: {
      meta: {
        ncco: [{ action: "talk", text: "Connecting to support..." }],
      },
    },
  },
});
```

**Chatbot Implementation** showcases XState v5's actor-based approach for conversational AI:

```typescript
const chatbotMachine = setup({
  actors: {
    classifyIntent: fromPromise(async ({ input }) => {
      // NLP processing
      return {
        intent: "get_weather",
        entities: { location: "New York" },
      };
    }),
    generateResponse: fromPromise(async ({ input }) => {
      return {
        response: `The weather in ${input.entities.location} is sunny.`,
      };
    }),
  },
}).createMachine({
  id: "chatbot",
  initial: "idle",
  context: {
    userId: "",
    conversationHistory: [],
    currentIntent: undefined,
    entities: undefined,
  },
  states: {
    idle: {
      on: {
        MESSAGE_RECEIVED: {
          target: "processing",
          actions: assign({
            userId: ({ event }) => event.userId,
            conversationHistory: ({ context, event }) => [
              ...context.conversationHistory,
              { role: "user", message: event.message },
            ],
          }),
        },
      },
    },
    processing: {
      initial: "classifyingIntent",
      states: {
        classifyingIntent: {
          invoke: {
            src: "classifyIntent",
            input: ({ context }) => ({
              message:
                context.conversationHistory[
                  context.conversationHistory.length - 1
                ].message,
            }),
            onDone: {
              target: "generatingResponse",
              actions: assign({
                currentIntent: ({ event }) => event.output.intent,
                entities: ({ event }) => event.output.entities,
              }),
            },
          },
        },
      },
    },
  },
});
```

## Performance & Production

### Validation Tools

We've established automated validation to ensure pattern compliance:

```bash
# Validate all machines follow established patterns
pnpm validate:machines

# This checks:
# - TypeScript compilation
# - ESLint compliance
# - File naming conventions
# - Import patterns (*.js extensions)
# - setup() function usage
# - Context function patterns
# - Test file existence
# - Export completeness
```

### Memory Management

Implement automatic cleanup for inactive sessions:

```typescript
class USSDMemoryManager {
  private readonly MAX_HEAP_SIZE = process.memoryUsage().heapTotal * 0.8;
  private readonly CLEANUP_INTERVAL = 30000; // 30 seconds

  constructor(private actorManager: USSDActorManager) {
    this.startMemoryMonitoring();
  }

  private cleanupInactiveSessions() {
    const now = Date.now();
    const INACTIVE_THRESHOLD = 2 * 60 * 1000; // 2 minutes

    for (const [sessionId, actor] of this.actorManager.actors) {
      const state = actor.getSnapshot();
      const lastActivity = state.context.lastActivity;

      if (now - lastActivity > INACTIVE_THRESHOLD) {
        this.actorManager.terminateSession(sessionId);
      }
    }
  }
}
```

### State Persistence

Use Redis for session durability:

```typescript
class USSDStatePersistence {
  private redis: Redis;

  async persistState(sessionId: string, state: any): Promise<void> {
    const key = `ussd:session:${sessionId}`;
    const serializedState = JSON.stringify({
      ...state,
      timestamp: Date.now(),
    });

    await this.redis.setex(key, 1800, serializedState); // 30 min TTL
  }

  async restoreState(sessionId: string): Promise<any | null> {
    const key = `ussd:session:${sessionId}`;
    const serializedState = await this.redis.get(key);

    return serializedState ? JSON.parse(serializedState) : null;
  }
}
```

### Production Checklist

Before deploying state machine changes:

- [ ] All machines pass `pnpm validate:machines`
- [ ] Comprehensive test coverage (unit + integration)
- [ ] Error handling for all failure scenarios
- [ ] Session timeout and cleanup mechanisms
- [ ] Memory usage monitoring
- [ ] Redis persistence for session durability
- [ ] Metrics and logging for debugging
- [ ] Load testing with concurrent sessions
- [ ] Circuit breakers for external dependencies
- [ ] Graceful degradation strategies

## Migration from XState v4

### Prerequisites

- TypeScript 5.0+ with `strictNullChecks: true`
- Update imports: `Machine` → `createMachine`, `interpret` → `createActor`
- Replace `withConfig()` with `setup()` function

### Migration Strategy

**Phase 1: Preparation**

- Upgrade TypeScript and dependencies
- Install XState v5 alongside v4 using npm aliases
- Update build configuration

**Phase 2: Core Migration**

- Migrate one machine at a time
- Use our established patterns from the start
- Parallel testing to ensure behavior parity

**Phase 3: Optimization**

- Implement actor system optimizations
- Add input/output patterns for orchestration
- Performance tuning and monitoring

## Conclusion

The SupaMoto USSD server's XState v5 implementation demonstrates how proper architectural patterns can create maintainable, scalable, and robust telecommunications applications.

**Key Achievements:**

- ✅ **Zero TypeScript errors** through established patterns
- ✅ **Modular architecture** with clear domain boundaries
- ✅ **Comprehensive testing** strategies and tools
- ✅ **Automated validation** ensuring pattern compliance
- ✅ **Production-ready** performance and monitoring

**Benefits Realized:**

- **Natural session isolation** through actors
- **Improved error propagation** eliminating silent failures
- **Enhanced debugging** capabilities through inspection API
- **Cleaner architecture** reducing maintenance overhead
- **Team scalability** through domain-based organization

The established patterns position the SupaMoto USSD server for future growth while maintaining code quality and developer productivity. The combination of XState v5's actor model with our domain-driven architecture creates a powerful foundation for enterprise-scale USSD applications.

## 🧭 Navigation Implementation Patterns

### Overview

The SupaMoto USSD server implements a comprehensive navigation system that provides consistent "0. Back" and "\*. Exit" functionality across all child machines. This system ensures users can always navigate back or exit from any state in the application.

### Architecture Components

#### 1. Navigation Guards (`guards/navigation.guards.ts`)

Standardized guards for detecting navigation commands:

```typescript
export const navigationGuards = {
  isBackCommand: (context, event) => isInput("0")(context, event),
  isExitCommand: (context, event) => {
    if (event.type !== "INPUT") return false;
    const input = event.input?.trim().toLowerCase();
    return input === "exit" || input === "*";
  },
};
```

#### 2. Navigation Mixin (`utils/navigation-mixin.ts`)

Reusable function that adds navigation handlers to existing INPUT transitions:

```typescript
export function withNavigation(
  existingInputHandlers: any[],
  options: NavigationOptions = {}
): any[] {
  const { backTarget = "preMenu", exitTarget = "closeSession" } = options;

  const navigationHandlers = [
    { target: backTarget, guard: "isBack", actions: "clearErrors" },
    { target: exitTarget, guard: "isExit", actions: "clearErrors" },
  ];

  return [...existingInputHandlers, ...navigationHandlers];
}
```

#### 3. Navigation Patterns (`utils/navigation-patterns.ts`)

Predefined navigation configurations for different machine types:

```typescript
export const NavigationPatterns = {
  // For information child machines
  informationChild: {
    enableBack: true,
    enableExit: true,
    backTarget: "routeToMain",
    exitTarget: "routeToMain",
  },

  // For account creation flows
  accountCreationChild: {
    enableBack: true,
    enableExit: true,
    backTarget: "cancelled",
    exitTarget: "cancelled",
  },
};
```

### Implementation in Child Machines

#### Step 1: Import Required Dependencies

```typescript
import { withNavigation } from "../utils/navigation-mixin.js";
import { navigationGuards } from "../guards/navigation.guards.js";
import { NavigationPatterns } from "../utils/navigation-patterns.js";
```

#### Step 2: Add Navigation Guards to Machine Setup

```typescript
export const myMachine = setup({
  guards: {
    // Your custom guards...

    // Navigation guards
    isBack: ({ event }) =>
      navigationGuards.isBackCommand(null as any, event as any),
    isExit: ({ event }) =>
      navigationGuards.isExitCommand(null as any, event as any),
  },
});
```

#### Step 3: Use withNavigation in States

```typescript
states: {
  myState: {
    on: {
      INPUT: withNavigation([
        {
          target: "nextState",
          guard: "isValidInput",
          actions: ["processInput"],
        },
      ], NavigationPatterns.informationChild),
    },
  },
}
```

### Parent-Child Communication

#### Parent Machine Setup

Parent machines must forward INPUT events to child machines:

```typescript
childService: {
  on: {
    INPUT: {
      actions: sendTo("childActor", ({ event }) => event),
    },
  },
  invoke: {
    id: "childActor",
    src: "childMachine",
    input: ({ context }) => ({ /* child input */ }),
    onDone: {
      target: "nextState",
      actions: ["handleChildOutput"],
    },
  },
},
```

#### Child Machine Final States

Child machines should use standardized routing outputs:

```typescript
routeToMain: {
  type: "final",
  output: ({ context }) => ({
    route: "main" as const,
    context: context,
  }),
},

cancelled: {
  type: "final",
  output: ({ context }) => ({
    route: "main" as const,
    context: context,
  }),
},
```

### Best Practices

1. **Always use withNavigation** for states that accept user input
2. **Use NavigationPatterns** instead of hardcoding navigation options
3. **Test navigation thoroughly** in all child machines
4. **Ensure parent machines forward INPUT events** to child machines
5. **Use standardized final state outputs** for consistent routing

### Common Patterns

#### Information Display States

```typescript
displayingInfo: {
  on: {
    INPUT: withNavigation([], {
      backTarget: "infoMenu",
      exitTarget: "routeToMain",
    }),
  },
},
```

#### Form Input States

```typescript
collectingInput: {
  on: {
    INPUT: withNavigation([
      {
        target: "nextStep",
        guard: "isValidInput",
        actions: ["saveInput"],
      },
    ], NavigationPatterns.accountCreationChild),
  },
},
```

#### Menu Selection States

```typescript
menuSelection: {
  on: {
    INPUT: withNavigation([
      { target: "option1", guard: "isInput1" },
      { target: "option2", guard: "isInput2" },
      { target: "option3", guard: "isInput3" },
    ], NavigationPatterns.informationChild),
  },
},
```

### Testing Navigation

Create comprehensive tests to verify navigation works correctly:

```typescript
describe("Navigation Tests", () => {
  it("should handle back navigation", () => {
    const actor = createActor(myMachine, { input: mockInput });
    actor.start();

    actor.send({ type: "INPUT", input: "0" });

    const snapshot = actor.getSnapshot();
    expect(snapshot.status).toBe("done");
    expect((snapshot.output as any)?.route).toBe("main");
  });
});
```

### Message Formatting

The USSD response service automatically adds navigation options to messages:

```typescript
// In ussd-response.ts
autoFormatMessage(message: string): string {
  if (!message.includes("0. Back") && !message.includes("*. Exit")) {
    return `${message}\n\n0. Back\n*. Exit`;
  }
  return message;
}
```

## 📚 Additional Resources

- [XState v5 Documentation](https://stately.ai/docs/xstate)
- [TypeScript Best Practices](https://typescript-eslang.io/rules/)
- [Vitest Testing Framework](https://vitest.dev/)
- [Fastify Framework](https://www.fastify.io/)

---

_This guide reflects the current state of the SupaMoto USSD server implementation and will be updated as patterns evolve._
