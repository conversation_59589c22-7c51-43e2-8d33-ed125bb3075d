# XState v5 Architecture Guide: Modular Machine Composition

## Overview

This guide addresses architectural issues identified in our ixo-ussd-server XState v5 implementation and provides research-backed solutions for creating truly modular, autonomous state machines that follow XState v5 best practices.

## 🚨 Identified Issues

### 1. Child Machine Initialization Pattern Flaw

**Problem:** The `knowMoreMachine` expects a `START` event to transition from `idle` to `infoMenu`, but XState v5 invoked machines automatically start and should begin in their intended initial state.

**Current Implementation:**

```typescript
// ❌ Problematic pattern
states: {
  idle: {
    on: {
      START: {
        target: "infoMenu",
        actions: "initializeContext",
      },
    },
  },
  infoMenu: {
    // Main functionality here
  }
}
```

**Issue:** This creates an unnecessary intermediate state that requires manual triggering, contradicting XState v5's automatic invocation lifecycle.

### 2. Parent Machine Bloat

**Problem:** The `supamotoWalletMachine.ts` is becoming increasingly complex as we add more child machine invocations, violating the principle of autonomous modular machines.

**Current Bloat Indicators:**

- 621 lines in main orchestrator
- Multiple child machine configurations
- Extensive input/output mapping
- Complex guard logic duplication

### 3. Non-Autonomous Child Machines

**Problem:** Child machines require extensive parent machine configuration and aren't truly autonomous, making them difficult to test and reuse.

## 🔬 XState v5 Research Findings

### Machine Invocation Lifecycle

According to XState v5 documentation:

1. **Automatic Start:** Invoked actors start automatically when the state is entered
2. **Input Handling:** Use `context: ({ input }) => ({ ... })` for initialization
3. **No Manual Triggers:** Child machines should not require START events
4. **Clean Separation:** Parent machines should only handle orchestration, not child machine details

### Best Practices for Machine Composition

#### 1. Autonomous Child Machines

- Each child machine should be fully functional in isolation
- Initial state should be the actual working state, not an idle state
- Context initialization should happen via input, not actions

#### 2. Minimal Parent Configuration

- Parent machines should only handle routing and high-level orchestration
- Avoid duplicating child machine logic in parent
- Use standardized input/output patterns

#### 3. Proper Setup Usage

- Use `setup({ ... })` for type safety and reusability
- Define actors, actions, and guards in setup for better organization
- Leverage TypeScript inference for robust typing

## 🛠️ Architectural Solutions

### Solution 1: Fix Child Machine Initialization

**Before (Problematic):**

```typescript
export const knowMoreMachine = setup({
  // ...
}).createMachine({
  initial: "idle", // ❌ Unnecessary intermediate state
  states: {
    idle: {
      on: {
        START: { target: "infoMenu", actions: "initializeContext" },
      },
    },
    infoMenu: {
      // Main functionality
    },
  },
});
```

**After (Correct XState v5 Pattern):**

```typescript
export const knowMoreMachine = setup({
  // ...
}).createMachine({
  initial: "infoMenu", // ✅ Start directly in working state
  context: ({ input }) => ({
    // ✅ Initialize via input, not actions
    sessionId: input?.sessionId || "",
    phoneNumber: input?.phoneNumber || "",
    serviceCode: input?.serviceCode || "",
    selectedInfo: undefined,
    currentInfoPage: 1,
    totalInfoPages: 1,
    infoContent:
      "Welcome to SupaMoto Information Center\n1. Product Information\n2. Service Information\n3. About SupaMoto\n4. Contact Information",
    error: undefined,
  }),
  states: {
    infoMenu: {
      // Main functionality starts immediately
      on: {
        SELECT_1: { target: "displayingInfo", actions: "loadProductInfo" },
        // ...
      },
    },
    // ...
  },
});
```

### Solution 2: Reduce Parent Machine Complexity

**Strategy: Standardized Child Machine Interface**

Create a standard interface for all child machines:

```typescript
// Standard child machine input/output pattern
interface StandardChildInput {
  sessionId: string;
  phoneNumber: string;
  serviceCode: string;
}

interface StandardChildOutput {
  route: "main" | "back" | "exit";
  context?: any;
  message?: string;
}
```

**Simplified Parent Machine:**

```typescript
export const supamotoWalletMachine = setup({
  types: {
    context: {} as SupamotoWalletContext,
    events: {} as SupamotoWalletEvent,
  },
  actors: {
    knowMoreMachine,
    userServicesMachine,
    agentMachine,
  },
}).createMachine({
  // Simplified orchestration logic
  states: {
    preMenu: {
      on: {
        SELECT_1: "knowMoreService",
        SELECT_2: "walletIdEntry",
        SELECT_3: "agentIdEntry",
      },
    },
    knowMoreService: {
      invoke: {
        src: "knowMoreMachine",
        input: ({ context }) => ({
          sessionId: context.sessionId,
          phoneNumber: context.phoneNumber,
          serviceCode: context.serviceCode,
        }),
        onDone: "preMenu", // Simple routing
      },
    },
    // ...
  },
});
```

### Solution 3: Autonomous Machine Testing

Each child machine should be testable in isolation:

```typescript
// Example: knowMoreMachine standalone test
describe("knowMoreMachine", () => {
  it("should start in infoMenu state when invoked", () => {
    const actor = createActor(knowMoreMachine, {
      input: {
        sessionId: "test-123",
        phoneNumber: "+************",
        serviceCode: "*2233#",
      },
    });

    actor.start();

    expect(actor.getSnapshot().value).toBe("infoMenu");
    expect(actor.getSnapshot().context.sessionId).toBe("test-123");
  });
});
```

## 🎭 Actor Type Patterns and Message Responsibility

**Established through systematic refactoring**, the SupaMoto USSD server implements clear patterns for different actor types with distinct message management responsibilities:

### Promise Actors vs State Machine Actors

The architecture distinguishes between two fundamental actor types, each with different message management patterns:

#### Promise Actors (Parent-Managed Messages)

**Use Case:** Simple business logic functions (API calls, validation, data processing)

**Examples:** `verifyWallet`, `verifyAgent`

**Message Responsibility:** **Parent machine** manages all messages

```typescript
// ✅ Correct: Parent manages Promise actor messages
verifyingWallet: {
  entry: assign(() => ({
    message: "Verifying... Please wait.", // ✅ Parent orchestration message
  })),
  invoke: {
    src: "verifyWallet", // ✅ Simple Promise function
    onDone: {
      target: "userMainMenu",
      actions: assign(({ event }) => ({
        isAuthenticated: true,
        customerName: event.output.customerName, // ✅ Parent stores data
        currentBalance: event.output.balance,
      })),
    },
    onError: {
      target: "walletIdEntry",
      actions: assign(() => ({
        validationError: "Invalid wallet ID...", // ✅ Parent validation
        message: "Invalid wallet ID...", // ✅ Parent error handling
      })),
    },
  },
},
```

**Why Parent-Managed:**

- Promise actors are **simple functions** without UI concerns
- Parent is **orchestrating the business process**
- Messages are **status updates** during parent operations
- No complex workflow or state management needed

#### State Machine Actors (Child-Managed Messages)

**Use Case:** Complex workflows with their own UI and business logic

**Examples:** `knowMoreMachine`, `userServicesMachine`, `agentMachine`

**Message Responsibility:** **Child machine** manages all messages

```typescript
// ✅ Correct: Child manages State Machine actor messages
userMainMenu: {
  invoke: {
    id: "userServicesChild",
    src: "userServicesMachine", // ✅ Complex state machine
    input: ({ context }) => ({
      sessionId: context.sessionId,
      customerName: context.customerName, // ✅ Data passing only
      currentBalance: context.currentBalance,
    }),
    // Child generates its own messages ✅
  },
},

// Child machine context initialization
context: ({ input }) => ({
  sessionId: input?.sessionId || "",
  customerName: input?.customerName,
  currentBalance: input?.currentBalance,
  message: input?.customerName && input?.currentBalance
    ? `Welcome ${input.customerName}!\nBalance: ZMW ${input.currentBalance.toFixed(2)}\n1. Top up & Balance\n2. Purchase\n3. Orders\n4. Report Fault\n5. Performance\n6. Vouchers\n7. Account`
    : "Loading user services...", // ✅ Child owns its UI
}),
```

**Why Child-Managed:**

- State machines have **complex workflows** with multiple states
- Child machines contain **domain-specific business logic**
- Messages are **workflow-specific** and change based on child state
- Child machines need **complete autonomy** for testing and reuse

### Separation of Concerns and Responsibility Boundaries

#### Parent Machine Responsibilities

- ✅ **Data orchestration** - Passing data between actors
- ✅ **Event forwarding** - Routing user input to appropriate actors
- ✅ **Actor lifecycle** - Starting, stopping, and managing actors
- ✅ **Simple validation** - Basic input validation for routing decisions
- ✅ **Promise actor messages** - Status updates for simple operations

#### Child Machine Responsibilities

- ✅ **Own message generation** - All UI content and user interactions
- ✅ **Business logic** - Domain-specific workflows and processing
- ✅ **State management** - Internal state transitions and data
- ✅ **Complex validation** - Domain-specific input validation
- ✅ **Error handling** - Workflow-specific error states and recovery

### Benefits of Clear Separation

- **✅ Zero duplication** - Messages exist in one place only
- **✅ Easy maintenance** - Changes don't cascade between machines
- **✅ Clear testing** - Each machine can be tested independently
- **✅ Type safety** - Clear interfaces between parent and child
- **✅ Reusability** - Child machines work in different contexts
- **✅ Scalability** - New actors follow established patterns

## 📋 Migration Strategy

### Phase 1: Fix Child Machine Initialization

1. Update `knowMoreMachine` to start in `infoMenu` state
2. Move initialization logic from actions to context function
3. Remove unnecessary `START` event handling
4. Test child machine in isolation

### Phase 2: Standardize Child Machine Interfaces

1. Define standard input/output types
2. Update all child machines to use standard interface
3. Simplify parent machine invocation logic
4. Update tests to use new patterns

### Phase 3: Reduce Parent Machine Complexity

1. Extract complex logic to child machines
2. Simplify parent machine to pure orchestration
3. Remove duplicated guard logic
4. Implement standardized error handling

## 🎯 Expected Outcomes

After implementing these solutions:

1. **Reduced Complexity:** Parent machine focuses only on orchestration
2. **Autonomous Children:** Each child machine works independently
3. **Better Testing:** Child machines can be tested in isolation
4. **Type Safety:** Improved TypeScript inference and type checking
5. **Maintainability:** Clearer separation of concerns
6. **Reusability:** Child machines can be reused in different contexts

## ⚠️ Common Anti-Patterns to Avoid

### 1. Idle State Anti-Pattern

```typescript
// ❌ DON'T: Create unnecessary idle states
states: {
  idle: {
    on: { START: "working" }
  },
  working: {
    // Actual functionality
  }
}

// ✅ DO: Start directly in working state
states: {
  working: {
    // Functionality available immediately
  }
}
```

### 2. Manual Initialization Anti-Pattern

```typescript
// ❌ DON'T: Use actions for initialization
context: { data: null },
states: {
  loading: {
    entry: "initializeData", // Manual initialization
  }
}

// ✅ DO: Use input for initialization
context: ({ input }) => ({
  data: input.initialData || null,
}),
```

### 3. Parent Machine Bloat Anti-Pattern

```typescript
// ❌ DON'T: Handle child machine details in parent
states: {
  childService: {
    invoke: {
      src: "childMachine",
      input: ({ context }) => ({
        // Extensive configuration
        setting1: context.setting1,
        setting2: context.setting2,
        // ... many more settings
      }),
      onDone: [
        {
          target: "state1",
          guard: ({ event }) => event.output.result === "case1",
          actions: ["handleCase1", "updateContext1", "logCase1"],
        },
        {
          target: "state2",
          guard: ({ event }) => event.output.result === "case2",
          actions: ["handleCase2", "updateContext2", "logCase2"],
        },
        // ... many more cases
      ],
    },
  },
}

// ✅ DO: Keep parent simple, let child handle complexity
states: {
  childService: {
    invoke: {
      src: "childMachine",
      input: ({ context }) => ({
        sessionId: context.sessionId,
        phoneNumber: context.phoneNumber,
        serviceCode: context.serviceCode,
      }),
      onDone: "nextState", // Simple routing
    },
  },
}
```

### 4. Non-Standard Interface Anti-Pattern

```typescript
// ❌ DON'T: Use inconsistent input/output patterns
// Machine A expects: { userId, sessionData }
// Machine B expects: { user_id, session_info }
// Machine C expects: { id, session }

// ✅ DO: Use standardized interfaces
interface StandardChildInput {
  sessionId: string;
  phoneNumber: string;
  serviceCode: string;
  // Additional data as needed
}

interface StandardChildOutput {
  route: "main" | "back" | "exit";
  context?: any;
  message?: string;
}
```

## 🔧 Refactoring Checklist

### For Child Machines:

- [ ] Remove unnecessary `idle` states
- [ ] Start in functional initial state
- [ ] Use `context: ({ input }) => ({ ... })` for initialization
- [ ] Implement standard input/output interface
- [ ] Ensure machine works autonomously
- [ ] Add comprehensive unit tests
- [ ] Create demo file for interactive testing

### For Parent Machine:

- [ ] Simplify invocation logic
- [ ] Remove child machine implementation details
- [ ] Use standard input patterns
- [ ] Implement simple routing on `onDone`
- [ ] Extract complex logic to child machines
- [ ] Reduce overall line count
- [ ] Focus on orchestration only

### For Overall Architecture:

- [ ] Define standard interfaces
- [ ] Create reusable guard modules
- [ ] Implement consistent error handling
- [ ] Add integration tests
- [ ] Update documentation
- [ ] Validate TypeScript inference

## 📚 References

- [XState v5 Invoke Documentation](https://stately.ai/docs/invoke)
- [XState v5 Setup Documentation](https://stately.ai/docs/setup)
- [XState v5 Machine Actors](https://stately.ai/docs/state-machine-actors)
- [XState v5 Best Practices](https://stately.ai/blog/2023-12-01-xstate-v5)
- [XState v5 Migration Guide](https://stately.ai/docs/migration)

## 💡 Implementation Examples

### Example 1: Refactored knowMoreMachine

```typescript
import { setup, assign } from "xstate";

export const knowMoreMachine = setup({
  types: {
    context: {} as {
      sessionId: string;
      phoneNumber: string;
      serviceCode: string;
      selectedInfo?: "1" | "2" | "3" | "4";
      currentInfoPage: number;
      totalInfoPages: number;
      infoContent: string;
      error?: string;
    },
    events: {} as
      | { type: "SELECT_1" | "SELECT_2" | "SELECT_3" | "SELECT_4" }
      | { type: "NEXT_PAGE" | "PREVIOUS_PAGE" }
      | { type: "BACK_TO_MENU" | "BACK_TO_MAIN" }
      | { type: "ERROR"; error: string },
    input: {} as {
      sessionId: string;
      phoneNumber: string;
      serviceCode: string;
    },
  },
  actions: {
    setSelectedInfo: assign({
      selectedInfo: ({ event }) => {
        if (event.type === "SELECT_1") return "1";
        if (event.type === "SELECT_2") return "2";
        if (event.type === "SELECT_3") return "3";
        if (event.type === "SELECT_4") return "4";
        return undefined;
      },
    }),
    loadProductInfo: assign({
      infoContent:
        "SupaMoto Products:\n1. Solar Home Systems\n2. Clean Cookstoves\n3. Water Purification\n4. LED Lighting\n\nAll products are carbon-verified.",
      totalInfoPages: 2,
      currentInfoPage: 1,
    }),
    // ... other actions
  },
  guards: {
    hasNextPage: ({ context }) =>
      context.currentInfoPage < context.totalInfoPages,
    hasPreviousPage: ({ context }) => context.currentInfoPage > 1,
  },
}).createMachine({
  id: "knowMoreMachine",
  initial: "infoMenu", // ✅ Start directly in working state

  context: ({ input }) => ({
    sessionId: input.sessionId,
    phoneNumber: input.phoneNumber,
    serviceCode: input.serviceCode,
    currentInfoPage: 1,
    totalInfoPages: 1,
    infoContent:
      "Welcome to SupaMoto Information Center\n1. Product Information\n2. Service Information\n3. About SupaMoto\n4. Contact Information",
  }),

  states: {
    infoMenu: {
      on: {
        SELECT_1: {
          target: "displayingInfo",
          actions: ["setSelectedInfo", "loadProductInfo"],
        },
        SELECT_2: {
          target: "displayingInfo",
          actions: ["setSelectedInfo", "loadServiceInfo"],
        },
        SELECT_3: {
          target: "displayingInfo",
          actions: ["setSelectedInfo", "loadAboutInfo"],
        },
        SELECT_4: {
          target: "displayingInfo",
          actions: ["setSelectedInfo", "loadContactInfo"],
        },
        BACK_TO_MAIN: {
          type: "final",
          output: { route: "main" as const },
        },
      },
    },

    displayingInfo: {
      on: {
        NEXT_PAGE: {
          guard: "hasNextPage",
          actions: "nextPage",
        },
        PREVIOUS_PAGE: {
          guard: "hasPreviousPage",
          actions: "previousPage",
        },
        BACK_TO_MENU: "infoMenu",
        BACK_TO_MAIN: {
          type: "final",
          output: { route: "main" as const },
        },
      },
    },
  },
});
```

### Example 2: Simplified Parent Machine

```typescript
export const supamotoWalletMachine = setup({
  types: {
    context: {} as SupamotoWalletContext,
    events: {} as SupamotoWalletEvent,
  },
  actors: {
    knowMoreMachine,
    userServicesMachine,
    agentMachine,
    verifyWallet: fromPromise(
      async ({ input }: { input: { walletId: string } }) => {
        // Wallet verification logic
        return { isValid: input.walletId.length >= 8 };
      }
    ),
  },
  actions: {
    setPreMenuMessage: assign({
      message:
        "Welcome to SupaMoto\n1. Know More\n2. Enter Wallet ID\n3. Agent Menu",
    }),
    setWalletIdEntryMessage: assign({
      message: "Enter your Wallet ID:",
    }),
    // ... other actions
  },
  guards: {
    ...navigationGuards, // Reuse modular guards
    isValidWalletIdInput: ({ event }) =>
      event.type === "INPUT" && event.input.length >= 8,
  },
}).createMachine({
  id: "supamotoWalletMachine",
  initial: "preMenu",

  context: ({ input }) => ({
    sessionId: input.sessionId,
    phoneNumber: input.phoneNumber,
    serviceCode: input.serviceCode,
    isAuthenticated: false,
    isAgent: false,
    sessionStartTime: new Date().toISOString(),
    message: "",
  }),

  states: {
    preMenu: {
      entry: "setPreMenuMessage",
      on: {
        INPUT: [
          { target: "knowMoreService", guard: "isInput1" },
          { target: "walletIdEntry", guard: "isInput2" },
          { target: "agentIdEntry", guard: "isInput3" },
          { target: "closeSession", guard: "isBack" },
        ],
      },
    },

    knowMoreService: {
      invoke: {
        src: "knowMoreMachine",
        input: ({ context }) => ({
          sessionId: context.sessionId,
          phoneNumber: context.phoneNumber,
          serviceCode: context.serviceCode,
        }),
        onDone: "preMenu", // Simple routing back to main menu
      },
    },

    walletIdEntry: {
      entry: "setWalletIdEntryMessage",
      on: {
        INPUT: [
          {
            target: "verifyingWallet",
            guard: "isValidWalletIdInput",
            actions: assign({ walletId: ({ event }) => event.input }),
          },
          { target: "preMenu", guard: "isBack" },
        ],
      },
    },

    verifyingWallet: {
      invoke: {
        src: "verifyWallet",
        input: ({ context }) => ({ walletId: context.walletId! }),
        onDone: {
          target: "userServicesMenu",
          actions: assign({ isAuthenticated: true }),
        },
        onError: "walletIdEntry",
      },
    },

    userServicesMenu: {
      invoke: {
        src: "userServicesMachine",
        input: ({ context }) => ({
          sessionId: context.sessionId,
          phoneNumber: context.phoneNumber,
          serviceCode: context.serviceCode,
          walletId: context.walletId,
        }),
        onDone: "preMenu",
      },
    },

    // ... other states
  },
});
```

## 🧪 Testing Patterns

### Autonomous Child Machine Testing

```typescript
import { createActor } from "xstate";
import { knowMoreMachine } from "./knowMoreMachine.js";

describe("knowMoreMachine", () => {
  it("should start in infoMenu state with proper context", () => {
    const actor = createActor(knowMoreMachine, {
      input: {
        sessionId: "test-123",
        phoneNumber: "+************",
        serviceCode: "*2233#",
      },
    });

    actor.start();
    const snapshot = actor.getSnapshot();

    expect(snapshot.value).toBe("infoMenu");
    expect(snapshot.context.sessionId).toBe("test-123");
    expect(snapshot.context.infoContent).toContain("Welcome to SupaMoto");
  });

  it("should transition to displayingInfo when selecting product info", () => {
    const actor = createActor(knowMoreMachine, {
      input: {
        sessionId: "test-123",
        phoneNumber: "+************",
        serviceCode: "*2233#",
      },
    });

    actor.start();
    actor.send({ type: "SELECT_1" });

    const snapshot = actor.getSnapshot();
    expect(snapshot.value).toBe("displayingInfo");
    expect(snapshot.context.selectedInfo).toBe("1");
    expect(snapshot.context.infoContent).toContain("SupaMoto Products");
  });

  it("should output correct route when going back to main", () => {
    const actor = createActor(knowMoreMachine, {
      input: {
        sessionId: "test-123",
        phoneNumber: "+************",
        serviceCode: "*2233#",
      },
    });

    actor.start();
    actor.send({ type: "BACK_TO_MAIN" });

    const snapshot = actor.getSnapshot();
    expect(snapshot.status).toBe("done");
    expect(snapshot.output).toEqual({ route: "main" });
  });
});
```

---

_This guide provides the foundation for refactoring our XState v5 architecture to be more modular, maintainable, and aligned with framework best practices._
