// This file was auto-generated by 'typesafe-i18n'. Any manual changes will be overwritten.
/* eslint-disable */
import type {
  BaseTranslation as BaseTranslationType,
  LocalizedString,
  RequiredParams,
} from "typesafe-i18n";

export type BaseTranslation = BaseTranslationType;
export type BaseLocale = "eng";

export type Locales = "eng" | "swa";

export type Translation = RootTranslation;

export type Translations = RootTranslation;

type RootTranslation = {
  welcome: {
    /**
     * W​e​l​c​o​m​e​ ​t​o​ ​I​X​O​ ​P​l​a​t​f​o​r​m
     */
    title: string;
    menu: {
      /**
       * M​y​ ​A​c​c​o​u​n​t
       */
      account: string;
      /**
       * S​e​r​v​i​c​e​s
       */
      services: string;
      /**
       * H​e​l​p
       */
      help: string;
    };
  };
  account: {
    /**
     * A​c​c​o​u​n​t​ ​I​n​f​o​r​m​a​t​i​o​n
     */
    title: string;
    /**
     * P​h​o​n​e​:​ ​{​p​h​o​n​e​}
     * @param {unknown} phone
     */
    info: RequiredParams<"phone">;
    menu: {
      /**
       * B​a​l​a​n​c​e
       */
      balance: string;
      /**
       * P​r​o​f​i​l​e
       */
      profile: string;
      /**
       * B​a​c​k
       */
      back: string;
    };
  };
  balance: {
    /**
     * B​a​l​a​n​c​e
     */
    title: string;
    /**
     * Y​o​u​r​ ​c​u​r​r​e​n​t​ ​b​a​l​a​n​c​e​ ​i​s​:​ ​{​a​m​o​u​n​t​}​ ​{​c​u​r​r​e​n​c​y​}
     * @param {unknown} amount
     * @param {unknown} currency
     */
    info: RequiredParams<"amount" | "currency">;
    /**
     * B​a​c​k
     */
    back: string;
  };
  profile: {
    /**
     * P​r​o​f​i​l​e
     */
    title: string;
    /**
     * P​h​o​n​e​:​ ​{​p​h​o​n​e​}
     * @param {unknown} phone
     */
    phone: RequiredParams<"phone">;
    /**
     * L​a​n​g​u​a​g​e​:​ ​{​l​a​n​g​u​a​g​e​}
     * @param {unknown} language
     */
    language: RequiredParams<"language">;
    /**
     * B​a​c​k
     */
    back: string;
  };
  services: {
    /**
     * A​v​a​i​l​a​b​l​e​ ​S​e​r​v​i​c​e​s
     */
    title: string;
    menu: {
      /**
       * T​r​a​n​s​f​e​r
       */
      transfer: string;
      /**
       * P​a​y​m​e​n​t​s
       */
      payments: string;
      /**
       * C​l​a​i​m​s
       */
      claims: string;
      /**
       * B​a​c​k
       */
      back: string;
    };
  };
  help: {
    /**
     * H​e​l​p
     */
    title: string;
    /**
     * N​e​e​d​ ​h​e​l​p​?​ ​C​o​n​t​a​c​t​ ​s​u​p​p​o​r​t​ ​a​t​:​ ​{​p​h​o​n​e​}
     * @param {unknown} phone
     */
    contact: RequiredParams<"phone">;
    /**
     * B​a​c​k
     */
    back: string;
  };
  common: {
    /**
     * A​n​ ​e​r​r​o​r​ ​o​c​c​u​r​r​e​d​.​ ​P​l​e​a​s​e​ ​t​r​y​ ​a​g​a​i​n​ ​l​a​t​e​r​.
     */
    error: string;
    /**
     * I​n​v​a​l​i​d​ ​i​n​p​u​t​.​ ​P​l​e​a​s​e​ ​t​r​y​ ​a​g​a​i​n​.
     */
    invalidInput: string;
    /**
     * B​a​c​k​ ​t​o​ ​m​a​i​n​ ​m​e​n​u
     */
    back: string;
  };
};

export type TranslationFunctions = {
  welcome: {
    /**
     * Welcome to IXO Platform
     */
    title: () => LocalizedString;
    menu: {
      /**
       * My Account
       */
      account: () => LocalizedString;
      /**
       * Services
       */
      services: () => LocalizedString;
      /**
       * Help
       */
      help: () => LocalizedString;
    };
  };
  account: {
    /**
     * Account Information
     */
    title: () => LocalizedString;
    /**
     * Phone: {phone}
     */
    info: (arg: { phone: unknown }) => LocalizedString;
    menu: {
      /**
       * Balance
       */
      balance: () => LocalizedString;
      /**
       * Profile
       */
      profile: () => LocalizedString;
      /**
       * Back
       */
      back: () => LocalizedString;
    };
  };
  balance: {
    /**
     * Balance
     */
    title: () => LocalizedString;
    /**
     * Your current balance is: {amount} {currency}
     */
    info: (arg: { amount: unknown; currency: unknown }) => LocalizedString;
    /**
     * Back
     */
    back: () => LocalizedString;
  };
  profile: {
    /**
     * Profile
     */
    title: () => LocalizedString;
    /**
     * Phone: {phone}
     */
    phone: (arg: { phone: unknown }) => LocalizedString;
    /**
     * Language: {language}
     */
    language: (arg: { language: unknown }) => LocalizedString;
    /**
     * Back
     */
    back: () => LocalizedString;
  };
  services: {
    /**
     * Available Services
     */
    title: () => LocalizedString;
    menu: {
      /**
       * Transfer
       */
      transfer: () => LocalizedString;
      /**
       * Payments
       */
      payments: () => LocalizedString;
      /**
       * Claims
       */
      claims: () => LocalizedString;
      /**
       * Back
       */
      back: () => LocalizedString;
    };
  };
  help: {
    /**
     * Help
     */
    title: () => LocalizedString;
    /**
     * Need help? Contact support at: {phone}
     */
    contact: (arg: { phone: unknown }) => LocalizedString;
    /**
     * Back
     */
    back: () => LocalizedString;
  };
  common: {
    /**
     * An error occurred. Please try again later.
     */
    error: () => LocalizedString;
    /**
     * Invalid input. Please try again.
     */
    invalidInput: () => LocalizedString;
    /**
     * Back to main menu
     */
    back: () => LocalizedString;
  };
};

export type Formatters = {};
