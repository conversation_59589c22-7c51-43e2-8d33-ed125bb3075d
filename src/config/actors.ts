/**
 * Actor Configuration System
 *
 * Provides environment-based actor configuration for the SupaMoto Wallet system.
 * Supports production, development, and test environments with appropriate
 * service implementations.
 */

import { fromPromise } from "xstate";
import {
  mockWalletVerificationService,
  walletVerificationService,
  type WalletVerificationInput,
} from "../services/wallet-verification.js";
import {
  mockAgentVerificationService,
  agentVerificationService,
  type AgentVerificationInput,
} from "../services/agent-verification.js";
import { createModuleLogger } from "../services/logger.js";
import { ENV, type Environment } from "../config.js";

const logger = createModuleLogger("actor-config");

/**
 * Actor configuration interface
 * Defines the shape of actors that can be provided to machines
 */
export interface ActorConfiguration {
  verifyWallet: any; // XState Promise actor
  verifyAgent: any; // XState Promise actor
}

/**
 * Create production actors
 * Uses real services that would call backend APIs
 */
function createProductionActors(): ActorConfiguration {
  logger.info("Creating production actors");

  return {
    verifyWallet: fromPromise(
      async ({ input }: { input: WalletVerificationInput }) => {
        return await walletVerificationService.verifyWallet(input);
      }
    ),

    verifyAgent: fromPromise(
      async ({ input }: { input: AgentVerificationInput }) => {
        return await agentVerificationService.verifyAgent(input);
      }
    ),
  };
}

/**
 * Create development actors
 * Uses mock services with realistic delays and data
 */
function createDevelopmentActors(): ActorConfiguration {
  logger.info("Creating development actors");

  return {
    verifyWallet: fromPromise(
      async ({ input }: { input: WalletVerificationInput }) => {
        return await mockWalletVerificationService.verifyWallet(input);
      }
    ),

    verifyAgent: fromPromise(
      async ({ input }: { input: AgentVerificationInput }) => {
        return await mockAgentVerificationService.verifyAgent(input);
      }
    ),
  };
}

/**
 * Create test actors
 * Uses mock services with faster responses for testing
 */
function createTestActors(): ActorConfiguration {
  logger.info("Creating test actors");

  return {
    verifyWallet: fromPromise(
      async ({ input }: { input: WalletVerificationInput }) => {
        // Faster response for tests
        await new Promise(resolve => setTimeout(resolve, 100));
        return await mockWalletVerificationService.verifyWallet(input);
      }
    ),

    verifyAgent: fromPromise(
      async ({ input }: { input: AgentVerificationInput }) => {
        // Faster response for tests
        await new Promise(resolve => setTimeout(resolve, 100));
        return await mockAgentVerificationService.verifyAgent(input);
      }
    ),
  };
}

/**
 * Create actors based on environment
 */
export function createActors(environment: Environment): ActorConfiguration {
  switch (environment) {
    case "production":
      return createProductionActors();
    case "development":
      return createDevelopmentActors();
    case "test":
      return createTestActors();
    default:
      logger.warn(
        `Unknown environment: ${environment}, defaulting to development`
      );
      return createDevelopmentActors();
  }
}

/**
 * Create actors for current environment
 * Uses centralized environment detection from config
 */
export function createCurrentEnvironmentActors(): ActorConfiguration {
  const environment = ENV.CURRENT;
  logger.info(`Creating actors for environment: ${environment}`);
  return createActors(environment);
}

/**
 * Actor factory with custom configuration
 * Allows for custom actor implementations (useful for specific tests)
 */
export class ActorFactory {
  private environment: Environment;

  constructor(environment: Environment = ENV.CURRENT) {
    this.environment = environment;
  }

  /**
   * Create standard actors for the configured environment
   */
  createStandardActors(): ActorConfiguration {
    return createActors(this.environment);
  }

  /**
   * Create custom actors with overrides
   * Useful for testing specific scenarios
   */
  createCustomActors(
    overrides: Partial<ActorConfiguration>
  ): ActorConfiguration {
    const standardActors = this.createStandardActors();
    return {
      ...standardActors,
      ...overrides,
    };
  }

  /**
   * Get the current environment
   */
  getEnvironment(): Environment {
    return this.environment;
  }

  /**
   * Set a new environment
   */
  setEnvironment(environment: Environment): void {
    this.environment = environment;
    logger.info(`Environment changed to: ${environment}`);
  }
}

// Export default factory instance
export const actorFactory = new ActorFactory();

// Export convenience functions (already exported above)

/**
 * Utility functions for testing
 */
export const testUtils = {
  /**
   * Get valid wallet IDs for testing
   */
  getValidWalletIds(): string[] {
    return mockWalletVerificationService.getValidWalletIds();
  },

  /**
   * Get valid agent IDs for testing
   */
  getValidAgentIds(): string[] {
    return mockAgentVerificationService.getValidAgentIds();
  },

  /**
   * Add mock wallet for testing
   */
  addMockWallet(walletId: string, customerName: string, balance: number): void {
    mockWalletVerificationService.addMockWallet(
      walletId,
      customerName,
      balance
    );
  },

  /**
   * Add mock agent for testing
   */
  addMockAgent(
    agentId: string,
    agentName: string,
    agentLevel: "basic" | "premium" | "gold" | "platinum"
  ): void {
    mockAgentVerificationService.addMockAgent(agentId, agentName, agentLevel);
  },
};
