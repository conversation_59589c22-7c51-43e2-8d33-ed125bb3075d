import { z } from "zod";
import { phoneNumberSchema, amountSchema } from "./ussd.schemas.js";

/**
 * SupaMoto API Validation Schemas
 *
 * Validation schemas for SupaMoto integration endpoints
 */

// SupaMoto wallet ID validation
export const supaMotoWalletIdSchema = z
  .string()
  .min(10, "Wallet ID too short")
  .max(50, "Wallet ID too long")
  .regex(/^[A-Za-z0-9_-]+$/, "Invalid wallet ID format");

// Customer reference validation
export const customerReferenceSchema = z
  .string()
  .min(1, "Customer reference is required")
  .max(100, "Customer reference too long")
  .regex(/^[A-Za-z0-9_.-]+$/, "Invalid customer reference format");

// Province/District/Area codes for fault reporting
export const locationCodeSchema = z
  .string()
  .min(1, "Location code is required")
  .max(10, "Location code too long")
  .regex(/^[A-Z0-9_-]+$/, "Invalid location code format");

// Product codes for pellet purchasing
export const productCodeSchema = z
  .string()
  .min(1, "Product code is required")
  .max(20, "Product code too long")
  .regex(/^[A-Z0-9_-]+$/, "Invalid product code format");

// Quantity validation for orders
export const quantitySchema = z
  .number()
  .int("Quantity must be an integer")
  .min(1, "Quantity must be at least 1")
  .max(1000, "Quantity exceeds maximum limit");

// Order ID validation
export const orderIdSchema = z
  .string()
  .min(5, "Order ID too short")
  .max(50, "Order ID too long")
  .regex(/^[A-Za-z0-9_-]+$/, "Invalid order ID format");

/**
 * Wallet Management Schemas
 */
export const createWalletRequestSchema = z
  .object({
    phoneNumber: phoneNumberSchema,
    customerReference: customerReferenceSchema,
  })
  .strict();

export type CreateWalletRequest = z.infer<typeof createWalletRequestSchema>;

export const walletResponseSchema = z
  .object({
    walletId: supaMotoWalletIdSchema,
    phoneNumber: phoneNumberSchema,
    balance: z.number().min(0),
    status: z.enum(["active", "inactive", "suspended"]),
    createdAt: z.string().datetime().optional(),
  })
  .strict();

export type WalletResponse = z.infer<typeof walletResponseSchema>;

/**
 * Balance and Top-up Schemas
 */
export const balanceRequestSchema = z
  .object({
    walletId: supaMotoWalletIdSchema,
  })
  .strict();

export type BalanceRequest = z.infer<typeof balanceRequestSchema>;

export const balanceResponseSchema = z
  .object({
    walletId: supaMotoWalletIdSchema,
    balance: z.number().min(0),
    currency: z.string().length(3).default("USD"),
    lastUpdated: z.string().datetime().optional(),
  })
  .strict();

export type BalanceResponse = z.infer<typeof balanceResponseSchema>;

export const topUpRequestSchema = z
  .object({
    walletId: supaMotoWalletIdSchema,
    amount: amountSchema,
    paymentMethod: z.enum(["mobile_money", "bank_transfer", "cash"]),
    reference: z.string().max(50).optional(),
  })
  .strict();

export type TopUpRequest = z.infer<typeof topUpRequestSchema>;

/**
 * Pellet Purchase Schemas
 */
export const purchasePelletRequestSchema = z
  .object({
    walletId: supaMotoWalletIdSchema,
    productCode: productCodeSchema,
    quantity: quantitySchema,
    totalAmount: amountSchema,
    deliveryAddress: z.string().min(10).max(200),
    phoneNumber: phoneNumberSchema,
  })
  .strict();

export type PurchasePelletRequest = z.infer<typeof purchasePelletRequestSchema>;

export const purchaseAccessoryRequestSchema = z
  .object({
    walletId: supaMotoWalletIdSchema,
    productCode: productCodeSchema,
    quantity: quantitySchema,
    totalAmount: amountSchema,
    deliveryAddress: z.string().min(10).max(200),
    phoneNumber: phoneNumberSchema,
  })
  .strict();

export type PurchaseAccessoryRequest = z.infer<
  typeof purchaseAccessoryRequestSchema
>;

/**
 * Order Management Schemas
 */
export const orderTrackingRequestSchema = z
  .object({
    orderId: orderIdSchema,
    walletId: supaMotoWalletIdSchema.optional(),
    phoneNumber: phoneNumberSchema.optional(),
  })
  .strict();

export type OrderTrackingRequest = z.infer<typeof orderTrackingRequestSchema>;

export const orderStatusSchema = z.enum([
  "pending",
  "confirmed",
  "processing",
  "shipped",
  "delivered",
  "cancelled",
  "refunded",
]);

export const orderResponseSchema = z
  .object({
    orderId: orderIdSchema,
    walletId: supaMotoWalletIdSchema,
    status: orderStatusSchema,
    productCode: productCodeSchema,
    quantity: quantitySchema,
    totalAmount: z.number().min(0),
    orderDate: z.string().datetime(),
    deliveryDate: z.string().datetime().optional(),
    trackingNumber: z.string().optional(),
    deliveryAddress: z.string(),
  })
  .strict();

export type OrderResponse = z.infer<typeof orderResponseSchema>;

/**
 * Fault Reporting Schemas
 */
export const reportFaultRequestSchema = z
  .object({
    walletId: supaMotoWalletIdSchema,
    phoneNumber: phoneNumberSchema,
    provinceCode: locationCodeSchema,
    districtCode: locationCodeSchema,
    areaCode: locationCodeSchema,
    faultDescription: z.string().min(10).max(500),
    faultCategory: z.enum([
      "device",
      "delivery",
      "payment",
      "quality",
      "other",
    ]),
    urgency: z.enum(["low", "medium", "high", "critical"]).default("medium"),
  })
  .strict();

export type ReportFaultRequest = z.infer<typeof reportFaultRequestSchema>;

export const faultResponseSchema = z
  .object({
    faultId: z.string(),
    walletId: supaMotoWalletIdSchema,
    status: z.enum(["reported", "investigating", "resolved", "closed"]),
    category: z.string(),
    description: z.string(),
    reportedAt: z.string().datetime(),
    resolvedAt: z.string().datetime().optional(),
    resolution: z.string().optional(),
  })
  .strict();

export type FaultResponse = z.infer<typeof faultResponseSchema>;

/**
 * Performance and Analytics Schemas
 */
export const performanceRequestSchema = z
  .object({
    walletId: supaMotoWalletIdSchema,
    dateFrom: z.string().datetime().optional(),
    dateTo: z.string().datetime().optional(),
    metricType: z.enum(["usage", "orders", "payments", "faults"]).optional(),
  })
  .strict();

export type PerformanceRequest = z.infer<typeof performanceRequestSchema>;

export const performanceResponseSchema = z
  .object({
    walletId: supaMotoWalletIdSchema,
    period: z.object({
      from: z.string().datetime(),
      to: z.string().datetime(),
    }),
    metrics: z.object({
      totalOrders: z.number().min(0),
      totalSpent: z.number().min(0),
      deliveryRate: z.number().min(0).max(100),
      faultCount: z.number().min(0),
      satisfactionScore: z.number().min(1).max(5).optional(),
    }),
  })
  .strict();

export type PerformanceResponse = z.infer<typeof performanceResponseSchema>;

/**
 * Collection Partner Schemas
 */
export const collectionPartnerRequestSchema = z
  .object({
    location: z.object({
      provinceCode: locationCodeSchema,
      districtCode: locationCodeSchema,
      areaCode: locationCodeSchema.optional(),
    }),
    radius: z.number().min(1).max(100).optional().default(10), // km radius
  })
  .strict();

export type CollectionPartnerRequest = z.infer<
  typeof collectionPartnerRequestSchema
>;

export const collectionPartnerSchema = z
  .object({
    partnerId: z.string(),
    name: z.string().min(1).max(100),
    address: z.string().min(10).max(200),
    phoneNumber: phoneNumberSchema,
    location: z.object({
      latitude: z.number().min(-90).max(90),
      longitude: z.number().min(-180).max(180),
      provinceCode: locationCodeSchema,
      districtCode: locationCodeSchema,
      areaCode: locationCodeSchema.optional(),
    }),
    services: z.array(z.enum(["pickup", "delivery", "payment", "support"])),
    operatingHours: z.string().optional(),
    distance: z.number().min(0).optional(), // Distance from query point in km
  })
  .strict();

export type CollectionPartner = z.infer<typeof collectionPartnerSchema>;

/**
 * Location Reference Schemas
 */
export const provinceSchema = z
  .object({
    code: locationCodeSchema,
    name: z.string().min(1).max(50),
    districts: z
      .array(
        z.object({
          code: locationCodeSchema,
          name: z.string().min(1).max(50),
          areas: z
            .array(
              z.object({
                code: locationCodeSchema,
                name: z.string().min(1).max(50),
              })
            )
            .optional(),
        })
      )
      .optional(),
  })
  .strict();

export type Province = z.infer<typeof provinceSchema>;

/**
 * Product Catalog Schemas
 */
export const productSchema = z
  .object({
    code: productCodeSchema,
    name: z.string().min(1).max(100),
    category: z.enum(["pellet", "accessory", "service"]),
    description: z.string().max(500).optional(),
    price: z.number().min(0),
    currency: z.string().length(3).default("USD"),
    available: z.boolean().default(true),
    specifications: z.record(z.string()).optional(),
  })
  .strict();

export type Product = z.infer<typeof productSchema>;

/**
 * Validation Helper Functions
 */

/**
 * Validate SupaMoto wallet ID
 */
export function validateWalletId(walletId: string): {
  isValid: boolean;
  error?: string;
} {
  try {
    supaMotoWalletIdSchema.parse(walletId);
    return { isValid: true };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return { isValid: false, error: error.errors[0].message };
    }
    return { isValid: false, error: "Invalid wallet ID" };
  }
}

/**
 * Validate location codes (province, district, area)
 */
export function validateLocationCode(code: string): {
  isValid: boolean;
  error?: string;
} {
  try {
    locationCodeSchema.parse(code);
    return { isValid: true };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return { isValid: false, error: error.errors[0].message };
    }
    return { isValid: false, error: "Invalid location code" };
  }
}

/**
 * Validate order quantity
 */
export function validateQuantity(quantity: number): {
  isValid: boolean;
  error?: string;
} {
  try {
    quantitySchema.parse(quantity);
    return { isValid: true };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return { isValid: false, error: error.errors[0].message };
    }
    return { isValid: false, error: "Invalid quantity" };
  }
}
