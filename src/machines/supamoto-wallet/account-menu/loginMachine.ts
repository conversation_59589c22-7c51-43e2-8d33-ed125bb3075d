/**
 * Login Machine - Handles customer authentication flow
 *
 * This machine implements the login flow where users:
 * 1. Enter their customer ID
 * 2. System verifies customer exists and has PIN
 * 3. User enters PIN (up to 3 attempts)
 * 4. System verifies PIN securely
 * 5. Returns appropriate result to parent machine
 *
 * Follows autonomous child machine patterns with two events: INPUT and ERROR
 */

import { setup, assign, fromPromise } from "xstate";
import { withNavigation } from "../utils/navigation-mixin.js";
import {
  progressiveDataService,
  type CustomerRecord,
} from "../../../services/progressive-data.js";
import { verifyPin } from "../../../utils/pin-encryption.js";
import { createModuleLogger } from "../../../services/logger.js";
import { NavigationPatterns } from "../utils/navigation-patterns.js";

const logger = createModuleLogger("loginMachine");

// Types and Interfaces
export interface LoginContext {
  sessionId: string;
  phoneNumber: string;
  serviceCode: string;
  message: string;
  error?: string;
  customerId?: string;
  customer?: CustomerRecord;
  pinAttempts: number;
  nextParentState: LoginOutput;
}

export interface LoginInput {
  sessionId: string;
  phoneNumber: string;
  serviceCode: string;
}

export type LoginEvent =
  | { type: "INPUT"; text: string }
  | { type: "ERROR"; error: string };

export enum LoginOutput {
  LOGIN_SUCCESS = "LOGIN_SUCCESS",
  CUSTOMER_NOT_FOUND = "CUSTOMER_NOT_FOUND",
  ENCRYPTED_PIN_FIELD_EMPTY = "ENCRYPTED_PIN_FIELD_EMPTY",
  MAX_ATTEMPTS_EXCEEDED = "MAX_ATTEMPTS_EXCEEDED",
  CANCELLED = "CANCELLED",
  UNDEFINED = "UNDEFINED",
}

// Messages
const CUSTOMER_ID_PROMPT = "Enter your SupaMoto Customer ID:";
const PIN_PROMPT = "Enter your PIN:";
const INVALID_CUSTOMER_ID = "Invalid Customer ID format. Please try again.";
const CUSTOMER_NOT_FOUND_MSG =
  "Customer ID not found. Please check and try again or contact SupaMoto support.";
const PIN_FIELD_EMPTY_MSG =
  "Your account needs PIN setup. Please contact SupaMoto support.";
const INCORRECT_PIN_MSG = "Incorrect PIN. Please try again.";
const MAX_ATTEMPTS_MSG =
  "Maximum PIN attempts exceeded. Your PIN has been reset for security reasons. Please contact SupaMoto support.";
const VERIFYING_MSG = "Verifying...";

// Guards
/**
 * Validates customer ID format (C followed by 8+ digits)
 */
const isValidCustomerId = ({ event }: { event: LoginEvent }) => {
  if (event.type !== "INPUT") return false;
  const customerId = event.text.trim();
  // Basic validation: should start with 'C' and be followed by digits
  return /^C\d{8,}$/.test(customerId);
};

/**
 * Validates PIN format (4-6 digits)
 */
const isValidPin = ({ event }: { event: LoginEvent }) => {
  if (event.type !== "INPUT") return false;
  const pin = event.text.trim();
  // PIN should be 4-6 digits
  return /^\d{4,6}$/.test(pin);
};

/**
 * Checks if maximum PIN attempts (3) have been exceeded
 */
const hasMaxAttemptsExceeded = ({ context }: { context: LoginContext }) => {
  return context.pinAttempts >= 3;
};

// Actors
/**
 * Service actor that looks up customer by customer ID
 * Handles three scenarios:
 * - Customer not found: throws CUSTOMER_NOT_FOUND
 * - Customer found but no PIN: throws ENCRYPTED_PIN_FIELD_EMPTY
 * - Customer found with PIN: returns customer record
 */
const customerLookupService = fromPromise(
  async ({ input }: { input: { customerId: string } }) => {
    logger.info(
      { customerId: input.customerId.slice(-4) },
      "Looking up customer"
    );

    const customer = await progressiveDataService.getCustomerByCustomerId(
      input.customerId
    );

    if (!customer) {
      throw new Error("CUSTOMER_NOT_FOUND");
    }

    if (!customer.encryptedPin) {
      throw new Error("ENCRYPTED_PIN_FIELD_EMPTY");
    }

    return customer;
  }
);

/**
 * Service actor that verifies PIN securely
 * Handles attempt tracking and PIN clearing on max attempts
 * - Correct PIN: returns success
 * - Incorrect PIN (< 3 attempts): throws INCORRECT_PIN
 * - Incorrect PIN (3rd attempt): clears PIN and throws MAX_ATTEMPTS_EXCEEDED
 */
const pinVerificationService = fromPromise(
  async ({
    input,
  }: {
    input: {
      pin: string;
      customer: CustomerRecord;
      attempts: number;
      customerId: string;
    };
  }) => {
    logger.info(
      {
        customerId: input.customerId.slice(-4),
        attempts: input.attempts,
      },
      "Verifying PIN"
    );

    const isValid = verifyPin(input.pin, input.customer.encryptedPin!);

    if (!isValid) {
      // If this is the 3rd attempt, clear the PIN
      if (input.attempts >= 3) {
        logger.warn(
          { customerId: input.customerId.slice(-4) },
          "Max PIN attempts exceeded, clearing PIN"
        );
        await progressiveDataService.clearCustomerPin(input.customerId);
        throw new Error("MAX_ATTEMPTS_EXCEEDED");
      }
      throw new Error("INCORRECT_PIN");
    }

    return { success: true };
  }
);

// Machine type
export type LoginMachine = typeof loginMachine;

export const loginMachine = setup({
  types: {
    context: {} as LoginContext,
    events: {} as LoginEvent,
    input: {} as LoginInput,
  },
  guards: {
    isValidCustomerId,
    isValidPin,
    hasMaxAttemptsExceeded,
    // Navigation guards
    isBack: ({ event }) => event.type === "INPUT" && event.text === "0",
    isExit: ({ event }) => event.type === "INPUT" && event.text === "*",
  },
  actions: {
    clearErrors: assign(() => ({ error: undefined })),
  },
  actors: {
    customerLookupService,
    pinVerificationService,
  },
}).createMachine({
  id: "login",
  initial: "customerIdEntry",
  context: ({ input }): LoginContext => ({
    sessionId: input?.sessionId || "",
    phoneNumber: input?.phoneNumber || "",
    serviceCode: input?.serviceCode || "",
    message: CUSTOMER_ID_PROMPT,
    error: undefined,
    customerId: undefined,
    customer: undefined,
    pinAttempts: 0,
    nextParentState: LoginOutput.UNDEFINED,
  }),
  output: ({ context }) => ({
    result: context.nextParentState,
    customerId: context.customerId,
    customer: context.customer,
  }),
  states: {
    customerIdEntry: {
      entry: assign({ message: CUSTOMER_ID_PROMPT }),
      on: {
        INPUT: withNavigation(
          [
            {
              target: "verifyingCustomerId",
              guard: "isValidCustomerId",
              actions: [
                assign(({ event }) => {
                  if (event.type !== "INPUT") return {};
                  return { customerId: event.text.trim() };
                }),
              ],
            },
            {
              actions: assign({ message: INVALID_CUSTOMER_ID }),
            },
          ],
          NavigationPatterns.loginChild
        ),
        ERROR: {
          target: "error",
          actions: [
            assign(({ event }) => ({
              error: event.error || "An error occurred",
              message: "System error. Please try again.",
            })),
          ],
        },
      },
    },

    verifyingCustomerId: {
      entry: assign({ message: VERIFYING_MSG }),
      invoke: {
        src: "customerLookupService",
        input: ({ context }) => ({ customerId: context.customerId! }),
        onDone: {
          target: "pinEntry",
          actions: [
            assign(({ event }) => ({ customer: event.output })),
            assign({ message: PIN_PROMPT }),
          ],
        },
        onError: [
          {
            target: "customerNotFound",
            guard: ({ event }) =>
              (event.error as Error)?.message === "CUSTOMER_NOT_FOUND",
            actions: [
              assign({
                nextParentState: LoginOutput.CUSTOMER_NOT_FOUND,
                message: CUSTOMER_NOT_FOUND_MSG,
              }),
            ],
          },
          {
            target: "pinFieldEmpty",
            guard: ({ event }) =>
              (event.error as Error)?.message === "ENCRYPTED_PIN_FIELD_EMPTY",
            actions: [
              assign({
                nextParentState: LoginOutput.ENCRYPTED_PIN_FIELD_EMPTY,
                message: PIN_FIELD_EMPTY_MSG,
              }),
            ],
          },
          {
            target: "error",
            actions: [
              assign(({ event }) => ({
                error: (event.error as Error)?.message || "An error occurred",
                message: "System error. Please try again.",
              })),
            ],
          },
        ],
      },
    },

    pinEntry: {
      on: {
        INPUT: withNavigation(
          [
            {
              target: "verifyingPin",
              guard: "isValidPin",
            },
            {
              actions: assign({
                message: `${INCORRECT_PIN_MSG}\n\n${PIN_PROMPT}`,
              }),
            },
          ],
          NavigationPatterns.loginChild
        ),
        ERROR: {
          target: "error",
          actions: [
            assign(({ event }) => ({
              error: event.error || "An error occurred",
              message: "System error. Please try again.",
            })),
          ],
        },
      },
    },

    verifyingPin: {
      entry: assign({ message: VERIFYING_MSG }),
      invoke: {
        src: "pinVerificationService",
        input: ({ context, event }) => ({
          pin: (event as any).text.trim(),
          customer: context.customer!,
          attempts: context.pinAttempts + 1,
          customerId: context.customerId!,
        }),
        onDone: {
          target: "loginSuccess",
          actions: [
            assign({
              nextParentState: LoginOutput.LOGIN_SUCCESS,
              message: "Login successful!",
            }),
          ],
        },
        onError: [
          {
            target: "maxAttemptsExceeded",
            guard: ({ event }) =>
              (event.error as Error)?.message === "MAX_ATTEMPTS_EXCEEDED",
            actions: [
              assign({
                nextParentState: LoginOutput.MAX_ATTEMPTS_EXCEEDED,
                message: MAX_ATTEMPTS_MSG,
              }),
            ],
          },
          {
            target: "pinEntry",
            guard: ({ event }) =>
              (event.error as Error)?.message === "INCORRECT_PIN",
            actions: [
              assign(({ context }) => {
                const newAttempts = context.pinAttempts + 1;
                return {
                  pinAttempts: newAttempts,
                  message: `${INCORRECT_PIN_MSG} (${newAttempts}/3)\n\n${PIN_PROMPT}`,
                };
              }),
            ],
          },
          {
            target: "error",
            actions: [
              assign(({ event }) => ({
                error: (event.error as Error)?.message || "An error occurred",
                message: "System error. Please try again.",
              })),
            ],
          },
        ],
      },
    },

    loginSuccess: {
      type: "final",
    },

    customerNotFound: {
      type: "final",
    },

    pinFieldEmpty: {
      type: "final",
    },

    maxAttemptsExceeded: {
      type: "final",
    },

    cancelled: {
      entry: assign({
        nextParentState: LoginOutput.CANCELLED,
        message: "Login cancelled.",
      }),
      type: "final",
    },

    error: {
      on: {
        INPUT: withNavigation(
          [{ target: "customerIdEntry" }],
          NavigationPatterns.loginChild
        ),
        ERROR: {
          actions: [
            assign(({ event }) => ({
              error: event.error || "An error occurred",
              message: "System error. Please try again.",
            })),
          ],
        },
      },
    },
  },
});
