/**
 * Login Machine Tests
 *
 * Tests the login machine's behavior including:
 * - Customer ID validation and lookup
 * - PIN verification with attempt tracking
 * - Error handling and security measures
 * - Navigation and cancellation flows
 */

import { describe, it, expect, vi, beforeEach } from "vitest";
import { createActor } from "xstate";
import { loginMachine, LoginOutput } from "./loginMachine.js";
import { progressiveDataService } from "../../../services/progressive-data.js";
import { verifyPin } from "../../../utils/pin-encryption.js";

// Mock dependencies
vi.mock("../../../services/progressive-data.js");
vi.mock("../../../utils/pin-encryption.js");
vi.mock("../../../services/logger.js", () => ({
  createModuleLogger: () => ({
    info: vi.fn(),
    warn: vi.fn(),
    error: vi.fn(),
    debug: vi.fn(),
  }),
}));

const mockProgressiveDataService = vi.mocked(progressiveDataService);
const mockVerifyPin = vi.mocked(verifyPin);

describe("loginMachine", () => {
  const mockInput = {
    sessionId: "test-session",
    phoneNumber: "+260123456789",
    serviceCode: "*2233#",
  };

  const mockCustomer = {
    id: 1,
    customerId: "*********",
    fullName: "Test Customer",
    email: "<EMAIL>",
    encryptedPin: "encrypted-pin-hash",
    preferredLanguage: "eng",
    lastCompletedAction: "",
    householdId: undefined,
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe("Customer ID Entry", () => {
    it("should start in customerIdEntry state with correct message", () => {
      const actor = createActor(loginMachine, { input: mockInput });
      actor.start();

      expect(actor.getSnapshot().value).toBe("customerIdEntry");
      expect(actor.getSnapshot().context.message).toBe(
        "Enter your SupaMoto Customer ID:"
      );
    });

    it("should validate customer ID format", () => {
      const actor = createActor(loginMachine, { input: mockInput });
      actor.start();

      // Invalid format
      actor.send({ type: "INPUT", text: "invalid" });
      expect(actor.getSnapshot().value).toBe("customerIdEntry");
      expect(actor.getSnapshot().context.message).toBe(
        "Invalid Customer ID format. Please try again."
      );

      // Valid format
      mockProgressiveDataService.getCustomerByCustomerId.mockResolvedValue(
        mockCustomer
      );
      actor.send({ type: "INPUT", text: "*********" });
      expect(actor.getSnapshot().value).toBe("verifyingCustomerId");
    });

    it("should handle navigation commands", () => {
      const actor = createActor(loginMachine, { input: mockInput });
      actor.start();

      // Test exit command
      actor.send({ type: "INPUT", text: "*" });
      expect(actor.getSnapshot().value).toBe("cancelled");
      expect((actor.getSnapshot().output as any)?.result).toBe(
        LoginOutput.CANCELLED
      );
    });
  });

  describe("Customer Verification", () => {
    it("should handle customer not found", async () => {
      mockProgressiveDataService.getCustomerByCustomerId.mockRejectedValue(
        new Error("CUSTOMER_NOT_FOUND")
      );

      const actor = createActor(loginMachine, { input: mockInput });
      actor.start();

      actor.send({ type: "INPUT", text: "*********" });

      // Wait for async operation
      await new Promise(resolve => setTimeout(resolve, 10));

      expect(actor.getSnapshot().value).toBe("customerNotFound");
      expect((actor.getSnapshot().output as any)?.result).toBe(
        LoginOutput.CUSTOMER_NOT_FOUND
      );
    });

    it("should handle customer with empty PIN", async () => {
      mockProgressiveDataService.getCustomerByCustomerId.mockRejectedValue(
        new Error("ENCRYPTED_PIN_FIELD_EMPTY")
      );

      const actor = createActor(loginMachine, { input: mockInput });
      actor.start();

      actor.send({ type: "INPUT", text: "*********" });

      await new Promise(resolve => setTimeout(resolve, 10));

      expect(actor.getSnapshot().value).toBe("pinFieldEmpty");
      expect((actor.getSnapshot().output as any)?.result).toBe(
        LoginOutput.ENCRYPTED_PIN_FIELD_EMPTY
      );
    });

    it("should proceed to PIN entry for valid customer", async () => {
      mockProgressiveDataService.getCustomerByCustomerId.mockResolvedValue(
        mockCustomer
      );

      const actor = createActor(loginMachine, { input: mockInput });
      actor.start();

      actor.send({ type: "INPUT", text: "*********" });

      await new Promise(resolve => setTimeout(resolve, 10));

      expect(actor.getSnapshot().value).toBe("pinEntry");
      expect(actor.getSnapshot().context.message).toBe("Enter your PIN:");
      expect(actor.getSnapshot().context.customer).toEqual(mockCustomer);
    });
  });

  describe("PIN Entry and Verification", () => {
    beforeEach(async () => {
      mockProgressiveDataService.getCustomerByCustomerId.mockResolvedValue(
        mockCustomer
      );
    });

    it("should validate PIN format", async () => {
      const actor = createActor(loginMachine, { input: mockInput });
      actor.start();

      actor.send({ type: "INPUT", text: "*********" });
      await new Promise(resolve => setTimeout(resolve, 10));

      // Invalid PIN format
      actor.send({ type: "INPUT", text: "abc" });
      expect(actor.getSnapshot().value).toBe("pinEntry");
      expect(actor.getSnapshot().context.message).toContain("Incorrect PIN");

      // Valid PIN format
      mockVerifyPin.mockReturnValue(true);
      actor.send({ type: "INPUT", text: "1234" });
      expect(actor.getSnapshot().value).toBe("verifyingPin");
    });

    it("should handle successful PIN verification", async () => {
      mockVerifyPin.mockReturnValue(true);

      const actor = createActor(loginMachine, { input: mockInput });
      actor.start();

      actor.send({ type: "INPUT", text: "*********" });
      await new Promise(resolve => setTimeout(resolve, 10));

      actor.send({ type: "INPUT", text: "1234" });
      await new Promise(resolve => setTimeout(resolve, 10));

      expect(actor.getSnapshot().value).toBe("loginSuccess");
      expect((actor.getSnapshot().output as any)?.result).toBe(
        LoginOutput.LOGIN_SUCCESS
      );
    });

    it("should handle incorrect PIN with retry", async () => {
      mockVerifyPin.mockReturnValue(false);

      const actor = createActor(loginMachine, { input: mockInput });
      actor.start();

      actor.send({ type: "INPUT", text: "*********" });
      await new Promise(resolve => setTimeout(resolve, 10));

      actor.send({ type: "INPUT", text: "1234" });
      await new Promise(resolve => setTimeout(resolve, 10));

      expect(actor.getSnapshot().value).toBe("pinEntry");
      expect(actor.getSnapshot().context.pinAttempts).toBe(1);
      expect(actor.getSnapshot().context.message).toContain("(1/3)");
    });

    it("should handle max attempts exceeded", async () => {
      mockVerifyPin.mockReturnValue(false);
      mockProgressiveDataService.clearCustomerPin.mockResolvedValue();

      const actor = createActor(loginMachine, { input: mockInput });
      actor.start();

      actor.send({ type: "INPUT", text: "*********" });
      await new Promise(resolve => setTimeout(resolve, 10));

      // First two attempts
      actor.send({ type: "INPUT", text: "1234" });
      await new Promise(resolve => setTimeout(resolve, 10));

      actor.send({ type: "INPUT", text: "1234" });
      await new Promise(resolve => setTimeout(resolve, 10));

      // Third attempt should trigger max attempts exceeded
      actor.send({ type: "INPUT", text: "1234" });
      await new Promise(resolve => setTimeout(resolve, 10));

      expect(actor.getSnapshot().value).toBe("maxAttemptsExceeded");
      expect((actor.getSnapshot().output as any)?.result).toBe(
        LoginOutput.MAX_ATTEMPTS_EXCEEDED
      );
      expect(mockProgressiveDataService.clearCustomerPin).toHaveBeenCalledWith(
        "*********"
      );
    });
  });

  describe("Error Handling", () => {
    it("should handle database errors gracefully", async () => {
      mockProgressiveDataService.getCustomerByCustomerId.mockRejectedValue(
        new Error("Database connection failed")
      );

      const actor = createActor(loginMachine, { input: mockInput });
      actor.start();

      actor.send({ type: "INPUT", text: "*********" });
      await new Promise(resolve => setTimeout(resolve, 10));

      expect(actor.getSnapshot().value).toBe("error");
      expect(actor.getSnapshot().context.message).toBe(
        "System error. Please try again."
      );
    });

    it("should handle ERROR events", () => {
      const actor = createActor(loginMachine, { input: mockInput });
      actor.start();

      actor.send({ type: "ERROR", error: "Network error" });
      expect(actor.getSnapshot().value).toBe("error");
      expect(actor.getSnapshot().context.error).toBe("Network error");
    });
  });

  describe("Navigation and Cancellation", () => {
    it("should handle back navigation to cancelled state", () => {
      const actor = createActor(loginMachine, { input: mockInput });
      actor.start();

      actor.send({ type: "INPUT", text: "0" });
      expect(actor.getSnapshot().value).toBe("cancelled");
      expect((actor.getSnapshot().output as any)?.result).toBe(
        LoginOutput.CANCELLED
      );
    });

    it("should handle exit navigation to cancelled state", () => {
      const actor = createActor(loginMachine, { input: mockInput });
      actor.start();

      actor.send({ type: "INPUT", text: "*" });
      expect(actor.getSnapshot().value).toBe("cancelled");
      expect((actor.getSnapshot().output as any)?.result).toBe(
        LoginOutput.CANCELLED
      );
    });
  });

  describe("Machine Output", () => {
    it("should provide correct output for successful login", async () => {
      mockProgressiveDataService.getCustomerByCustomerId.mockResolvedValue(
        mockCustomer
      );
      mockVerifyPin.mockReturnValue(true);

      const actor = createActor(loginMachine, { input: mockInput });
      actor.start();

      actor.send({ type: "INPUT", text: "*********" });
      await new Promise(resolve => setTimeout(resolve, 10));

      actor.send({ type: "INPUT", text: "1234" });
      await new Promise(resolve => setTimeout(resolve, 10));

      const output = actor.getSnapshot().output as any;
      expect(output?.result).toBe(LoginOutput.LOGIN_SUCCESS);
      expect(output?.customerId).toBe("*********");
      expect(output?.customer).toEqual(mockCustomer);
    });

    it("should provide correct output for different failure scenarios", () => {
      const actor = createActor(loginMachine, { input: mockInput });
      actor.start();

      // Test cancellation
      actor.send({ type: "INPUT", text: "*" });
      expect((actor.getSnapshot().output as any)?.result).toBe(
        LoginOutput.CANCELLED
      );
    });
  });
});
