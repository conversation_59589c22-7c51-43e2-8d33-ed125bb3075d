import { assign, from<PERSON>rom<PERSON>, sendTo, setup } from "xstate";
import type {
  <PERSON><PERSON><PERSON><PERSON>,
  AgentVerificationInput,
  AgentVerificationResult,
} from "../../services/agent-verification.js";
import type {
  WalletVerificationInput,
  WalletVerificationResult,
} from "../../services/wallet-verification.js";
import { accountCreationMachine } from "./account-creation/index.js";
import {
  accountMenuMachine,
  AccountMenuOutput,
} from "./account-menu/accountMenuMachine.js";
import { loginMachine, LoginOutput } from "./account-menu/loginMachine.js";
import { agentMachine } from "./agent/index.js";
import { navigationGuards } from "./guards/index.js";
import { knowMoreMachine } from "./information/index.js";
import { userServicesMachine } from "./user-services/index.js";
import { withNavigation } from "./utils/navigation-mixin.js";

/**
 * SupaMoto Wallet State Machine - Modular Architecture
 *
 * Main orchestrator machine that implements the USSD menu flows from:
 * docs/requirements/USSD-menu-mermaid.md
 *
 * Coordinates between three main service domains:
 * 1. Information services (Know More flows) - knowMoreMachine
 * 2. User services (authenticated user workflows) - userServicesMachine
 * 3. Agent services (agent-specific workflows) - agentMachine
 *
 * Flow:
 * Start → *2233# Pre-Menu → [Know More | Enter Wallet ID | Agent Menu]
 * - Know More → Information flows (SMS responses)
 * - Wallet ID → Verify → User Menu (authenticated services)
 * - Agent Menu → Agent ID → Verify → Agent Services
 */

export interface SupamotoWalletContext {
  sessionId: string;
  phoneNumber: string;
  serviceCode: string;
  walletId?: string;
  customerName?: string;
  agentId?: string;
  agentName?: string;
  agentLevel?: AgentLevel;
  currentBalance?: number;
  isAuthenticated: boolean;
  isAgent: boolean;
  sessionStartTime: string;
  error?: string;
  validationError?: string;
  // USSD Response
  message: string;
}

export type SupamotoWalletEvent =
  | { type: "DIAL_USSD"; phoneNumber: string; serviceCode: string }
  | { type: "INPUT"; input: string } // User input from USSD
  | { type: "SUBMIT_WALLET_ID"; walletId: string }
  | { type: "SUBMIT_AGENT_ID"; agentId: string }
  | { type: "CHILD_MACHINE_DONE"; output: any }
  | { type: "BACK" }
  | { type: "CANCEL" }
  | { type: "ERROR"; error: string };

const preMenuMessage =
  "Welcome to SupaMoto\n1. Know More\n2. Account Menu\n3. Agent Menu\n*. Exit";
export const supamotoWalletMachine = setup({
  types: {
    context: {} as SupamotoWalletContext,
    events: {} as SupamotoWalletEvent,
    input: {} as {
      sessionId?: string;
      phoneNumber?: string;
      serviceCode?: string;
    },
  },

  actors: {
    // Child state machines
    knowMoreMachine,
    userServicesMachine,
    agentMachine,
    accountMenuMachine,
    loginMachine,
    accountCreationMachine,

    verifyWallet: fromPromise(
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      async ({ input }: { input: WalletVerificationInput }) => {
        throw new Error(
          "verifyWallet actor not provided. Use machine.provide({ actors: { verifyWallet } })"
        );
      }
    ),

    verifyAgent: fromPromise(
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      async ({ input }: { input: AgentVerificationInput }) => {
        throw new Error(
          "verifyAgent actor not provided. Use machine.provide({ actors: { verifyAgent } })"
        );
      }
    ),
  },

  actions: {
    initializeSession: assign(({ context }) => ({
      sessionId:
        context.sessionId ||
        `session_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
      sessionStartTime: new Date().toISOString(),
      isAuthenticated: false,
      isAgent: false,
      error: undefined,
      validationError: undefined,
    })),

    setPhoneAndService: assign(({ event }) => ({
      phoneNumber: event.type === "DIAL_USSD" ? event.phoneNumber : "",
      serviceCode: event.type === "DIAL_USSD" ? event.serviceCode : "",
      message: preMenuMessage,
    })),

    setPreMenuMessage: assign(() => ({
      message: preMenuMessage,
    })),

    setWalletIdEntryMessage: assign(() => ({
      message: "Enter your Wallet ID:",
    })),

    setAgentIdEntryMessage: assign(() => ({
      message: "Enter your Agent ID:",
    })),

    setVerifyingMessage: assign(() => ({
      message: "Verifying... Please wait.",
    })),

    setErrorMessage: assign(({ context }) => ({
      message:
        context.error ||
        context.validationError ||
        "An error occurred. Please try again.",
    })),

    setWalletId: assign(({ event }) => ({
      walletId: event.type === "SUBMIT_WALLET_ID" ? event.walletId : undefined,
    })),

    setAgentId: assign(({ event }) => ({
      agentId: event.type === "SUBMIT_AGENT_ID" ? event.agentId : undefined,
    })),

    setUserAuthenticated: assign(() => ({
      isAuthenticated: true,
      isAgent: false,
    })),

    setAgentAuthenticated: assign(() => ({
      isAuthenticated: true,
      isAgent: true,
    })),

    updateBalanceFromChild: assign(({ context, event }) => ({
      currentBalance:
        event.type === "CHILD_MACHINE_DONE"
          ? event.output?.context?.currentBalance !== undefined
            ? event.output.context.currentBalance
            : context.currentBalance
          : context.currentBalance,
    })),

    setError: assign(({ event }) => ({
      error: event.type === "ERROR" ? event.error : "An error occurred",
    })),

    clearErrors: assign(() => ({
      error: undefined,
      validationError: undefined,
    })),

    resetSession: assign(() => ({
      walletId: undefined,
      customerName: undefined,
      agentId: undefined,
      agentName: undefined,
      agentLevel: undefined,
      currentBalance: undefined,
      isAuthenticated: false,
      isAgent: false,
      error: undefined,
      validationError: undefined,
    })),

    logToConsole: ({ event, context }) => {
      let details = `👀 Parent log: context: ${JSON.stringify(context)} \n| event: ${event.type}`;
      if ("input" in event) {
        details += ` | input: ${event.input}`;
      }
      if ("error" in event) {
        details += ` | error: ${event.error}`;
      }
      /* eslint-disable no-console*/
      console.log(details);
    },
  },

  guards: {
    // Business logic guards (machine-specific)
    hasValidWalletId: ({ event }) =>
      event.type === "SUBMIT_WALLET_ID" &&
      Boolean(event.walletId && event.walletId.length >= 8),

    hasValidAgentId: ({ event }) =>
      event.type === "SUBMIT_AGENT_ID" &&
      Boolean(event.agentId && event.agentId.length >= 6),

    hasError: ({ context }) => Boolean(context.error),
    hasValidationError: ({ context }) => Boolean(context.validationError),

    // Navigation guards - these handle universal commands
    isBack: ({ event }) =>
      navigationGuards.isBackCommand(null as any, event as any),
    isExit: ({ event }) =>
      navigationGuards.isExitCommand(null as any, event as any),

    // Navigation guards (adapted from modular guards)
    isInput1: ({ event }) =>
      navigationGuards.isInput("1")(null as any, event as any),
    isInput2: ({ event }) =>
      navigationGuards.isInput("2")(null as any, event as any),
    isInput3: ({ event }) =>
      navigationGuards.isInput("3")(null as any, event as any),

    // Basic input validation guards
    isValidWalletIdInput: ({ event }) =>
      event.type === "INPUT" && event.input.trim().length >= 8,
    isValidAgentIdInput: ({ event }) =>
      event.type === "INPUT" && event.input.trim().length >= 6,
  },
}).createMachine({
  id: "supamotoWalletMachine",
  initial: "idle",

  context: ({ input }) => ({
    sessionId: input?.sessionId || "",
    phoneNumber: input?.phoneNumber || "",
    serviceCode: input?.serviceCode || "",
    isAuthenticated: false,
    isAgent: false,
    sessionStartTime: "",
    message: preMenuMessage,
  }),

  states: {
    // Initial state - waiting for USSD dial
    idle: {
      on: {
        DIAL_USSD: {
          target: "preMenu",
          actions: ["initializeSession", "setPhoneAndService"],
        },
      },
    },

    preMenu: {
      entry: ["clearErrors", "setPreMenuMessage"],
      on: {
        INPUT: withNavigation(
          [
            {
              target: "knowMoreService",
              guard: "isInput1",
              actions: "clearErrors",
            },
            {
              target: "accountMenu",
              guard: "isInput2",
              actions: "clearErrors",
            },
            {
              target: "agentIdEntry",
              guard: "isInput3",
              actions: "clearErrors",
            },
            {
              target: "closeSession",
              guard: "isBack",
            },
            {
              target: "closeSession",
              guard: "isExit",
            },
            {
              target: "preMenu",
              actions: assign(() => ({
                message: `Invalid selection. Please choose 1, 2, or 3.\n\n${preMenuMessage}`,
              })),
            },
          ],
          {
            enableBack: false, // No back from main menu
            enableExit: true, // But allow exit
          }
        ),
        ERROR: {
          target: "error",
          actions: "setError",
        },
      },
    },

    // Know More service - delegates to knowMoreMachine
    knowMoreService: {
      on: {
        INPUT: { actions: sendTo("knowMoreChild", ({ event }) => event) },
      },
      invoke: {
        id: "knowMoreChild",
        src: "knowMoreMachine",
        input: ({ context }) => ({
          sessionId: context.sessionId,
          phoneNumber: context.phoneNumber,
          serviceCode: context.serviceCode,
        }),
        onDone: {
          target: "preMenu",
        },
        onError: {
          target: "error",
          actions: "setError",
        },
      },
    },

    // Account Menu - routes to login or account creation
    accountMenu: {
      on: {
        INPUT: {
          actions: [sendTo("accountMenuChild", ({ event }) => event)],
        },
      },
      invoke: {
        id: "accountMenuChild",
        src: "accountMenuMachine",
        input: ({ context }) => ({
          sessionId: context.sessionId,
          phoneNumber: context.phoneNumber,
          serviceCode: context.serviceCode,
        }),
        onDone: [
          {
            target: "login",
            guard: ({ event }) =>
              (event as { output?: { result: AccountMenuOutput } }).output
                ?.result === AccountMenuOutput.LOGIN_SELECTED,
          },
          {
            target: "accountCreation",
            guard: ({ event }) =>
              (event as { output?: { result: AccountMenuOutput } }).output
                ?.result === AccountMenuOutput.CREATE_SELECTED,
          },
          {
            target: "preMenu",
            guard: ({ event }) =>
              (event as { output?: { result: AccountMenuOutput } }).output
                ?.result === AccountMenuOutput.UNDEFINED,
          },
          {
            target: "preMenu",
          },
        ],
        onError: {
          target: "error",
          actions: "setError",
        },
        onSnapshot: {
          actions: assign(({ event }) => ({
            message: event.snapshot.context.message,
          })),
        },
      },
    },

    // Login service - handles existing user authentication
    login: {
      on: {
        INPUT: {
          actions: sendTo("loginChild", ({ event }) => event),
        },
      },
      invoke: {
        id: "loginChild",
        src: "loginMachine",
        input: ({ context }) => ({
          sessionId: context.sessionId,
          phoneNumber: context.phoneNumber,
          serviceCode: context.serviceCode,
        }),
        onDone: [
          {
            target: "userMainMenu",
            guard: ({ event }) =>
              (event.output as any)?.result === LoginOutput.LOGIN_SUCCESS,
            actions: [
              "clearErrors",
              assign(({ event }) => {
                const output = event.output as any;
                return {
                  walletId: output?.customerId || "",
                  customerName: output?.customer?.fullName || "Existing User",
                  isAuthenticated: true,
                };
              }),
            ],
          },
          {
            target: "accountMenu",
            guard: ({ event }) =>
              (event.output as any)?.result === LoginOutput.CUSTOMER_NOT_FOUND,
            actions: ["clearErrors"],
          },
          {
            target: "accountMenu",
            guard: ({ event }) =>
              (event.output as any)?.result ===
              LoginOutput.ENCRYPTED_PIN_FIELD_EMPTY,
            actions: ["clearErrors"],
          },
          {
            target: "accountMenu",
            guard: ({ event }) =>
              (event.output as any)?.result ===
              LoginOutput.MAX_ATTEMPTS_EXCEEDED,
            actions: ["clearErrors"],
          },
          {
            target: "preMenu",
            guard: ({ event }) =>
              (event.output as any)?.result === LoginOutput.CANCELLED,
            actions: ["clearErrors"],
          },
        ],
        onError: {
          target: "error",
          actions: "setError",
        },
        onSnapshot: {
          actions: assign(({ event }) => ({
            message: event.snapshot.context.message,
          })),
        },
      },
    },

    // Account Creation service - handles new user registration
    accountCreation: {
      on: {
        INPUT: {
          actions: sendTo("accountCreationChild", ({ event }) => event),
        },
      },
      invoke: {
        id: "accountCreationChild",
        src: "accountCreationMachine",
        input: ({ context }) => ({
          sessionId: context.sessionId,
          phoneNumber: context.phoneNumber,
          serviceCode: context.serviceCode,
        }),
        onDone: [
          {
            target: "accountCreationSuccess",
            guard: ({ event }) =>
              (event.output as any)?.type === "ACCOUNT_CREATED",
            actions: [
              "clearErrors",
              assign(({ event }) => {
                const output = event.output as any;
                return {
                  customerName: output?.fullName || "",
                  walletId: output?.customerId || "",
                };
              }),
            ],
          },
          {
            target: "accountMenu",
            guard: ({ event }) => (event.output as any)?.type === "CANCELLED",
            actions: ["clearErrors"],
          },
        ],
        onError: {
          target: "error",
          actions: "setError",
        },
        onSnapshot: {
          actions: assign(({ event }) => ({
            message: event.snapshot.context.message,
          })),
        },
      },
    },

    // Account creation success state
    accountCreationSuccess: {
      entry: assign(() => ({
        message:
          "Account created successfully!\n\nYou can now:\n1. Enter Wallet ID to access services\n2. Return to main menu\n0. Back",
        isEnd: false,
      })),
      on: {
        INPUT: [
          {
            target: "walletIdEntry",
            guard: "isInput1",
            actions: "clearErrors",
          },
          {
            target: "preMenu",
            guard: "isInput2",
            actions: "clearErrors",
          },
          {
            target: "preMenu",
            guard: "isBack",
            actions: "clearErrors",
          },
        ],
      },
    },

    // Wallet ID entry for user authentication
    walletIdEntry: {
      entry: "setWalletIdEntryMessage",
      on: {
        INPUT: withNavigation([
          {
            target: "verifyingWallet",
            guard: "isValidWalletIdInput",
            actions: [
              assign(({ event }) => ({ walletId: event.input })),
              "setVerifyingMessage",
            ],
          },
          {
            target: "walletIdEntry",
            actions: assign(() => ({
              validationError:
                "Please enter a valid wallet ID (minimum 8 characters)",
              message:
                "Invalid wallet ID. Please enter a valid wallet ID (minimum 8 characters):\n\n0. Back\n*. Exit",
            })),
          },
        ]),
        SUBMIT_WALLET_ID: [
          {
            target: "verifyingWallet",
            actions: "setWalletId",
            guard: "hasValidWalletId",
          },
          {
            target: "walletIdEntry",
            actions: assign(() => ({
              validationError:
                "Please enter a valid wallet ID (minimum 8 characters)",
              message:
                "Invalid wallet ID. Please enter a valid wallet ID (minimum 8 characters):\n\n0. Back\n*. Exit",
            })),
          },
        ],
        BACK: "preMenu",
        ERROR: {
          target: "error",
          actions: "setError",
        },
      },
    },

    // Verifying wallet ID - "Verify that MSISDN belongs to Wallet ID"
    verifyingWallet: {
      entry: "setVerifyingMessage",
      invoke: {
        src: "verifyWallet",
        input: ({ context }) => ({
          walletId: context.walletId || "",
          phoneNumber: context.phoneNumber,
        }),
        onDone: {
          target: "userMainMenu",
          actions: [
            assign(({ event }) => ({
              isAuthenticated: true,
              isAgent: false,
              customerName: (event.output as WalletVerificationResult)
                .customerName,
              currentBalance: (event.output as WalletVerificationResult)
                .balance,
            })),
            "clearErrors",
          ],
        },
        onError: {
          target: "walletIdEntry",
          actions: assign(() => ({
            validationError: "Invalid wallet ID. Please try again.",
            message:
              "Invalid wallet ID. Please try again.\nEnter your Wallet ID:\n\n0. Back\n*. Exit",
          })),
        },
      },
      // No INPUT handlers - user can't interrupt verification
    },

    // Agent ID entry for agent authentication
    agentIdEntry: {
      entry: "setAgentIdEntryMessage",
      on: {
        INPUT: withNavigation([
          {
            target: "verifyingAgent",
            guard: "isValidAgentIdInput",
            actions: [
              assign(({ event }) => ({ agentId: event.input })),
              "setVerifyingMessage",
            ],
          },
          {
            target: "agentIdEntry",
            actions: assign(() => ({
              validationError:
                "Please enter a valid agent ID (minimum 6 characters)",
              message:
                "Invalid agent ID. Please enter a valid agent ID (minimum 6 characters):\n\n0. Back\n*. Exit",
            })),
          },
        ]),
        SUBMIT_AGENT_ID: [
          {
            target: "verifyingAgent",
            actions: "setAgentId",
            guard: "hasValidAgentId",
          },
          {
            target: "agentIdEntry",
            actions: assign(() => ({
              validationError:
                "Please enter a valid agent ID (minimum 6 characters)",
              message:
                "Invalid agent ID. Please enter a valid agent ID (minimum 6 characters):\n\n0. Back\n*. Exit",
            })),
          },
        ],
        BACK: "preMenu",
        ERROR: {
          target: "error",
          actions: "setError",
        },
      },
    },

    // Verifying agent ID - "Verify Agent"
    verifyingAgent: {
      entry: "setVerifyingMessage",
      invoke: {
        src: "verifyAgent",
        input: ({ context }) => ({
          agentId: context.agentId || "",
          phoneNumber: context.phoneNumber,
        }),
        onDone: {
          target: "agentMainMenu",
          actions: [
            assign(({ event }) => ({
              isAuthenticated: true,
              isAgent: true,
              agentName: (event.output as AgentVerificationResult).agentName,
              agentLevel: (event.output as AgentVerificationResult).agentLevel,
            })),
            "clearErrors",
          ],
        },
        onError: {
          target: "agentIdEntry",
          actions: assign(() => ({
            validationError: "Invalid agent ID. Please try again.",
            message:
              "Invalid agent ID. Please try again.\nEnter your Agent ID:",
          })),
        },
      },
    },

    // User main menu - authenticated user services
    userMainMenu: {
      on: {
        INPUT: {
          actions: sendTo("userServicesChild", ({ event }) => event),
        },
      },
      invoke: {
        id: "userServicesChild",
        src: "userServicesMachine",
        input: ({ context }) => ({
          sessionId: context.sessionId,
          phoneNumber: context.phoneNumber,
          serviceCode: context.serviceCode,
          walletId: context.walletId,
          customerName: context.customerName,
          currentBalance: context.currentBalance,
        }),
        onDone: {
          target: "userMainMenu",
          actions: ["updateBalanceFromChild", "clearErrors"],
        },
        onError: {
          target: "error",
          actions: "setError",
        },
      },
    },

    // Agent main menu - agent services
    agentMainMenu: {
      on: {
        INPUT: {
          actions: sendTo("agentChild", ({ event }) => event),
        },
      },
      invoke: {
        id: "agentChild",
        src: "agentMachine",
        input: ({ context }) => ({
          sessionId: context.sessionId,
          phoneNumber: context.phoneNumber,
          serviceCode: context.serviceCode,
          agentId: context.agentId,
          agentName: context.agentName,
          agentLevel: context.agentLevel,
        }),
        onDone: {
          target: "agentMainMenu",
          actions: ["clearErrors"],
        },
        onError: {
          target: "error",
          actions: "setError",
        },
      },
    },

    // Error state with custom back behavior
    error: {
      entry: "setErrorMessage",
      on: {
        INPUT: withNavigation([], {
          backTarget: "preMenu", // Always go back to main menu from error
          enableBack: true,
          enableExit: true,
        }),
        DIAL_USSD: {
          target: "preMenu",
          actions: ["resetSession", "setPhoneAndService", "clearErrors"],
        },
      },
    },

    // Session closed - final state
    closeSession: {
      type: "final",
      entry: [
        "resetSession",
        assign(() => ({
          message: "Thank you for using SupaMoto Wallet. Goodbye!",
        })),
      ],
    },
  },
});

export type SupamotoWalletMachine = typeof supamotoWalletMachine;
