import { setup, assign } from "xstate";
import { withNavigation } from "../utils/navigation-mixin.js";
import { navigationGuards } from "../guards/navigation.guards.js";
import { NavigationPatterns } from "../utils/navigation-patterns.js";

/**
 * Know More Machine - Information Request and Product Information
 *
 * Handles:
 * - Product information display
 * - Service information requests
 * - Educational content delivery
 * - Navigation back to main menu
 *
 * Entry Points: Automatic invocation (starts in infoMenu)
 * Exit Points: Outputs routing decision for main orchestrator
 */

export interface KnowMoreContext {
  sessionId: string;
  phoneNumber: string;
  serviceCode: string;
  message: string; // USSD message to display to user
  error?: string;
}

export type KnowMoreEvent =
  | { type: "INPUT"; input: string } // User input from USSD
  | { type: "ERROR"; error: string };

const infoMenuMessage =
  "Welcome to SupaMoto Information Center\n1. Product Information\n2. Service Information\n3. About SupaMoto\n4. Contact Information";

export const knowMoreMachine = setup({
  types: {
    context: {} as KnowMoreContext,
    events: {} as KnowMoreEvent,
    input: {} as {
      sessionId: string;
      phoneNumber: string;
      serviceCode: string;
    },
  },

  actions: {
    setInfoMenuMessage: assign(() => ({
      message: infoMenuMessage,
    })),

    loadProductInfo: assign({
      message:
        "SupaMoto Products:\nAll products are carbon-verified and contribute to environmental impact.\n\n1. Solar Home Systems\n2. Clean Cookstoves\n3. Water Purification\n4. LED Lighting\n0. Back",
    }),

    loadServiceInfo: assign({
      message:
        "SupaMoto Services:\nAccess all services via *2233#\n\n1. Wallet Management\n2. Top-up Services\n3. Purchase Products\n4. Order Tracking\n5. Performance Monitoring\n0. Back",
    }),

    loadAboutInfo: assign({
      message:
        "About SupaMoto:\nSupaMoto is a leading provider of clean energy solutions in Africa. We offer carbon-verified products that help communities access clean energy.\n0. Back",
    }),

    loadContactInfo: assign({
      message:
        "Contact SupaMoto:\nCustomer Service: +260-XXX-XXXX\nEmail: <EMAIL>\nWebsite: www.supamoto.com\nOffice Hours: Mon-Fri 8AM-5PM\n0. Back",
    }),

    setError: assign({
      error: ({ event }) =>
        event.type === "ERROR" ? event.error : "An error occurred",
    }),

    clearErrors: assign({
      error: undefined,
    }),
  },

  guards: {
    // Input guards for USSD navigation
    isInput1: ({ event }) =>
      navigationGuards.isInput("1")(null as any, event as any),
    isInput2: ({ event }) =>
      navigationGuards.isInput("2")(null as any, event as any),
    isInput3: ({ event }) =>
      navigationGuards.isInput("3")(null as any, event as any),
    isInput4: ({ event }) =>
      navigationGuards.isInput("4")(null as any, event as any),

    // Navigation guards
    isBack: ({ event }) =>
      navigationGuards.isBackCommand(null as any, event as any),
    isExit: ({ event }) =>
      navigationGuards.isExitCommand(null as any, event as any),
  },
}).createMachine({
  id: "knowMoreMachine",
  initial: "infoMenu",

  context: ({ input }) => ({
    sessionId: input?.sessionId || "",
    phoneNumber: input?.phoneNumber || "",
    serviceCode: input?.serviceCode || "",
    message: infoMenuMessage,
    error: undefined,
  }),

  states: {
    // Main information menu
    infoMenu: {
      entry: "setInfoMenuMessage",
      on: {
        INPUT: withNavigation(
          [
            {
              target: "displayingInfo",
              guard: "isInput1",
              actions: ["loadProductInfo"],
            },
            {
              target: "displayingInfo",
              guard: "isInput2",
              actions: ["loadServiceInfo"],
            },
            {
              target: "displayingInfo",
              guard: "isInput3",
              actions: ["loadAboutInfo"],
            },
            {
              target: "displayingInfo",
              guard: "isInput4",
              actions: ["loadContactInfo"],
            },
          ],
          NavigationPatterns.informationChild
        ),
        ERROR: {
          target: "error",
          actions: "setError",
        },
      },
    },

    // Displaying information content
    displayingInfo: {
      on: {
        INPUT: withNavigation([], {
          backTarget: "infoMenu",
          exitTarget: "routeToMain",
          enableBack: true,
          enableExit: false,
        }),
        ERROR: {
          target: "error",
          actions: "setError",
        },
      },
    },

    // Error state
    error: {
      entry: "setError",
      on: {
        INPUT: withNavigation([], {
          backTarget: "infoMenu",
          exitTarget: "routeToMain",
        }),
      },
    },

    // Route back to main menu
    routeToMain: {
      type: "final",
      action: "clearErrors",
    },
  },
});

export type KnowMoreMachine = typeof knowMoreMachine;
