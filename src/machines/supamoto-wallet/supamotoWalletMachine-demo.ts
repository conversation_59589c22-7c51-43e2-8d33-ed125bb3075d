/* eslint-disable no-console */
import { createActor } from "xstate";
import { supamotoWalletMachine } from "./supamotoWalletMachine.js";

/**
 * SupaMoto Wallet Machine Demo - Main Orchestrator
 *
 * Demonstrates the main orchestrator machine functionality including:
 * - USSD session initialization (*2233# Pre-Menu)
 * - Service routing (Know More, User Services, Agent Services)
 * - Authentication flows (Wallet ID and Agent ID verification)
 * - Child machine coordination and output handling
 * - Error handling and session management
 *
 * Based on: docs/requirements/USSD-menu-mermaid.md
 */

console.log("🚀 SupaMoto Wallet Machine Demo - Main Orchestrator\n");

// Demo 1: USSD Session Initialization and Pre-Menu
console.log("=".repeat(60));
console.log("DEMO 1: USSD Session Initialization and Pre-Menu");
console.log("=".repeat(60));

const actor1 = createActor(supamotoWalletMachine, { input: {} });
actor1.subscribe(snapshot => {
  console.log(`📍 State: ${snapshot.value}`);
  console.log(`📱 Phone: ${snapshot.context.phoneNumber || "Not set"}`);
  console.log(`🔢 Service Code: ${snapshot.context.serviceCode || "Not set"}`);
  console.log(`🆔 Session ID: ${snapshot.context.sessionId || "Not set"}`);
  if (snapshot.context.sessionStartTime) {
    console.log(
      `⏰ Session Started: ${new Date(snapshot.context.sessionStartTime).toLocaleTimeString()}`
    );
  }
  if (snapshot.context.error) {
    console.log(`❌ Error: ${snapshot.context.error}`);
  }
  if (snapshot.context.validationError) {
    console.log(`⚠️  Validation Error: ${snapshot.context.validationError}`);
  }
  console.log("");
});

actor1.start();
console.log("📱 User dials *2233#");

actor1.send({
  type: "DIAL_USSD",
  phoneNumber: "+260987654321",
  serviceCode: "*2233#",
});

setTimeout(() => {
  console.log("✅ Pre-Menu demo complete!\n");

  // Demo 2: Know More Service Flow
  console.log("=".repeat(60));
  console.log("DEMO 2: Know More Service Flow");
  console.log("=".repeat(60));

  const actor2 = createActor(supamotoWalletMachine, { input: {} });
  actor2.subscribe(snapshot => {
    console.log(`📍 State: ${snapshot.value}`);
    console.log(`💬 Message: ${snapshot.context.message}`);
    console.log("");
  });

  actor2.start();
  actor2.send({
    type: "DIAL_USSD",
    phoneNumber: "+260987654321",
    serviceCode: "*2233#",
  });
  console.log("📱 User dials *2233#");

  setTimeout(() => {
    actor2.send({ type: "SELECT_1" });
    console.log("📱 User selects 1. Know More");

    setTimeout(() => {
      console.log("✅ Know More service demo complete!\n");

      // Demo 3: User Authentication and Services
      console.log("=".repeat(60));
      console.log("DEMO 3: User Authentication and Services");
      console.log("=".repeat(60));

      const actor3 = createActor(supamotoWalletMachine, { input: {} });
      actor3.subscribe(snapshot => {
        console.log(`📍 State: ${snapshot.value}`);
        console.log(`🔐 Authenticated: ${snapshot.context.isAuthenticated}`);
        console.log(`👤 Is Agent: ${snapshot.context.isAgent}`);
        if (snapshot.context.walletId) {
          console.log(`💳 Wallet ID: ${snapshot.context.walletId}`);
        }
        if (snapshot.context.currentBalance !== undefined) {
          console.log(
            `💰 Balance: K${snapshot.context.currentBalance.toFixed(2)}`
          );
        }
        if (snapshot.context.validationError) {
          console.log(
            `⚠️  Validation Error: ${snapshot.context.validationError}`
          );
        }
        console.log("");
      });

      actor3.start();
      actor3.send({
        type: "DIAL_USSD",
        phoneNumber: "+260987654321",
        serviceCode: "*2233#",
      });
      console.log("📱 User dials *2233#");

      setTimeout(() => {
        actor3.send({ type: "SELECT_2" });
        console.log("📱 User selects 2. Enter Wallet ID");

        setTimeout(() => {
          // Test invalid wallet ID first
          actor3.send({ type: "SUBMIT_WALLET_ID", walletId: "C123" });
          console.log("📱 User enters invalid wallet ID: C123");

          setTimeout(() => {
            // Now test valid wallet ID
            actor3.send({ type: "SUBMIT_WALLET_ID", walletId: "C21009802" });
            console.log("📱 User enters valid wallet ID: C21009802");

            setTimeout(() => {
              console.log("✅ User authentication demo complete!\n");

              // Demo 4: Agent Authentication and Services
              console.log("=".repeat(60));
              console.log("DEMO 4: Agent Authentication and Services");
              console.log("=".repeat(60));

              const actor4 = createActor(supamotoWalletMachine, { input: {} });
              actor4.subscribe(snapshot => {
                console.log(`📍 State: ${snapshot.value}`);
                console.log(
                  `🔐 Authenticated: ${snapshot.context.isAuthenticated}`
                );
                console.log(`👤 Is Agent: ${snapshot.context.isAgent}`);
                if (snapshot.context.agentId) {
                  console.log(`🏢 Agent ID: ${snapshot.context.agentId}`);
                }
                if (snapshot.context.agentLevel) {
                  console.log(`⭐ Agent Level: ${snapshot.context.agentLevel}`);
                }
                if (snapshot.context.validationError) {
                  console.log(
                    `⚠️  Validation Error: ${snapshot.context.validationError}`
                  );
                }
                console.log("");
              });

              actor4.start();
              actor4.send({
                type: "DIAL_USSD",
                phoneNumber: "+260123456789",
                serviceCode: "*2233#",
              });
              console.log("📱 Agent dials *2233#");

              setTimeout(() => {
                actor4.send({ type: "SELECT_3" });
                console.log("📱 Agent selects 3. Agent Menu");

                setTimeout(() => {
                  // Test invalid agent ID first
                  actor4.send({ type: "SUBMIT_AGENT_ID", agentId: "A123" });
                  console.log("📱 Agent enters invalid agent ID: A123");

                  setTimeout(() => {
                    // Now test valid agent ID
                    actor4.send({ type: "SUBMIT_AGENT_ID", agentId: "AGT001" });
                    console.log("📱 Agent enters valid agent ID: AGT001");

                    setTimeout(() => {
                      console.log("✅ Agent authentication demo complete!\n");

                      // Demo 5: Error Handling and Recovery
                      console.log("=".repeat(60));
                      console.log("DEMO 5: Error Handling and Recovery");
                      console.log("=".repeat(60));

                      const actor5 = createActor(supamotoWalletMachine, {
                        input: {},
                      });
                      actor5.subscribe(snapshot => {
                        console.log(`📍 State: ${snapshot.value}`);
                        if (snapshot.context.error) {
                          console.log(`❌ Error: ${snapshot.context.error}`);
                        }
                        console.log("");
                      });

                      actor5.start();
                      actor5.send({
                        type: "DIAL_USSD",
                        phoneNumber: "+260987654321",
                        serviceCode: "*2233#",
                      });
                      console.log("📱 User dials *2233#");

                      setTimeout(() => {
                        actor5.send({
                          type: "ERROR",
                          error: "Network connection failed",
                        });
                        console.log("📱 Network error occurs");

                        setTimeout(() => {
                          actor5.send({
                            type: "DIAL_USSD",
                            phoneNumber: "+260111222333",
                            serviceCode: "*2233#",
                          });
                          console.log(
                            "📱 User redials *2233# after error recovery"
                          );

                          setTimeout(() => {
                            console.log("✅ Error handling demo complete!\n");

                            // Demo 6: Navigation and Session Management
                            console.log("=".repeat(60));
                            console.log(
                              "DEMO 6: Navigation and Session Management"
                            );
                            console.log("=".repeat(60));

                            const actor6 = createActor(supamotoWalletMachine, {
                              input: {},
                            });
                            actor6.subscribe(snapshot => {
                              console.log(`📍 State: ${snapshot.value}`);
                              if (snapshot.context.walletId) {
                                console.log(
                                  `💳 Wallet ID: ${snapshot.context.walletId}`
                                );
                              }
                              console.log("");
                            });

                            actor6.start();
                            actor6.send({
                              type: "DIAL_USSD",
                              phoneNumber: "+260987654321",
                              serviceCode: "*2233#",
                            });
                            console.log("📱 User dials *2233#");

                            setTimeout(() => {
                              actor6.send({ type: "SELECT_2" });
                              console.log("📱 User selects 2. Enter Wallet ID");

                              setTimeout(() => {
                                actor6.send({ type: "BACK" });
                                console.log(
                                  "📱 User navigates back to pre-menu"
                                );

                                setTimeout(() => {
                                  actor6.send({ type: "CLOSE_SESSION" });
                                  console.log("📱 User closes session");

                                  setTimeout(() => {
                                    console.log(
                                      "✅ Navigation and session management demo complete!\n"
                                    );

                                    console.log(
                                      "\n🎉 SupaMoto Wallet Machine Demo Complete!"
                                    );
                                    console.log("\n📊 Machine Summary:");
                                    console.log(
                                      "   • Main orchestrator for SupaMoto Wallet USSD system"
                                    );
                                    console.log(
                                      "   • Implements USSD menu flows from requirements diagram"
                                    );
                                    console.log(
                                      "   • Coordinates three service domains:"
                                    );
                                    console.log(
                                      "     - Information services (Know More flows)"
                                    );
                                    console.log(
                                      "     - User services (authenticated user workflows)"
                                    );
                                    console.log(
                                      "     - Agent services (agent-specific workflows)"
                                    );
                                    console.log(
                                      "   • Handles authentication for both users and agents"
                                    );
                                    console.log(
                                      "   • Provides robust error handling and recovery"
                                    );
                                    console.log(
                                      "   • Manages session lifecycle and context preservation"
                                    );
                                    console.log(
                                      "   • Validates input and provides user feedback"
                                    );
                                    console.log(
                                      "   • Routes between child machines based on user choices"
                                    );
                                    console.log(
                                      "   • Type-safe with XState v5 setup() pattern"
                                    );
                                    console.log(
                                      "   • Modular architecture replacing 1000+ line monolith"
                                    );
                                  }, 1000);
                                }, 1000);
                              }, 1000);
                            }, 1000);
                          }, 1000);
                        }, 1000);
                      }, 1000);
                    }, 1000);
                  }, 1000);
                }, 1000);
              }, 1000);
            }, 1200);
          }, 1000);
        }, 1000);
      }, 1000);
    }, 1500);
  }, 1000);
}, 1000);
