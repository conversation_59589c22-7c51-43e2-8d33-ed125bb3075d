/**
 * SupaMoto Wallet Machine Module - Modular Architecture
 *
 * Main entry point for the SupaMoto Wallet state machine and related components.
 * Organized by domain for better scalability and maintainability.
 *
 * Architecture:
 * - Main orchestrator machine coordinates between service domains
 * - Information services handle "Know More" flows
 * - User services handle authenticated user workflows
 * - Agent services handle agent-specific workflows
 * - Shared utilities and types support all domains
 */

// Main orchestrator machine - refactored modular architecture
export { supamotoWalletMachine } from "./supamotoWalletMachine.js";
export type {
  SupamotoWalletMachine,
  SupamotoWalletContext,
  SupamotoWalletEvent,
} from "./supamotoWalletMachine.js";

// Core machines - explicit exports for validation
export { welcomeMachine } from "./core/welcomeMachine.js";

// Information machines - explicit exports for validation
export { knowMoreMachine } from "./information/knowMoreMachine.js";

// User service machines - explicit exports for validation
export { userServicesMachine } from "./user-services/userServicesMachine.js";
export { topupMachine } from "./user-services/topupMachine.js";
export { purchaseMachine } from "./user-services/purchaseMachine.js";
export { orderMachine } from "./user-services/orderMachine.js";
export { faultMachine } from "./user-services/faultMachine.js";
export { performanceMachine } from "./user-services/performanceMachine.js";
export { voucherMachine } from "./user-services/voucherMachine.js";
export { accountMachine } from "./user-services/accountMachine.js";
export { ixoBalanceMachine } from "./user-services/ixoBalanceMachine.js";

// Agent machines - explicit exports for validation
export { agentMachine } from "./agent/agentMachine.js";

// Domain-based machine exports (barrel exports for convenience)
export * from "./core/index.js";
export * from "./information/index.js";
export * from "./user-services/index.js";
export * from "./agent/index.js";

// Shared utilities and types
export * from "./shared/index.js";
