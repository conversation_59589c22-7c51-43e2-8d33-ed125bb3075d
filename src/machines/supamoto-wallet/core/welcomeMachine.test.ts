import { describe, it, expect } from "vitest";
import { createActor } from "xstate";
import { welcomeMachine } from "./welcomeMachine.js";

describe("welcomeMachine - Smoke Tests", () => {
  const mockInput = {
    sessionId: "test-session-123",
    phoneNumber: "+260987654321",
    serviceCode: "*2233#",
    walletId: "*********",
    currentBalance: 100,
  };

  describe("Basic Functionality", () => {
    it("should create and start successfully", () => {
      const actor = createActor(welcomeMachine, { input: mockInput });
      actor.start();

      const snapshot = actor.getSnapshot();
      expect(snapshot).toBeDefined();
      expect(snapshot.context.sessionId).toBe("test-session-123");
    });

    it("should handle menu selections", () => {
      const actor = createActor(welcomeMachine, { input: mockInput });
      actor.start();

      actor.send({ type: "SELECT_1" });

      const snapshot = actor.getSnapshot();
      expect(snapshot.value).toBeDefined();
      expect(snapshot.context).toBeDefined();
    });

    it("should handle navigation events", () => {
      const actor = createActor(welcomeMachine, { input: mockInput });
      actor.start();

      actor.send({ type: "SELECT_2" });

      const snapshot = actor.getSnapshot();
      expect(snapshot).toBeDefined();
    });

    it("should handle back navigation", () => {
      const actor = createActor(welcomeMachine, { input: mockInput });
      actor.start();

      actor.send({ type: "BACK" });

      const snapshot = actor.getSnapshot();
      expect(snapshot).toBeDefined();
    });
  });
});
