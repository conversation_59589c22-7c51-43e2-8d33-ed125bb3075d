/* eslint-disable no-console */
import { createActor } from "xstate";
import { welcomeMachine } from "./welcomeMachine.js";

/**
 * Welcome Machine Demo
 *
 * Demonstrates the welcome machine functionality including:
 * - USSD session initiation
 * - Pre-menu navigation
 * - Wallet verification flow
 * - Routing decisions
 */

console.log("🚀 Welcome Machine Demo\n");

const mockInput = {
  sessionId: "demo-session-456",
  phoneNumber: "+************",
  serviceCode: "*2233#",
};

// Demo 1: Know More Flow
console.log("=".repeat(50));
console.log("DEMO 1: Know More Selection");
console.log("=".repeat(50));

const actor1 = createActor(welcomeMachine, { input: mockInput });
actor1.subscribe(snapshot => {
  console.log(`📍 State: ${snapshot.value}`);
  if (snapshot.output) {
    console.log(`🎯 Output:`, snapshot.output);
  }
});

actor1.start();
actor1.send({ type: "DIAL_USSD" });
actor1.send({ type: "SELECT_1" }); // Know More

console.log("✅ Know More route selected!\n");

// Demo 2: Wallet Verification Flow
console.log("=".repeat(50));
console.log("DEMO 2: Wallet Verification Success");
console.log("=".repeat(50));

const actor2 = createActor(welcomeMachine, { input: mockInput });
actor2.subscribe(snapshot => {
  console.log(`📍 State: ${snapshot.value}`);
  if (snapshot.context.walletId) {
    console.log(`💳 Wallet ID: ${snapshot.context.walletId}`);
  }
  if (snapshot.output) {
    console.log(`🎯 Output:`, snapshot.output);
  }
});

actor2.start();
actor2.send({ type: "DIAL_USSD" });
actor2.send({ type: "SELECT_2" }); // Enter Wallet ID
actor2.send({ type: "SUBMIT_WALLET_ID", walletId: "wallet-abc-123" });
actor2.send({ type: "VERIFICATION_SUCCESS" });

console.log("✅ Wallet verified and routed to user services!\n");

// Demo 3: Wallet Verification Failure and Retry
console.log("=".repeat(50));
console.log("DEMO 3: Wallet Verification Failure & Retry");
console.log("=".repeat(50));

const actor3 = createActor(welcomeMachine, { input: mockInput });
actor3.subscribe(snapshot => {
  console.log(`📍 State: ${snapshot.value}`);
  if (snapshot.context.walletId) {
    console.log(`💳 Wallet ID: ${snapshot.context.walletId}`);
  }
  console.log(`🔐 Verified: ${snapshot.context.isWalletVerified}`);
});

actor3.start();
actor3.send({ type: "DIAL_USSD" });
actor3.send({ type: "SELECT_2" });
actor3.send({ type: "SUBMIT_WALLET_ID", walletId: "invalid-wallet" });
actor3.send({ type: "VERIFICATION_FAILED" });
console.log("❌ First attempt failed, back to wallet entry");

actor3.send({ type: "SUBMIT_WALLET_ID", walletId: "valid-wallet-456" });
actor3.send({ type: "VERIFICATION_SUCCESS" });
console.log("✅ Second attempt successful!\n");

// Demo 4: Agent Menu Selection
console.log("=".repeat(50));
console.log("DEMO 4: Agent Menu Selection");
console.log("=".repeat(50));

const actor4 = createActor(welcomeMachine, { input: mockInput });
actor4.subscribe(snapshot => {
  console.log(`📍 State: ${snapshot.value}`);
  if (snapshot.output) {
    console.log(`🎯 Output:`, snapshot.output);
  }
});

actor4.start();
actor4.send({ type: "DIAL_USSD" });
actor4.send({ type: "SELECT_3" }); // Agent Menu

console.log("✅ Agent route selected!\n");

// Demo 5: Back Navigation and Session Closure
console.log("=".repeat(50));
console.log("DEMO 5: Back Navigation & Session Closure");
console.log("=".repeat(50));

const actor5 = createActor(welcomeMachine, { input: mockInput });
actor5.subscribe(snapshot => {
  console.log(`📍 State: ${snapshot.value}`);
  if (snapshot.output) {
    console.log(`🎯 Output:`, snapshot.output);
  }
});

actor5.start();
actor5.send({ type: "DIAL_USSD" });
actor5.send({ type: "SELECT_2" });
console.log("📱 User navigated to wallet entry");

actor5.send({ type: "BACK" });
console.log("⬅️ User pressed back, returned to pre-menu");

actor5.send({ type: "BACK" });
console.log("⬅️ User pressed back again, session closed");

console.log("\n🎉 Welcome Machine Demo Complete!");
console.log("\n📊 Machine Summary:");
console.log("   • Handles USSD session initiation");
console.log("   • Manages pre-menu navigation");
console.log("   • Processes wallet verification");
console.log("   • Outputs routing decisions for main orchestrator");
console.log("   • Supports proper back navigation");
console.log("   • Type-safe with XState v5 setup() pattern");
