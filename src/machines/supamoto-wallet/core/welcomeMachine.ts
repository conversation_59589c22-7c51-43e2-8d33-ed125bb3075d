import { setup, assign } from "xstate";

/**
 * Welcome Machine - Entry Point and Initial Routing
 *
 * Handles:
 * - USSD session initiation (*2233#)
 * - Pre-menu navigation
 * - Wallet ID entry and verification
 * - Initial routing decisions
 *
 * Entry Points: DIAL_USSD
 * Exit Points: Outputs routing decision for main orchestrator
 */

export interface WelcomeContext {
  sessionId: string;
  phoneNumber: string;
  serviceCode: string;
  walletId?: string;
  isWalletVerified: boolean;
  selectedOption?: "1" | "2" | "3"; // knowMore | userServices | agent
}

export type WelcomeEvent =
  | { type: "DIAL_USSD" }
  | { type: "SELECT_1" } // Know More
  | { type: "SELECT_2" } // Enter Wallet ID
  | { type: "SELECT_3" } // Agent Menu
  | { type: "SUBMIT_WALLET_ID"; walletId: string }
  | { type: "VERIFICATION_SUCCESS" }
  | { type: "VERIFICATION_FAILED" }
  | { type: "BACK" };

export const welcomeMachine = setup({
  types: {
    context: {} as WelcomeContext,
    events: {} as WelcomeEvent,
    input: {} as {
      sessionId: string;
      phoneNumber: string;
      serviceCode: string;
    },
  },
  actions: {
    // Context is already initialized with input, so this just ensures session is active
    initializeSession: assign({
      isWalletVerified: false,
    }),

    setSelectedOption: assign({
      selectedOption: ({ event }) => {
        if (event.type === "SELECT_1") return "1";
        if (event.type === "SELECT_2") return "2";
        if (event.type === "SELECT_3") return "3";
        return undefined;
      },
    }),

    setWalletId: assign({
      walletId: ({ event }) =>
        event.type === "SUBMIT_WALLET_ID" ? event.walletId : undefined,
    }),

    markWalletVerified: assign({
      isWalletVerified: true,
    }),

    clearWalletData: assign({
      walletId: undefined,
      isWalletVerified: false,
    }),
  },

  guards: {
    isKnowMoreSelected: ({ context }) => context.selectedOption === "1",
    isWalletIdSelected: ({ context }) => context.selectedOption === "2",
    isAgentMenuSelected: ({ context }) => context.selectedOption === "3",
    hasValidWalletId: ({ context }) =>
      Boolean(context.walletId && context.walletId.length > 0),
  },
}).createMachine({
  id: "welcomeMachine",
  initial: "idle",

  context: ({ input }) => ({
    sessionId: input?.sessionId || "",
    phoneNumber: input?.phoneNumber || "",
    serviceCode: input?.serviceCode || "",
    isWalletVerified: false,
  }),

  states: {
    // Initial state - waiting for USSD dial
    idle: {
      on: {
        DIAL_USSD: {
          target: "preMenu",
          actions: "initializeSession",
        },
      },
    },

    // *2233# Pre-Menu - Main entry point
    preMenu: {
      on: {
        SELECT_1: {
          target: "routeToKnowMore",
          actions: "setSelectedOption",
        },
        SELECT_2: {
          target: "walletIdEntry",
          actions: "setSelectedOption",
        },
        SELECT_3: {
          target: "routeToAgent",
          actions: "setSelectedOption",
        },
        BACK: "sessionClosed",
      },
    },

    // Wallet ID entry and verification flow
    walletIdEntry: {
      entry: "clearWalletData",
      on: {
        SUBMIT_WALLET_ID: {
          target: "verifyingWallet",
          actions: "setWalletId",
        },
        BACK: "preMenu",
      },
    },

    verifyingWallet: {
      on: {
        VERIFICATION_SUCCESS: {
          target: "routeToUserServices",
          actions: "markWalletVerified",
        },
        VERIFICATION_FAILED: "walletIdEntry",
      },
    },

    // Routing states - these output the decision for the main orchestrator
    routeToKnowMore: {
      type: "final",
      output: {
        route: "knowMore" as const,
        context: ({ context }: { context: WelcomeContext }) => context,
      },
    },

    routeToUserServices: {
      type: "final",
      output: {
        route: "userServices" as const,
        context: ({ context }: { context: WelcomeContext }) => context,
      },
    },

    routeToAgent: {
      type: "final",
      output: {
        route: "agent" as const,
        context: ({ context }: { context: WelcomeContext }) => context,
      },
    },

    // Session ended
    sessionClosed: {
      type: "final",
      output: {
        route: "closed" as const,
        context: ({ context }: { context: WelcomeContext }) => context,
      },
    },
  },
});

export type WelcomeMachine = typeof welcomeMachine;
