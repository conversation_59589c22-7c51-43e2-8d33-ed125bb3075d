/**
 * Standard interfaces for child machine communication
 *
 * These interfaces define the standard input/output patterns for
 * parent-child machine communication in the SupaMoto Wallet system.
 */

/**
 * Standard input for all child machines
 * Contains basic session information needed by all child machines
 */
export interface StandardChildInput {
  /** Unique session identifier */
  sessionId: string;

  /** User's phone number */
  phoneNumber: string;

  /** USSD service code used to dial the service */
  serviceCode: string;

  /** Any additional data needed by the specific child machine */
  [key: string]: any;
}

/**
 * Standard output from all child machines
 * Provides consistent routing and message handling
 */
export interface StandardChildOutput {
  /**
   * Routing instruction for parent machine
   * - "main": Return to main menu
   * - "back": Go back one level
   * - "exit": Close the session
   * - "continue": Stay in current child machine (for multi-step flows)
   */
  route: "main" | "back" | "exit" | "continue";

  /**
   * Message to display to the user
   * Child machines are responsible for their own message formatting
   */
  message: string;

  /**
   * Child machine context that should be preserved
   * This allows the child to maintain state between invocations
   */
  context?: Record<string, any>;

  /**
   * Optional error information if the child machine encountered an error
   */
  error?: string;
}
