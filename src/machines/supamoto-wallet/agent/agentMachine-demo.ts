/* eslint-disable no-console */
import { createActor } from "xstate";
import { agentMachine } from "./agentMachine.js";

/**
 * Agent Machine Demo
 *
 * Demonstrates the agent machine functionality including:
 * - Agent dashboard and performance metrics
 * - Customer management and transactions
 * - Inventory management and sales
 * - Commission tracking and payouts
 * - Customer support workflows
 */

console.log("🚀 Agent Machine Demo\n");

const mockInput = {
  sessionId: "demo-session-agent-456",
  phoneNumber: "+260987654321",
  serviceCode: "*2233#",
  agentId: "AGT001",
  agentLevel: "premium" as const,
};

// Demo 1: Agent Dashboard
console.log("=".repeat(50));
console.log("DEMO 1: Agent Dashboard");
console.log("=".repeat(50));

const actor1 = createActor(agentMachine, { input: mockInput });
actor1.subscribe(snapshot => {
  console.log(`📍 State: ${snapshot.value}`);
  if (snapshot.context.selectedService) {
    console.log(`🎯 Service: ${snapshot.context.selectedService}`);
  }
  if (snapshot.context.dashboardData) {
    const data = snapshot.context.dashboardData;
    console.log(`👥 Total Customers: ${data.totalCustomers}`);
    console.log(`💳 Monthly Transactions: ${data.monthlyTransactions}`);
    console.log(`💰 Total Commissions: K${data.totalCommissions.toFixed(2)}`);
    console.log(`💵 Pending Payouts: K${data.pendingPayouts.toFixed(2)}`);
    console.log(`📦 Inventory Value: K${data.inventoryValue.toFixed(2)}`);
    console.log(`⭐ Performance Rating: ${data.performanceRating}/5.0`);
  }
  console.log("");
});

actor1.start();
actor1.send({ type: "START" });
console.log("📱 Agent started dashboard");

actor1.send({ type: "VIEW_DASHBOARD" });
console.log("📱 Agent viewed dashboard");

actor1.send({ type: "REFRESH_DATA" });
console.log("📱 Agent refreshed dashboard data");

setTimeout(() => {
  console.log("✅ Dashboard demo complete!\n");

  // Demo 2: Customer Management
  console.log("=".repeat(50));
  console.log("DEMO 2: Customer Management");
  console.log("=".repeat(50));

  const actor2 = createActor(agentMachine, { input: mockInput });
  actor2.subscribe(snapshot => {
    console.log(`📍 State: ${snapshot.value}`);
    if (snapshot.context.customers) {
      console.log(`👥 Customers: ${snapshot.context.customers.length}`);
      snapshot.context.customers.forEach((customer, index) => {
        console.log(
          `   ${index + 1}. ${customer.name} - K${customer.walletBalance} (${customer.status})`
        );
      });
    }
    if (snapshot.context.selectedCustomer) {
      console.log(`👤 Selected Customer: ${snapshot.context.selectedCustomer}`);
    }
    console.log("");
  });

  actor2.start();
  actor2.send({ type: "START" });
  actor2.send({ type: "MANAGE_CUSTOMERS" });
  console.log("📱 Agent selected customer management");

  actor2.send({ type: "SELECT_CUSTOMER", customerId: "CUST001" });
  console.log("📱 Agent selected customer CUST001");

  actor2.send({
    type: "PROCESS_CUSTOMER_TOPUP",
    customerId: "CUST001",
    amount: 100,
  });
  console.log("📱 Agent processed K100 top-up for customer");

  setTimeout(() => {
    console.log("✅ Customer management demo complete!\n");

    // Demo 3: Inventory Management
    console.log("=".repeat(50));
    console.log("DEMO 3: Inventory Management");
    console.log("=".repeat(50));

    const actor3 = createActor(agentMachine, { input: mockInput });
    actor3.subscribe(snapshot => {
      console.log(`📍 State: ${snapshot.value}`);
      if (snapshot.context.inventory) {
        console.log(`📦 Inventory Items: ${snapshot.context.inventory.length}`);
        snapshot.context.inventory.forEach((item, index) => {
          console.log(
            `   ${index + 1}. ${item.productName} - Qty: ${item.quantity} @ K${item.unitPrice}`
          );
        });
      }
      if (snapshot.context.selectedProduct) {
        console.log(`📦 Selected Product: ${snapshot.context.selectedProduct}`);
      }
      console.log("");
    });

    actor3.start();
    actor3.send({ type: "START" });
    actor3.send({ type: "MANAGE_INVENTORY" });
    console.log("📱 Agent selected inventory management");

    actor3.send({
      type: "UPDATE_INVENTORY",
      productId: "INV001",
      quantity: 20,
    });
    console.log("📱 Agent updated solar system inventory to 20 units");

    actor3.send({
      type: "PROCESS_PRODUCT_SALE",
      customerId: "CUST002",
      productId: "INV002",
      quantity: 2,
    });
    console.log("📱 Agent sold 2 cookstoves to customer CUST002");

    setTimeout(() => {
      console.log("✅ Inventory management demo complete!\n");

      // Demo 4: Commission Tracking
      console.log("=".repeat(50));
      console.log("DEMO 4: Commission Tracking");
      console.log("=".repeat(50));

      const actor4 = createActor(agentMachine, { input: mockInput });
      actor4.subscribe(snapshot => {
        console.log(`📍 State: ${snapshot.value}`);
        if (snapshot.context.commissions) {
          console.log(`💰 Commissions: ${snapshot.context.commissions.length}`);
          let totalPending = 0;
          let totalPaid = 0;
          snapshot.context.commissions.forEach((commission, index) => {
            console.log(
              `   ${index + 1}. ${commission.type} - K${commission.amount.toFixed(2)} (${commission.status})`
            );
            if (commission.status === "pending") {
              totalPending += commission.amount;
            } else {
              totalPaid += commission.amount;
            }
          });
          console.log(`💵 Total Pending: K${totalPending.toFixed(2)}`);
          console.log(`✅ Total Paid: K${totalPaid.toFixed(2)}`);
        }
        console.log("");
      });

      actor4.start();
      actor4.send({ type: "START" });
      actor4.send({ type: "VIEW_COMMISSIONS" });
      console.log("📱 Agent viewed commission tracking");

      actor4.send({ type: "REQUEST_COMMISSION_PAYOUT" });
      console.log("📱 Agent requested commission payout");

      setTimeout(() => {
        console.log("✅ Commission tracking demo complete!\n");

        // Demo 5: Customer Support
        console.log("=".repeat(50));
        console.log("DEMO 5: Customer Support");
        console.log("=".repeat(50));

        const actor5 = createActor(agentMachine, { input: mockInput });
        actor5.subscribe(snapshot => {
          console.log(`📍 State: ${snapshot.value}`);
          if (snapshot.context.serviceRequest) {
            const request = snapshot.context.serviceRequest;
            console.log(`🎫 Support Request: ${request.type}`);
            console.log(`📝 Description: ${request.description}`);
            console.log(`⚡ Priority: ${request.priority}`);
          }
          console.log("");
        });

        actor5.start();
        actor5.send({ type: "START" });
        actor5.send({ type: "CUSTOMER_SUPPORT" });
        console.log("📱 Agent selected customer support");

        actor5.send({
          type: "CREATE_SUPPORT_REQUEST",
          requestType: "technical",
          description:
            "Customer reports solar panel not charging properly after recent storm",
          priority: "high",
        });
        console.log("📱 Agent created high-priority technical support request");

        setTimeout(() => {
          console.log("✅ Customer support demo complete!\n");

          // Demo 6: Navigation Flow
          console.log("=".repeat(50));
          console.log("DEMO 6: Navigation Flow");
          console.log("=".repeat(50));

          const actor6 = createActor(agentMachine, { input: mockInput });
          actor6.subscribe(snapshot => {
            console.log(`📍 State: ${snapshot.value}`);
            if (snapshot.output) {
              console.log(`🎯 Output:`, snapshot.output);
            }
            console.log("");
          });

          actor6.start();
          actor6.send({ type: "START" });
          console.log("📱 Agent started services");

          actor6.send({ type: "MANAGE_INVENTORY" });
          console.log("📱 Agent selected inventory management");

          actor6.send({ type: "BACK_TO_SERVICES" });
          console.log("📱 Agent navigated back to service selection");

          actor6.send({ type: "BACK_TO_MAIN" });
          console.log("📱 Agent navigated back to main menu");

          console.log("✅ Navigation flow demo complete!\n");

          console.log("\n🎉 Agent Machine Demo Complete!");
          console.log("\n📊 Machine Summary:");
          console.log(
            "   • Handles comprehensive agent workflows and operations"
          );
          console.log("   • Provides agent dashboard with performance metrics");
          console.log("   • Manages customer relationships and transactions");
          console.log("   • Controls inventory and product sales");
          console.log("   • Tracks commissions and processes payouts");
          console.log("   • Supports customer service request management");
          console.log("   • Includes pagination for large data sets");
          console.log("   • Provides robust validation and error handling");
          console.log("   • Supports navigation through agent service flows");
          console.log("   • Type-safe with XState v5 setup() pattern");
        }, 900);
      }, 1600);
    }, 1200);
  }, 1200);
}, 1200);
