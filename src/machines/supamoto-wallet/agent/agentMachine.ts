import { setup, assign } from "xstate";
import type { AgentLevel } from "../../../services/agent-verification.js";
import { withNavigation } from "../utils/navigation-mixin.js";
import { navigationGuards } from "../guards/navigation.guards.js";
import { NavigationPatterns } from "../utils/navigation-patterns.js";

/**
 * Agent Machine - Agent-Specific Workflows and Dashboard
 *
 * Handles:
 * - Agent-specific workflows and operations
 * - Agent dashboard and performance metrics
 * - Customer service and support flows
 * - Commission tracking and payments
 * - Inventory management for agents
 *
 * Entry Points: START
 * Exit Points: Outputs routing decision for main orchestrator
 */

export interface AgentContext {
  sessionId: string;
  phoneNumber: string;
  serviceCode: string;
  agentId?: string;
  agentName?: string;
  agentLevel?: AgentLevel;
  selectedService?:
    | "dashboard"
    | "customers"
    | "inventory"
    | "commissions"
    | "support";
  dashboardData?: {
    totalCustomers: number;
    monthlyTransactions: number;
    totalCommissions: number;
    pendingPayouts: number;
    inventoryValue: number;
    performanceRating: number;
  };
  customers?: Array<{
    id: string;
    name: string;
    phone: string;
    walletBalance: number;
    lastTransaction: string;
    status: "active" | "inactive";
  }>;
  inventory?: Array<{
    id: string;
    productName: string;
    category: string;
    quantity: number;
    unitPrice: number;
    totalValue: number;
  }>;
  commissions?: Array<{
    id: string;
    type: "transaction" | "sale" | "referral";
    amount: number;
    date: string;
    status: "pending" | "paid";
    customerId: string;
  }>;
  selectedCustomer?: string;
  selectedProduct?: string;
  serviceRequest?: {
    type: "technical" | "billing" | "product" | "general";
    description: string;
    priority: "low" | "medium" | "high";
    customerId?: string;
  };
  currentPage?: number;
  totalPages?: number;
  message?: string;
  error?: string;
  validationError?: string;
}

export type AgentEvent =
  | { type: "START" }
  | { type: "INPUT"; input: string }
  | { type: "VIEW_DASHBOARD" }
  | { type: "MANAGE_CUSTOMERS" }
  | { type: "MANAGE_INVENTORY" }
  | { type: "VIEW_COMMISSIONS" }
  | { type: "CUSTOMER_SUPPORT" }
  | { type: "SELECT_CUSTOMER"; customerId: string }
  | { type: "SELECT_PRODUCT"; productId: string }
  | { type: "UPDATE_INVENTORY"; productId: string; quantity: number }
  | { type: "PROCESS_CUSTOMER_TOPUP"; customerId: string; amount: number }
  | {
      type: "PROCESS_PRODUCT_SALE";
      customerId: string;
      productId: string;
      quantity: number;
    }
  | {
      type: "CREATE_SUPPORT_REQUEST";
      requestType: "technical" | "billing" | "product" | "general";
      description: string;
      priority: "low" | "medium" | "high";
    }
  | { type: "RESOLVE_SUPPORT_REQUEST"; requestId: string }
  | { type: "REQUEST_COMMISSION_PAYOUT" }
  | { type: "REFRESH_DATA" }
  | { type: "NEXT_PAGE" }
  | { type: "PREVIOUS_PAGE" }
  | { type: "BACK_TO_SERVICES" }
  | { type: "BACK_TO_MAIN" }
  | { type: "ERROR"; error: string };

// Mock agent data
const MOCK_DASHBOARD_DATA = {
  totalCustomers: 156,
  monthlyTransactions: 342,
  totalCommissions: 2450.75,
  pendingPayouts: 890.25,
  inventoryValue: 15600.0,
  performanceRating: 4.7,
};

const MOCK_CUSTOMERS = [
  {
    id: "CUST001",
    name: "Mary Banda",
    phone: "+260987654321",
    walletBalance: 125.5,
    lastTransaction: "2024-01-20",
    status: "active" as const,
  },
  {
    id: "CUST002",
    name: "James Phiri",
    phone: "+260987654322",
    walletBalance: 89.75,
    lastTransaction: "2024-01-19",
    status: "active" as const,
  },
  {
    id: "CUST003",
    name: "Grace Mwanza",
    phone: "+260987654323",
    walletBalance: 0.0,
    lastTransaction: "2024-01-15",
    status: "inactive" as const,
  },
];

const MOCK_INVENTORY = [
  {
    id: "INV001",
    productName: "20W Solar Home System",
    category: "Solar",
    quantity: 15,
    unitPrice: 150.0,
    totalValue: 2250.0,
  },
  {
    id: "INV002",
    productName: "Efficient Cookstove",
    category: "Cookstoves",
    quantity: 8,
    unitPrice: 75.0,
    totalValue: 600.0,
  },
  {
    id: "INV003",
    productName: "LED Lantern",
    category: "Lighting",
    quantity: 25,
    unitPrice: 25.0,
    totalValue: 625.0,
  },
];

const MOCK_COMMISSIONS = [
  {
    id: "COM001",
    type: "transaction" as const,
    amount: 15.5,
    date: "2024-01-20",
    status: "pending" as const,
    customerId: "CUST001",
  },
  {
    id: "COM002",
    type: "sale" as const,
    amount: 45.0,
    date: "2024-01-19",
    status: "paid" as const,
    customerId: "CUST002",
  },
  {
    id: "COM003",
    type: "referral" as const,
    amount: 25.0,
    date: "2024-01-18",
    status: "pending" as const,
    customerId: "CUST003",
  },
];

export const agentMachine = setup({
  types: {
    context: {} as AgentContext,
    events: {} as AgentEvent,
    input: {} as {
      sessionId: string;
      phoneNumber: string;
      serviceCode: string;
      agentId?: string;
      agentName?: string;
      agentLevel?: AgentLevel;
    },
  },

  actions: {
    initializeContext: assign({
      dashboardData: () => MOCK_DASHBOARD_DATA,
      customers: () => MOCK_CUSTOMERS,
      inventory: () => MOCK_INVENTORY,
      commissions: () => MOCK_COMMISSIONS,
      currentPage: 1,
      totalPages: 1,
      error: undefined,
      validationError: undefined,
    }),

    setSelectedService: assign({
      selectedService: ({ event }) => {
        if (event.type === "VIEW_DASHBOARD") return "dashboard";
        if (event.type === "MANAGE_CUSTOMERS") return "customers";
        if (event.type === "MANAGE_INVENTORY") return "inventory";
        if (event.type === "VIEW_COMMISSIONS") return "commissions";
        if (event.type === "CUSTOMER_SUPPORT") return "support";
        return undefined;
      },
    }),

    setSelectedCustomer: assign({
      selectedCustomer: ({ event }) =>
        event.type === "SELECT_CUSTOMER" ? event.customerId : undefined,
    }),

    setSelectedProduct: assign({
      selectedProduct: ({ event }) =>
        event.type === "SELECT_PRODUCT" ? event.productId : undefined,
    }),

    updateInventory: assign({
      inventory: ({ context, event }) => {
        if (event.type === "UPDATE_INVENTORY" && context.inventory) {
          return context.inventory.map(item =>
            item.id === event.productId
              ? {
                  ...item,
                  quantity: event.quantity,
                  totalValue: event.quantity * item.unitPrice,
                }
              : item
          );
        }
        return context.inventory;
      },
    }),

    processCustomerTopup: assign({
      customers: ({ context, event }) => {
        if (event.type === "PROCESS_CUSTOMER_TOPUP" && context.customers) {
          return context.customers.map(customer =>
            customer.id === event.customerId
              ? {
                  ...customer,
                  walletBalance: customer.walletBalance + event.amount,
                  lastTransaction: new Date().toISOString().split("T")[0],
                  status: "active" as const,
                }
              : customer
          );
        }
        return context.customers;
      },
      commissions: ({ context, event }) => {
        if (event.type === "PROCESS_CUSTOMER_TOPUP" && context.commissions) {
          const newCommission = {
            id: `COM_${Date.now()}`,
            type: "transaction" as const,
            amount: event.amount * 0.05, // 5% commission
            date: new Date().toISOString().split("T")[0],
            status: "pending" as const,
            customerId: event.customerId,
          };
          return [...context.commissions, newCommission];
        }
        return context.commissions;
      },
    }),

    processProductSale: assign({
      inventory: ({ context, event }) => {
        if (event.type === "PROCESS_PRODUCT_SALE" && context.inventory) {
          return context.inventory.map(item =>
            item.id === event.productId
              ? {
                  ...item,
                  quantity: Math.max(0, item.quantity - event.quantity),
                  totalValue:
                    Math.max(0, item.quantity - event.quantity) *
                    item.unitPrice,
                }
              : item
          );
        }
        return context.inventory;
      },
      commissions: ({ context, event }) => {
        if (
          event.type === "PROCESS_PRODUCT_SALE" &&
          context.commissions &&
          context.inventory
        ) {
          const product = context.inventory.find(p => p.id === event.productId);
          if (product) {
            const saleValue = product.unitPrice * event.quantity;
            const newCommission = {
              id: `COM_${Date.now()}`,
              type: "sale" as const,
              amount: saleValue * 0.15, // 15% commission on sales
              date: new Date().toISOString().split("T")[0],
              status: "pending" as const,
              customerId: event.customerId,
            };
            return [...context.commissions, newCommission];
          }
        }
        return context.commissions;
      },
    }),

    createSupportRequest: assign({
      serviceRequest: ({ event }) =>
        event.type === "CREATE_SUPPORT_REQUEST"
          ? {
              type: event.requestType,
              description: event.description,
              priority: event.priority,
            }
          : undefined,
    }),

    refreshDashboardData: assign({
      dashboardData: ({ context }) => {
        if (context.dashboardData) {
          return {
            ...context.dashboardData,
            monthlyTransactions:
              context.dashboardData.monthlyTransactions +
              Math.floor(Math.random() * 5),
            totalCommissions:
              context.dashboardData.totalCommissions + Math.random() * 50,
            performanceRating: Math.min(
              5.0,
              context.dashboardData.performanceRating + Math.random() * 0.1
            ),
          };
        }
        return context.dashboardData;
      },
    }),

    nextPage: assign({
      currentPage: ({ context }) =>
        Math.min((context.currentPage || 1) + 1, context.totalPages || 1),
    }),

    previousPage: assign({
      currentPage: ({ context }) => Math.max((context.currentPage || 1) - 1, 1),
    }),

    setTotalPages: assign({
      totalPages: ({ context }) => {
        if (context.selectedService === "customers" && context.customers) {
          return Math.ceil(context.customers.length / 5); // 5 customers per page
        }
        if (context.selectedService === "inventory" && context.inventory) {
          return Math.ceil(context.inventory.length / 5); // 5 items per page
        }
        if (context.selectedService === "commissions" && context.commissions) {
          return Math.ceil(context.commissions.length / 10); // 10 commissions per page
        }
        return 1;
      },
    }),

    setError: assign({
      error: ({ event }) =>
        event.type === "ERROR" ? event.error : "An error occurred",
    }),

    setValidationError: assign({
      validationError: ({ event }) => {
        if (event.type === "UPDATE_INVENTORY" && event.quantity < 0) {
          return "Quantity cannot be negative";
        }
        if (event.type === "PROCESS_CUSTOMER_TOPUP" && event.amount <= 0) {
          return "Top-up amount must be positive";
        }
        if (event.type === "PROCESS_PRODUCT_SALE" && event.quantity <= 0) {
          return "Sale quantity must be positive";
        }
        if (
          event.type === "CREATE_SUPPORT_REQUEST" &&
          event.description.length < 10
        ) {
          return "Support request description must be at least 10 characters";
        }
        return undefined;
      },
    }),

    clearErrors: assign({
      error: undefined,
      validationError: undefined,
    }),
  },

  guards: {
    isDashboardService: ({ context }) =>
      context.selectedService === "dashboard",
    isCustomersService: ({ context }) =>
      context.selectedService === "customers",
    isInventoryService: ({ context }) =>
      context.selectedService === "inventory",
    isCommissionsService: ({ context }) =>
      context.selectedService === "commissions",
    isSupportService: ({ context }) => context.selectedService === "support",

    hasCustomers: ({ context }) =>
      Boolean(context.customers && context.customers.length > 0),
    hasInventory: ({ context }) =>
      Boolean(context.inventory && context.inventory.length > 0),
    hasCommissions: ({ context }) =>
      Boolean(context.commissions && context.commissions.length > 0),

    hasSelectedCustomer: ({ context }) => Boolean(context.selectedCustomer),
    hasSelectedProduct: ({ context }) => Boolean(context.selectedProduct),

    isValidQuantity: ({ event }) =>
      event.type === "UPDATE_INVENTORY" && event.quantity >= 0,

    isValidTopupAmount: ({ event }) =>
      event.type === "PROCESS_CUSTOMER_TOPUP" && event.amount > 0,

    isValidSaleQuantity: ({ event, context }) => {
      if (event.type === "PROCESS_PRODUCT_SALE" && context.inventory) {
        const product = context.inventory.find(p => p.id === event.productId);
        return Boolean(
          product && event.quantity > 0 && event.quantity <= product.quantity
        );
      }
      return false;
    },

    isValidSupportRequest: ({ event }) =>
      event.type === "CREATE_SUPPORT_REQUEST" && event.description.length >= 10,

    hasNextPage: ({ context }) =>
      (context.currentPage || 1) < (context.totalPages || 1),

    hasPreviousPage: ({ context }) => (context.currentPage || 1) > 1,

    hasError: ({ context }) => Boolean(context.error),
    hasValidationError: ({ context }) => Boolean(context.validationError),

    // Input guards for menu selection
    isInput1: ({ event }) => event.type === "INPUT" && event.input === "1",
    isInput2: ({ event }) => event.type === "INPUT" && event.input === "2",
    isInput3: ({ event }) => event.type === "INPUT" && event.input === "3",
    isInput4: ({ event }) => event.type === "INPUT" && event.input === "4",
    isInput5: ({ event }) => event.type === "INPUT" && event.input === "5",

    // Navigation guards
    isBack: ({ event }) =>
      navigationGuards.isBackCommand(null as any, event as any),
    isExit: ({ event }) =>
      navigationGuards.isExitCommand(null as any, event as any),
  },
}).createMachine({
  id: "agentMachine",
  initial: "idle",

  context: ({ input }) => ({
    sessionId: input?.sessionId || "",
    phoneNumber: input?.phoneNumber || "",
    serviceCode: input?.serviceCode || "",
    agentId: input?.agentId,
    agentName: input?.agentName,
    agentLevel: input?.agentLevel || "basic",
    message:
      input?.agentName && input?.agentLevel
        ? `Welcome Agent ${input.agentName}!\nLevel: ${input.agentLevel}\n1. Customer Management\n2. Inventory\n3. Sales Reports\n4. Commission\n5. Support`
        : "Loading agent services...",
  }),

  states: {
    // Initial state - waiting for start
    idle: {
      on: {
        START: {
          target: "serviceSelection",
          actions: "initializeContext",
        },
      },
    },

    // Agent service selection menu
    serviceSelection: {
      entry: ["clearErrors", "setTotalPages"],
      on: {
        INPUT: withNavigation(
          [
            {
              target: "dashboard",
              guard: "isInput1",
              actions: [assign({ selectedService: "dashboard" })],
            },
            {
              target: "customerManagement",
              guard: "isInput2",
              actions: [assign({ selectedService: "customers" })],
            },
            {
              target: "inventoryManagement",
              guard: "isInput3",
              actions: [assign({ selectedService: "inventory" })],
            },
            {
              target: "commissionTracking",
              guard: "isInput4",
              actions: [assign({ selectedService: "commissions" })],
            },
            {
              target: "supportManagement",
              guard: "isInput5",
              actions: [assign({ selectedService: "support" })],
            },
          ],
          NavigationPatterns.agentChild
        ),
        VIEW_DASHBOARD: {
          target: "dashboard",
          actions: "setSelectedService",
        },
        MANAGE_CUSTOMERS: {
          target: "customerManagement",
          actions: "setSelectedService",
        },
        MANAGE_INVENTORY: {
          target: "inventoryManagement",
          actions: "setSelectedService",
        },
        VIEW_COMMISSIONS: {
          target: "commissionTracking",
          actions: "setSelectedService",
        },
        CUSTOMER_SUPPORT: {
          target: "supportManagement",
          actions: "setSelectedService",
        },
        BACK_TO_MAIN: "routeToMain",
        ERROR: {
          target: "error",
          actions: "setError",
        },
      },
    },

    // Agent dashboard
    dashboard: {
      on: {
        REFRESH_DATA: {
          actions: "refreshDashboardData",
        },
        BACK_TO_SERVICES: "serviceSelection",
        BACK_TO_MAIN: "routeToMain",
        ERROR: {
          target: "error",
          actions: "setError",
        },
      },
    },

    // Customer management
    customerManagement: {
      on: {
        SELECT_CUSTOMER: {
          target: "customerDetails",
          actions: "setSelectedCustomer",
        },
        NEXT_PAGE: {
          guard: "hasNextPage",
          actions: "nextPage",
        },
        PREVIOUS_PAGE: {
          guard: "hasPreviousPage",
          actions: "previousPage",
        },
        BACK_TO_SERVICES: "serviceSelection",
        BACK_TO_MAIN: "routeToMain",
        ERROR: {
          target: "error",
          actions: "setError",
        },
      },
    },

    // Customer details
    customerDetails: {
      on: {
        PROCESS_CUSTOMER_TOPUP: [
          {
            target: "processingTransaction",
            actions: "processCustomerTopup",
            guard: "isValidTopupAmount",
          },
          {
            target: "customerDetails",
            actions: "setValidationError",
          },
        ],
        BACK_TO_SERVICES: "customerManagement",
        BACK_TO_MAIN: "routeToMain",
        ERROR: {
          target: "error",
          actions: "setError",
        },
      },
    },

    // Inventory management
    inventoryManagement: {
      on: {
        SELECT_PRODUCT: {
          target: "productDetails",
          actions: "setSelectedProduct",
        },
        UPDATE_INVENTORY: [
          {
            actions: "updateInventory",
            guard: "isValidQuantity",
          },
          {
            actions: "setValidationError",
          },
        ],
        PROCESS_PRODUCT_SALE: [
          {
            target: "processingTransaction",
            actions: "processProductSale",
            guard: "isValidSaleQuantity",
          },
          {
            actions: "setValidationError",
          },
        ],
        NEXT_PAGE: {
          guard: "hasNextPage",
          actions: "nextPage",
        },
        PREVIOUS_PAGE: {
          guard: "hasPreviousPage",
          actions: "previousPage",
        },
        BACK_TO_SERVICES: "serviceSelection",
        BACK_TO_MAIN: "routeToMain",
        ERROR: {
          target: "error",
          actions: "setError",
        },
      },
    },

    // Product details
    productDetails: {
      on: {
        UPDATE_INVENTORY: [
          {
            target: "inventoryManagement",
            actions: "updateInventory",
            guard: "isValidQuantity",
          },
          {
            target: "productDetails",
            actions: "setValidationError",
          },
        ],
        BACK_TO_SERVICES: "inventoryManagement",
        BACK_TO_MAIN: "routeToMain",
        ERROR: {
          target: "error",
          actions: "setError",
        },
      },
    },

    // Commission tracking
    commissionTracking: {
      on: {
        REQUEST_COMMISSION_PAYOUT: "processingPayout",
        NEXT_PAGE: {
          guard: "hasNextPage",
          actions: "nextPage",
        },
        PREVIOUS_PAGE: {
          guard: "hasPreviousPage",
          actions: "previousPage",
        },
        BACK_TO_SERVICES: "serviceSelection",
        BACK_TO_MAIN: "routeToMain",
        ERROR: {
          target: "error",
          actions: "setError",
        },
      },
    },

    // Support management
    supportManagement: {
      on: {
        CREATE_SUPPORT_REQUEST: [
          {
            target: "processingSupport",
            actions: "createSupportRequest",
            guard: "isValidSupportRequest",
          },
          {
            target: "supportManagement",
            actions: "setValidationError",
          },
        ],
        BACK_TO_SERVICES: "serviceSelection",
        BACK_TO_MAIN: "routeToMain",
        ERROR: {
          target: "error",
          actions: "setError",
        },
      },
    },

    // Processing transaction
    processingTransaction: {
      after: {
        1000: "transactionSuccess", // Simulate processing time
      },
      on: {
        ERROR: {
          target: "error",
          actions: "setError",
        },
      },
    },

    // Processing payout
    processingPayout: {
      after: {
        1500: "payoutSuccess", // Simulate processing time
      },
      on: {
        ERROR: {
          target: "error",
          actions: "setError",
        },
      },
    },

    // Processing support
    processingSupport: {
      after: {
        800: "supportSuccess", // Simulate processing time
      },
      on: {
        ERROR: {
          target: "error",
          actions: "setError",
        },
      },
    },

    // Transaction successful
    transactionSuccess: {
      on: {
        BACK_TO_SERVICES: "serviceSelection",
        BACK_TO_MAIN: "routeToMain",
      },
    },

    // Payout successful
    payoutSuccess: {
      on: {
        BACK_TO_SERVICES: "serviceSelection",
        BACK_TO_MAIN: "routeToMain",
      },
    },

    // Support successful
    supportSuccess: {
      on: {
        BACK_TO_SERVICES: "serviceSelection",
        BACK_TO_MAIN: "routeToMain",
      },
    },

    // Error state
    error: {
      entry: "setError",
      on: {
        START: {
          target: "serviceSelection",
          actions: "clearErrors",
        },
        BACK_TO_SERVICES: "serviceSelection",
        BACK_TO_MAIN: "routeToMain",
      },
    },

    // Route back to main menu
    routeToMain: {
      type: "final",
    },
  },
});

export type AgentMachine = typeof agentMachine;
