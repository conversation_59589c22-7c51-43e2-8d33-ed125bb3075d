/* eslint-disable no-console */
import { createActor } from "xstate";
import { voucherMachine } from "./voucherMachine.js";

/**
 * Voucher Machine Demo - Digital Voucher Management
 */

console.log("🚀 Voucher Machine Demo\n");

const mockInput = {
  sessionId: "demo-session-voucher-123",
  phoneNumber: "+260987654321",
  serviceCode: "*2233#",
  walletId: "C21009802",
  currentBalance: 200,
};

// Demo 1: Voucher Redemption
console.log("=".repeat(50));
console.log("DEMO 1: Voucher Redemption");
console.log("=".repeat(50));

const actor1 = createActor(voucherMachine, { input: mockInput });
actor1.subscribe(snapshot => {
  console.log(`📍 State: ${snapshot.value}`);
  if (snapshot.context.voucherCode) {
    console.log(`🎫 Voucher Code: ${snapshot.context.voucherCode}`);
  }
  if (snapshot.context.voucherAmount) {
    console.log(`💰 Voucher Amount: K${snapshot.context.voucherAmount}`);
  }
  if (snapshot.context.voucherType) {
    console.log(`🏷️ Voucher Type: ${snapshot.context.voucherType}`);
  }
  if (snapshot.context.transactionId) {
    console.log(`🆔 Transaction ID: ${snapshot.context.transactionId}`);
  }
  if (snapshot.context.currentBalance !== undefined) {
    console.log(`💵 Current Balance: K${snapshot.context.currentBalance}`);
  }
  console.log("");
});

actor1.start();
actor1.send({ type: "START" });
actor1.send({ type: "REDEEM_VOUCHER" });
actor1.send({ type: "SUBMIT_VOUCHER_CODE", code: "ENERGY2024001" });
actor1.send({ type: "CONFIRM_REDEMPTION" });
console.log("📱 User redeemed energy voucher");

setTimeout(() => {
  console.log("✅ Voucher redemption complete!\n");

  // Demo 2: Voucher Purchase
  console.log("=".repeat(50));
  console.log("DEMO 2: Voucher Purchase");
  console.log("=".repeat(50));

  const actor2 = createActor(voucherMachine, { input: mockInput });
  actor2.subscribe(snapshot => {
    console.log(`📍 State: ${snapshot.value}`);
    if (snapshot.context.voucherType) {
      console.log(`🏷️ Type: ${snapshot.context.voucherType}`);
    }
    if (snapshot.context.purchaseAmount) {
      console.log(`💰 Amount: K${snapshot.context.purchaseAmount}`);
    }
    if (snapshot.context.voucherCode) {
      console.log(`🎫 Generated Code: ${snapshot.context.voucherCode}`);
    }
    console.log("");
  });

  actor2.start();
  actor2.send({ type: "START" });
  actor2.send({ type: "PURCHASE_VOUCHER" });
  actor2.send({ type: "SELECT_CASH_VOUCHER" });
  actor2.send({ type: "SET_PURCHASE_AMOUNT", amount: 75 });
  actor2.send({ type: "CONFIRM_PURCHASE" });
  console.log("📱 User purchased cash voucher");

  setTimeout(() => {
    console.log("✅ Voucher purchase complete!\n");

    console.log("\n🎉 Voucher Machine Demo Complete!");
    console.log("\n📊 Machine Summary:");
    console.log("   • Handles digital voucher workflows");
    console.log("   • Supports voucher redemption and validation");
    console.log("   • Manages voucher purchase and generation");
    console.log("   • Includes voucher transfer functionality");
    console.log("   • Provides voucher balance and history tracking");
    console.log(
      "   • Supports multiple voucher types (energy, product, service, cash)"
    );
    console.log("   • Type-safe with XState v5 setup() pattern");
  }, 1200);
}, 1200);
