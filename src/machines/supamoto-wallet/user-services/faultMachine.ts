import { setup, assign } from "xstate";

/**
 * Fault Machine - Fault Reporting and Issue Management
 *
 * Handles:
 * - Fault reporting workflows
 * - Issue categorization and classification
 * - Fault description and details collection
 * - Escalation flows and priority assignment
 * - Fault tracking and status updates
 *
 * Entry Points: START
 * Exit Points: Outputs routing decision for main orchestrator
 */

export interface FaultContext {
  sessionId: string;
  phoneNumber: string;
  serviceCode: string;
  walletId?: string;
  faultCategory?: "product" | "delivery" | "payment" | "service" | "technical";
  faultType?: string;
  faultDescription?: string;
  faultPriority?: "low" | "medium" | "high" | "critical";
  affectedProduct?: {
    id: string;
    name: string;
    purchaseDate?: string;
  };
  contactPreference?: "sms" | "call" | "agent_visit";
  faultId?: string;
  faultStatus?:
    | "submitted"
    | "acknowledged"
    | "investigating"
    | "resolved"
    | "escalated";
  estimatedResolution?: string;
  assignedAgent?: string;
  escalationReason?: string;
  error?: string;
  validationError?: string;
}

export type FaultEvent =
  | { type: "START" }
  | { type: "SELECT_PRODUCT_FAULT" }
  | { type: "SELECT_DELIVERY_FAULT" }
  | { type: "SELECT_PAYMENT_FAULT" }
  | { type: "SELECT_SERVICE_FAULT" }
  | { type: "SELECT_TECHNICAL_FAULT" }
  | { type: "SELECT_FAULT_TYPE"; faultType: string }
  | { type: "SUBMIT_DESCRIPTION"; description: string }
  | {
      type: "SELECT_PRODUCT";
      productId: string;
      productName: string;
      purchaseDate?: string;
    }
  | { type: "SET_PRIORITY"; priority: "low" | "medium" | "high" | "critical" }
  | { type: "SELECT_SMS_CONTACT" }
  | { type: "SELECT_CALL_CONTACT" }
  | { type: "SELECT_AGENT_VISIT" }
  | { type: "SUBMIT_FAULT" }
  | { type: "FAULT_SUBMITTED"; faultId: string }
  | { type: "ESCALATE_FAULT"; reason: string }
  | { type: "ESCALATION_SUCCESS" }
  | { type: "VIEW_FAULT_STATUS" }
  | { type: "NEW_FAULT_REPORT" }
  | { type: "BACK_TO_CATEGORIES" }
  | { type: "BACK_TO_TYPES" }
  | { type: "BACK_TO_MENU" }
  | { type: "BACK_TO_MAIN" }
  | { type: "ERROR"; error: string };

// Fault type definitions by category (commented out as unused)
// const FAULT_TYPES = {
//   product: [
//     "Not working properly",
//     "Physical damage",
//     "Performance issues",
//     "Missing components",
//     "Installation problems",
//   ],
//   delivery: [
//     "Late delivery",
//     "Wrong product delivered",
//     "Damaged during delivery",
//     "Missing delivery",
//     "Delivery location issues",
//   ],
//   payment: [
//     "Payment not processed",
//     "Wrong amount charged",
//     "Refund request",
//     "Payment method issues",
//     "Transaction errors",
//   ],
//   service: [
//     "Poor customer service",
//     "Agent unavailable",
//     "Incorrect information",
//     "Service delays",
//     "Communication issues",
//   ],
//   technical: [
//     "USSD not working",
//     "App crashes",
//     "Login problems",
//     "System errors",
//     "Data synchronization issues",
//   ],
// };

export const faultMachine = setup({
  types: {
    context: {} as FaultContext,
    events: {} as FaultEvent,
    input: {} as {
      sessionId: string;
      phoneNumber: string;
      serviceCode: string;
      walletId?: string;
    },
  },

  actions: {
    initializeContext: assign({
      faultStatus: undefined,
      error: undefined,
      validationError: undefined,
    }),

    setFaultCategory: assign({
      faultCategory: ({ event }) => {
        if (event.type === "SELECT_PRODUCT_FAULT") return "product";
        if (event.type === "SELECT_DELIVERY_FAULT") return "delivery";
        if (event.type === "SELECT_PAYMENT_FAULT") return "payment";
        if (event.type === "SELECT_SERVICE_FAULT") return "service";
        if (event.type === "SELECT_TECHNICAL_FAULT") return "technical";
        return undefined;
      },
    }),

    setFaultType: assign({
      faultType: ({ event }) =>
        event.type === "SELECT_FAULT_TYPE" ? event.faultType : undefined,
    }),

    setFaultDescription: assign({
      faultDescription: ({ event }) =>
        event.type === "SUBMIT_DESCRIPTION" ? event.description : undefined,
    }),

    setAffectedProduct: assign({
      affectedProduct: ({ event }) =>
        event.type === "SELECT_PRODUCT"
          ? {
              id: event.productId,
              name: event.productName,
              purchaseDate: event.purchaseDate,
            }
          : undefined,
    }),

    setFaultPriority: assign({
      faultPriority: ({ event }) =>
        event.type === "SET_PRIORITY" ? event.priority : undefined,
    }),

    setContactPreference: assign({
      contactPreference: ({ event }) => {
        if (event.type === "SELECT_SMS_CONTACT") return "sms";
        if (event.type === "SELECT_CALL_CONTACT") return "call";
        if (event.type === "SELECT_AGENT_VISIT") return "agent_visit";
        return undefined;
      },
    }),

    generateFaultId: assign({
      faultId: () =>
        `FLT_${Date.now()}_${Math.random().toString(36).substr(2, 6)}`,
      faultStatus: "submitted" as const,
    }),

    acknowledgeFault: assign({
      faultStatus: "acknowledged" as const,
      assignedAgent: () => `Agent_${Math.floor(Math.random() * 100) + 1}`,
      estimatedResolution: () => {
        const days = Math.floor(Math.random() * 7) + 1;
        const date = new Date();
        date.setDate(date.getDate() + days);
        return date.toISOString().split("T")[0];
      },
    }),

    setEscalationReason: assign({
      escalationReason: ({ event }) =>
        event.type === "ESCALATE_FAULT" ? event.reason : undefined,
    }),

    escalateFault: assign({
      faultStatus: "escalated" as const,
      faultPriority: ({ context }) => {
        // Auto-escalate priority
        if (context.faultPriority === "low") return "medium";
        if (context.faultPriority === "medium") return "high";
        if (context.faultPriority === "high") return "critical";
        return context.faultPriority;
      },
    }),

    resetForNewFault: assign({
      faultCategory: undefined,
      faultType: undefined,
      faultDescription: undefined,
      faultPriority: undefined,
      affectedProduct: undefined,
      contactPreference: undefined,
      faultId: undefined,
      faultStatus: undefined,
      estimatedResolution: undefined,
      assignedAgent: undefined,
      escalationReason: undefined,
      error: undefined,
      validationError: undefined,
    }),

    setError: assign({
      error: ({ event }) =>
        event.type === "ERROR" ? event.error : "An error occurred",
    }),

    setValidationError: assign({
      validationError: ({ context, event }) => {
        if (
          event.type === "SUBMIT_DESCRIPTION" &&
          (!event.description || event.description.trim().length < 10)
        ) {
          return "Please provide a detailed description (minimum 10 characters)";
        }
        if (
          event.type === "ESCALATE_FAULT" &&
          (!event.reason || event.reason.trim().length < 5)
        ) {
          return "Please provide a reason for escalation";
        }
        if (!context.faultCategory) {
          return "Please select a fault category";
        }
        if (!context.faultType) {
          return "Please select a fault type";
        }
        if (
          !context.faultDescription ||
          context.faultDescription.trim().length < 10
        ) {
          return "Please provide a detailed fault description";
        }
        if (!context.contactPreference) {
          return "Please select a contact preference";
        }
        return undefined;
      },
    }),

    clearErrors: assign({
      error: undefined,
      validationError: undefined,
    }),
  },

  guards: {
    isProductFault: ({ context }) => context.faultCategory === "product",
    isDeliveryFault: ({ context }) => context.faultCategory === "delivery",
    isPaymentFault: ({ context }) => context.faultCategory === "payment",
    isServiceFault: ({ context }) => context.faultCategory === "service",
    isTechnicalFault: ({ context }) => context.faultCategory === "technical",

    hasValidDescription: ({ context }) =>
      Boolean(
        context.faultDescription && context.faultDescription.trim().length >= 10
      ),

    hasValidFaultData: ({ context }) =>
      Boolean(
        context.faultCategory &&
          context.faultType &&
          context.faultDescription &&
          context.faultDescription.trim().length >= 10 &&
          context.contactPreference
      ),

    isHighPriority: ({ context }) =>
      context.faultPriority === "high" || context.faultPriority === "critical",

    canEscalate: ({ context }) =>
      Boolean(context.faultId && context.faultStatus !== "escalated"),

    isValidEscalationReason: ({ event }) =>
      event.type === "ESCALATE_FAULT" &&
      Boolean(event.reason && event.reason.trim().length >= 5),

    hasError: ({ context }) => Boolean(context.error),
    hasValidationError: ({ context }) => Boolean(context.validationError),
  },
}).createMachine({
  id: "faultMachine",
  initial: "idle",

  context: ({ input }) => ({
    sessionId: input?.sessionId || "",
    phoneNumber: input?.phoneNumber || "",
    serviceCode: input?.serviceCode || "",
    walletId: input?.walletId,
  }),

  states: {
    // Initial state - waiting for start
    idle: {
      on: {
        START: {
          target: "categorySelection",
          actions: "initializeContext",
        },
      },
    },

    // Fault category selection
    categorySelection: {
      entry: "clearErrors",
      on: {
        SELECT_PRODUCT_FAULT: {
          target: "typeSelection",
          actions: "setFaultCategory",
        },
        SELECT_DELIVERY_FAULT: {
          target: "typeSelection",
          actions: "setFaultCategory",
        },
        SELECT_PAYMENT_FAULT: {
          target: "typeSelection",
          actions: "setFaultCategory",
        },
        SELECT_SERVICE_FAULT: {
          target: "typeSelection",
          actions: "setFaultCategory",
        },
        SELECT_TECHNICAL_FAULT: {
          target: "typeSelection",
          actions: "setFaultCategory",
        },
        BACK_TO_MENU: "routeToUserServices",
        BACK_TO_MAIN: "routeToMain",
        ERROR: {
          target: "error",
          actions: "setError",
        },
      },
    },

    // Fault type selection within category
    typeSelection: {
      on: {
        SELECT_FAULT_TYPE: {
          target: "descriptionEntry",
          actions: "setFaultType",
        },
        BACK_TO_CATEGORIES: "categorySelection",
        BACK_TO_MAIN: "routeToMain",
        ERROR: {
          target: "error",
          actions: "setError",
        },
      },
    },

    // Fault description entry
    descriptionEntry: {
      on: {
        SUBMIT_DESCRIPTION: [
          {
            target: "productSelection",
            actions: "setFaultDescription",
            guard: "hasValidDescription",
          },
          {
            target: "descriptionEntry",
            actions: "setValidationError",
          },
        ],
        BACK_TO_TYPES: "typeSelection",
        BACK_TO_MAIN: "routeToMain",
        ERROR: {
          target: "error",
          actions: "setError",
        },
      },
    },

    // Product selection (for product-related faults)
    productSelection: {
      on: {
        SELECT_PRODUCT: {
          target: "prioritySelection",
          actions: "setAffectedProduct",
        },
        SET_PRIORITY: {
          target: "contactPreference",
          actions: "setFaultPriority",
        },
        BACK_TO_TYPES: "descriptionEntry",
        BACK_TO_MAIN: "routeToMain",
        ERROR: {
          target: "error",
          actions: "setError",
        },
      },
    },

    // Priority selection
    prioritySelection: {
      on: {
        SET_PRIORITY: {
          target: "contactPreference",
          actions: "setFaultPriority",
        },
        BACK_TO_TYPES: "productSelection",
        BACK_TO_MAIN: "routeToMain",
        ERROR: {
          target: "error",
          actions: "setError",
        },
      },
    },

    // Contact preference selection
    contactPreference: {
      on: {
        SELECT_SMS_CONTACT: {
          target: "faultConfirmation",
          actions: "setContactPreference",
        },
        SELECT_CALL_CONTACT: {
          target: "faultConfirmation",
          actions: "setContactPreference",
        },
        SELECT_AGENT_VISIT: {
          target: "faultConfirmation",
          actions: "setContactPreference",
        },
        BACK_TO_TYPES: "prioritySelection",
        BACK_TO_MAIN: "routeToMain",
        ERROR: {
          target: "error",
          actions: "setError",
        },
      },
    },

    // Fault confirmation
    faultConfirmation: {
      on: {
        SUBMIT_FAULT: [
          {
            target: "submittingFault",
            guard: "hasValidFaultData",
          },
          {
            target: "faultConfirmation",
            actions: "setValidationError",
          },
        ],
        BACK_TO_TYPES: "contactPreference",
        BACK_TO_MAIN: "routeToMain",
        ERROR: {
          target: "error",
          actions: "setError",
        },
      },
    },

    // Submitting fault
    submittingFault: {
      entry: "generateFaultId",
      after: {
        1000: "faultSubmitted", // Simulate submission time
      },
      on: {
        ERROR: {
          target: "error",
          actions: "setError",
        },
      },
    },

    // Fault submitted successfully
    faultSubmitted: {
      entry: "acknowledgeFault",
      on: {
        VIEW_FAULT_STATUS: "faultStatus",
        ESCALATE_FAULT: [
          {
            target: "escalating",
            actions: "setEscalationReason",
            guard: "isValidEscalationReason",
          },
          {
            target: "faultSubmitted",
            actions: "setValidationError",
          },
        ],
        NEW_FAULT_REPORT: {
          target: "categorySelection",
          actions: "resetForNewFault",
        },
        BACK_TO_MENU: "routeToUserServices",
        BACK_TO_MAIN: "routeToMain",
      },
    },

    // Fault status view
    faultStatus: {
      on: {
        ESCALATE_FAULT: [
          {
            target: "escalating",
            actions: "setEscalationReason",
            guard: "isValidEscalationReason",
          },
          {
            target: "faultStatus",
            actions: "setValidationError",
          },
        ],
        NEW_FAULT_REPORT: {
          target: "categorySelection",
          actions: "resetForNewFault",
        },
        BACK_TO_MENU: "routeToUserServices",
        BACK_TO_MAIN: "routeToMain",
      },
    },

    // Escalating fault
    escalating: {
      entry: "escalateFault",
      after: {
        1000: "escalationSuccess", // Simulate escalation time
      },
      on: {
        ERROR: {
          target: "error",
          actions: "setError",
        },
      },
    },

    // Escalation successful
    escalationSuccess: {
      on: {
        NEW_FAULT_REPORT: {
          target: "categorySelection",
          actions: "resetForNewFault",
        },
        BACK_TO_MENU: "routeToUserServices",
        BACK_TO_MAIN: "routeToMain",
      },
    },

    // Error state
    error: {
      entry: "setError",
      on: {
        START: {
          target: "categorySelection",
          actions: "clearErrors",
        },
        BACK_TO_MENU: "routeToUserServices",
        BACK_TO_MAIN: "routeToMain",
      },
    },

    // Route back to user services
    routeToUserServices: {
      type: "final",
      output: {
        route: "userServices" as const,
        context: ({ context }: { context: FaultContext }) => context,
      },
    },

    // Route back to main menu
    routeToMain: {
      type: "final",
      output: {
        route: "main" as const,
        context: ({ context }: { context: FaultContext }) => context,
      },
    },
  },
});

export type FaultMachine = typeof faultMachine;
