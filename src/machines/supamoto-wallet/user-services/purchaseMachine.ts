import { setup, assign } from "xstate";

/**
 * Purchase Machine - Product Purchase and Order Processing
 *
 * Handles:
 * - Product catalog browsing
 * - Product selection and configuration
 * - Inventory checking
 * - Order creation and confirmation
 * - Payment processing for purchases
 *
 * Entry Points: START
 * Exit Points: Outputs routing decision for main orchestrator
 */

export interface PurchaseContext {
  sessionId: string;
  phoneNumber: string;
  serviceCode: string;
  walletId?: string;
  currentBalance?: number;
  selectedCategory?: "solar" | "cookstoves" | "water" | "lighting";
  selectedProduct?: {
    id: string;
    name: string;
    price: number;
    category: string;
    inStock: boolean;
    description?: string;
  };
  quantity?: number;
  totalAmount?: number;
  deliveryAddress?: string;
  orderId?: string;
  orderStatus?: "pending" | "confirmed" | "processing" | "completed" | "failed";
  paymentMethod?: "wallet" | "mobile_money" | "agent";
  transactionId?: string;
  error?: string;
  validationError?: string;
}

export type PurchaseEvent =
  | { type: "START" }
  | { type: "SELECT_SOLAR" }
  | { type: "SELECT_COOKSTOVES" }
  | { type: "SELECT_WATER" }
  | { type: "SELECT_LIGHTING" }
  | { type: "SELECT_PRODUCT"; productId: string }
  | { type: "SET_QUANTITY"; quantity: number }
  | { type: "SUBMIT_ADDRESS"; address: string }
  | { type: "SELECT_WALLET_PAYMENT" }
  | { type: "SELECT_MOBILE_MONEY" }
  | { type: "SELECT_AGENT_PAYMENT" }
  | { type: "CONFIRM_ORDER" }
  | { type: "PAYMENT_SUCCESS"; transactionId: string; orderId: string }
  | { type: "PAYMENT_FAILED"; error: string }
  | { type: "INVENTORY_AVAILABLE" }
  | { type: "INVENTORY_UNAVAILABLE" }
  | { type: "VIEW_ORDER_DETAILS" }
  | { type: "NEW_PURCHASE" }
  | { type: "BACK_TO_CATEGORIES" }
  | { type: "BACK_TO_PRODUCTS" }
  | { type: "BACK_TO_MENU" }
  | { type: "BACK_TO_MAIN" }
  | { type: "ERROR"; error: string };

// Mock product catalog
const PRODUCT_CATALOG = {
  solar: [
    {
      id: "SOL001",
      name: "20W Solar Home System",
      price: 150,
      category: "solar",
      inStock: true,
      description: "Basic solar system with LED lights",
    },
    {
      id: "SOL002",
      name: "50W Solar Home System",
      price: 300,
      category: "solar",
      inStock: true,
      description: "Mid-range solar system with phone charging",
    },
    {
      id: "SOL003",
      name: "100W Solar Home System",
      price: 500,
      category: "solar",
      inStock: false,
      description: "Premium solar system with TV support",
    },
  ],
  cookstoves: [
    {
      id: "COOK001",
      name: "Efficient Cookstove Model A",
      price: 75,
      category: "cookstoves",
      inStock: true,
      description: "90% efficiency clean cookstove",
    },
    {
      id: "COOK002",
      name: "Premium Cookstove Model B",
      price: 120,
      category: "cookstoves",
      inStock: true,
      description: "Premium model with temperature control",
    },
  ],
  water: [
    {
      id: "WAT001",
      name: "Water Purification System",
      price: 200,
      category: "water",
      inStock: true,
      description: "99.9% filtration efficiency",
    },
    {
      id: "WAT002",
      name: "Portable Water Filter",
      price: 50,
      category: "water",
      inStock: true,
      description: "Compact portable filter",
    },
  ],
  lighting: [
    {
      id: "LED001",
      name: "LED Lantern",
      price: 25,
      category: "lighting",
      inStock: true,
      description: "50,000 hour lifespan LED lantern",
    },
    {
      id: "LED002",
      name: "Solar LED String Lights",
      price: 40,
      category: "lighting",
      inStock: true,
      description: "Solar-powered string lights",
    },
  ],
};

export const purchaseMachine = setup({
  types: {
    context: {} as PurchaseContext,
    events: {} as PurchaseEvent,
    input: {} as {
      sessionId: string;
      phoneNumber: string;
      serviceCode: string;
      walletId?: string;
      currentBalance?: number;
    },
  },

  actions: {
    initializeContext: assign({
      orderStatus: undefined,
      error: undefined,
      validationError: undefined,
    }),

    setSelectedCategory: assign({
      selectedCategory: ({ event }) => {
        if (event.type === "SELECT_SOLAR") return "solar";
        if (event.type === "SELECT_COOKSTOVES") return "cookstoves";
        if (event.type === "SELECT_WATER") return "water";
        if (event.type === "SELECT_LIGHTING") return "lighting";
        return undefined;
      },
    }),

    setSelectedProduct: assign({
      selectedProduct: ({ event, context }) => {
        if (event.type === "SELECT_PRODUCT" && context.selectedCategory) {
          const products = PRODUCT_CATALOG[context.selectedCategory];
          return products.find(p => p.id === event.productId);
        }
        return undefined;
      },
    }),

    setQuantity: assign({
      quantity: ({ event }) =>
        event.type === "SET_QUANTITY" ? event.quantity : undefined,
      totalAmount: ({ event, context }) => {
        if (event.type === "SET_QUANTITY" && context.selectedProduct) {
          return event.quantity * context.selectedProduct.price;
        }
        return context.totalAmount;
      },
    }),

    setDeliveryAddress: assign({
      deliveryAddress: ({ event }) =>
        event.type === "SUBMIT_ADDRESS" ? event.address : undefined,
    }),

    setPaymentMethod: assign({
      paymentMethod: ({ event }) => {
        if (event.type === "SELECT_WALLET_PAYMENT") return "wallet";
        if (event.type === "SELECT_MOBILE_MONEY") return "mobile_money";
        if (event.type === "SELECT_AGENT_PAYMENT") return "agent";
        return undefined;
      },
    }),

    createOrder: assign({
      orderId: () =>
        `ORD_${Date.now()}_${Math.random().toString(36).substr(2, 6)}`,
      orderStatus: "pending" as const,
    }),

    confirmOrder: assign({
      orderStatus: "confirmed" as const,
    }),

    handlePaymentSuccess: assign({
      orderStatus: "completed" as const,
      transactionId: ({ event }) =>
        event.type === "PAYMENT_SUCCESS" ? event.transactionId : undefined,
      orderId: ({ event }) =>
        event.type === "PAYMENT_SUCCESS" ? event.orderId : undefined,
      currentBalance: ({ context }) => {
        if (
          context.paymentMethod === "wallet" &&
          context.currentBalance &&
          context.totalAmount
        ) {
          return context.currentBalance - context.totalAmount;
        }
        return context.currentBalance;
      },
    }),

    handlePaymentFailure: assign({
      orderStatus: "failed" as const,
      error: ({ event }) =>
        event.type === "PAYMENT_FAILED" ? event.error : "Payment failed",
    }),

    resetForNewPurchase: assign({
      selectedCategory: undefined,
      selectedProduct: undefined,
      quantity: undefined,
      totalAmount: undefined,
      deliveryAddress: undefined,
      orderId: undefined,
      orderStatus: undefined,
      paymentMethod: undefined,
      transactionId: undefined,
      error: undefined,
      validationError: undefined,
    }),

    setError: assign({
      error: ({ event }) =>
        event.type === "ERROR" ? event.error : "An error occurred",
    }),

    setValidationError: assign({
      validationError: ({ context }) => {
        if (!context.quantity || context.quantity <= 0) {
          return "Please enter a valid quantity";
        }
        if (context.quantity > 10) {
          return "Maximum quantity is 10 per order";
        }
        if (
          !context.deliveryAddress ||
          context.deliveryAddress.trim().length < 10
        ) {
          return "Please provide a complete delivery address";
        }
        if (
          context.paymentMethod === "wallet" &&
          context.currentBalance &&
          context.totalAmount &&
          context.currentBalance < context.totalAmount
        ) {
          return "Insufficient wallet balance";
        }
        return undefined;
      },
    }),

    clearErrors: assign({
      error: undefined,
      validationError: undefined,
    }),
  },

  guards: {
    isSolarSelected: ({ context }) => context.selectedCategory === "solar",
    isCookstovesSelected: ({ context }) =>
      context.selectedCategory === "cookstoves",
    isWaterSelected: ({ context }) => context.selectedCategory === "water",
    isLightingSelected: ({ context }) =>
      context.selectedCategory === "lighting",

    isProductInStock: ({ context }) =>
      Boolean(context.selectedProduct?.inStock),

    isValidQuantity: ({ context }) =>
      Boolean(
        context.quantity && context.quantity > 0 && context.quantity <= 10
      ),

    isValidAddress: ({ context }) =>
      Boolean(
        context.deliveryAddress && context.deliveryAddress.trim().length >= 10
      ),

    hasSufficientBalance: ({ context }) => {
      if (
        context.paymentMethod === "wallet" &&
        context.currentBalance &&
        context.totalAmount
      ) {
        return context.currentBalance >= context.totalAmount;
      }
      return true; // For non-wallet payments, assume sufficient funds
    },

    isWalletPayment: ({ context }) => context.paymentMethod === "wallet",
    isMobileMoneyPayment: ({ context }) =>
      context.paymentMethod === "mobile_money",
    isAgentPayment: ({ context }) => context.paymentMethod === "agent",

    hasOrderId: ({ context }) => Boolean(context.orderId),
    isOrderCompleted: ({ context }) => context.orderStatus === "completed",
    isOrderFailed: ({ context }) => context.orderStatus === "failed",

    hasError: ({ context }) => Boolean(context.error),
    hasValidationError: ({ context }) => Boolean(context.validationError),
  },
}).createMachine({
  id: "purchaseMachine",
  initial: "idle",

  context: ({ input }) => ({
    sessionId: input?.sessionId || "",
    phoneNumber: input?.phoneNumber || "",
    serviceCode: input?.serviceCode || "",
    walletId: input?.walletId,
    currentBalance: input?.currentBalance || 0,
  }),

  states: {
    // Initial state - waiting for start
    idle: {
      on: {
        START: {
          target: "categorySelection",
          actions: "initializeContext",
        },
      },
    },

    // Product category selection
    categorySelection: {
      entry: "clearErrors",
      on: {
        SELECT_SOLAR: {
          target: "productSelection",
          actions: "setSelectedCategory",
        },
        SELECT_COOKSTOVES: {
          target: "productSelection",
          actions: "setSelectedCategory",
        },
        SELECT_WATER: {
          target: "productSelection",
          actions: "setSelectedCategory",
        },
        SELECT_LIGHTING: {
          target: "productSelection",
          actions: "setSelectedCategory",
        },
        BACK_TO_MENU: "routeToUserServices",
        BACK_TO_MAIN: "routeToMain",
        ERROR: {
          target: "error",
          actions: "setError",
        },
      },
    },

    // Product selection within category
    productSelection: {
      on: {
        SELECT_PRODUCT: [
          {
            target: "quantitySelection",
            actions: "setSelectedProduct",
            guard: "isProductInStock",
          },
          {
            target: "outOfStock",
            actions: "setSelectedProduct",
          },
        ],
        BACK_TO_CATEGORIES: "categorySelection",
        BACK_TO_MAIN: "routeToMain",
        ERROR: {
          target: "error",
          actions: "setError",
        },
      },
    },

    // Out of stock state
    outOfStock: {
      on: {
        BACK_TO_PRODUCTS: "productSelection",
        BACK_TO_CATEGORIES: "categorySelection",
        BACK_TO_MAIN: "routeToMain",
      },
    },

    // Quantity selection
    quantitySelection: {
      on: {
        SET_QUANTITY: [
          {
            target: "deliveryAddress",
            actions: "setQuantity",
            guard: "isValidQuantity",
          },
          {
            target: "quantitySelection",
            actions: "setValidationError",
          },
        ],
        BACK_TO_PRODUCTS: "productSelection",
        BACK_TO_MAIN: "routeToMain",
        ERROR: {
          target: "error",
          actions: "setError",
        },
      },
    },

    // Delivery address entry
    deliveryAddress: {
      on: {
        SUBMIT_ADDRESS: [
          {
            target: "paymentMethodSelection",
            actions: "setDeliveryAddress",
            guard: "isValidAddress",
          },
          {
            target: "deliveryAddress",
            actions: "setValidationError",
          },
        ],
        BACK_TO_PRODUCTS: "quantitySelection",
        BACK_TO_MAIN: "routeToMain",
        ERROR: {
          target: "error",
          actions: "setError",
        },
      },
    },

    // Payment method selection
    paymentMethodSelection: {
      on: {
        SELECT_WALLET_PAYMENT: [
          {
            target: "orderConfirmation",
            actions: "setPaymentMethod",
            guard: "hasSufficientBalance",
          },
          {
            target: "paymentMethodSelection",
            actions: "setValidationError",
          },
        ],
        SELECT_MOBILE_MONEY: {
          target: "orderConfirmation",
          actions: "setPaymentMethod",
        },
        SELECT_AGENT_PAYMENT: {
          target: "orderConfirmation",
          actions: "setPaymentMethod",
        },
        BACK_TO_PRODUCTS: "deliveryAddress",
        BACK_TO_MAIN: "routeToMain",
        ERROR: {
          target: "error",
          actions: "setError",
        },
      },
    },

    // Order confirmation
    orderConfirmation: {
      entry: "createOrder",
      on: {
        CONFIRM_ORDER: {
          target: "processingPayment",
          actions: "confirmOrder",
        },
        BACK_TO_PRODUCTS: "paymentMethodSelection",
        BACK_TO_MAIN: "routeToMain",
        ERROR: {
          target: "error",
          actions: "setError",
        },
      },
    },

    // Processing payment
    processingPayment: {
      on: {
        PAYMENT_SUCCESS: {
          target: "orderCompleted",
          actions: "handlePaymentSuccess",
        },
        PAYMENT_FAILED: {
          target: "orderFailed",
          actions: "handlePaymentFailure",
        },
        ERROR: {
          target: "error",
          actions: "setError",
        },
      },
    },

    // Order completed successfully
    orderCompleted: {
      on: {
        VIEW_ORDER_DETAILS: "orderDetails",
        NEW_PURCHASE: {
          target: "categorySelection",
          actions: "resetForNewPurchase",
        },
        BACK_TO_MENU: "routeToUserServices",
        BACK_TO_MAIN: "routeToMain",
      },
    },

    // Order details view
    orderDetails: {
      on: {
        NEW_PURCHASE: {
          target: "categorySelection",
          actions: "resetForNewPurchase",
        },
        BACK_TO_MENU: "routeToUserServices",
        BACK_TO_MAIN: "routeToMain",
      },
    },

    // Order failed
    orderFailed: {
      on: {
        NEW_PURCHASE: {
          target: "categorySelection",
          actions: "resetForNewPurchase",
        },
        BACK_TO_MENU: "routeToUserServices",
        BACK_TO_MAIN: "routeToMain",
      },
    },

    // Error state
    error: {
      entry: "setError",
      on: {
        START: {
          target: "categorySelection",
          actions: "clearErrors",
        },
        BACK_TO_MENU: "routeToUserServices",
        BACK_TO_MAIN: "routeToMain",
      },
    },

    // Route back to user services
    routeToUserServices: {
      type: "final",
      output: {
        route: "userServices" as const,
        context: ({ context }: { context: PurchaseContext }) => context,
      },
    },

    // Route back to main menu
    routeToMain: {
      type: "final",
      output: {
        route: "main" as const,
        context: ({ context }: { context: PurchaseContext }) => context,
      },
    },
  },
});

export type PurchaseMachine = typeof purchaseMachine;
