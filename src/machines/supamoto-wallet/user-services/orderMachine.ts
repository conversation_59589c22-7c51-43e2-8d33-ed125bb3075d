import { setup, assign } from "xstate";

/**
 * Order Machine - Order Management and Status Tracking
 *
 * Handles:
 * - Order status checking and tracking
 * - Order history viewing
 * - Order modification requests
 * - Delivery status updates
 * - Order cancellation flows
 *
 * Entry Points: START
 * Exit Points: Outputs routing decision for main orchestrator
 */

export interface OrderContext {
  sessionId: string;
  phoneNumber: string;
  serviceCode: string;
  walletId?: string;
  selectedAction?:
    | "view_orders"
    | "track_order"
    | "modify_order"
    | "cancel_order";
  orders?: Array<{
    id: string;
    productName: string;
    quantity: number;
    totalAmount: number;
    status:
      | "pending"
      | "confirmed"
      | "processing"
      | "shipped"
      | "delivered"
      | "cancelled";
    orderDate: string;
    deliveryAddress: string;
    trackingNumber?: string;
    estimatedDelivery?: string;
  }>;
  selectedOrderId?: string;
  selectedOrder?: {
    id: string;
    productName: string;
    quantity: number;
    totalAmount: number;
    status:
      | "pending"
      | "confirmed"
      | "processing"
      | "shipped"
      | "delivered"
      | "cancelled";
    orderDate: string;
    deliveryAddress: string;
    trackingNumber?: string;
    estimatedDelivery?: string;
  };
  modificationRequest?: {
    type: "address_change" | "quantity_change" | "cancel_request";
    newAddress?: string;
    newQuantity?: number;
    reason?: string;
  };
  currentPage?: number;
  totalPages?: number;
  error?: string;
  validationError?: string;
}

export type OrderEvent =
  | { type: "START" }
  | { type: "VIEW_ORDERS" }
  | { type: "TRACK_ORDER" }
  | { type: "MODIFY_ORDER" }
  | { type: "CANCEL_ORDER" }
  | { type: "SELECT_ORDER"; orderId: string }
  | { type: "SUBMIT_ORDER_ID"; orderId: string }
  | { type: "REQUEST_ADDRESS_CHANGE"; newAddress: string }
  | { type: "REQUEST_QUANTITY_CHANGE"; newQuantity: number }
  | { type: "REQUEST_CANCELLATION"; reason: string }
  | { type: "CONFIRM_MODIFICATION" }
  | { type: "CONFIRM_CANCELLATION" }
  | { type: "MODIFICATION_SUCCESS" }
  | { type: "MODIFICATION_FAILED"; error: string }
  | { type: "NEXT_PAGE" }
  | { type: "PREVIOUS_PAGE" }
  | { type: "REFRESH_STATUS" }
  | { type: "NEW_ORDER_ACTION" }
  | { type: "BACK_TO_ORDERS" }
  | { type: "BACK_TO_MENU" }
  | { type: "BACK_TO_MAIN" }
  | { type: "ERROR"; error: string };

// Mock order data
const MOCK_ORDERS = [
  {
    id: "ORD_001",
    productName: "20W Solar Home System",
    quantity: 1,
    totalAmount: 150,
    status: "shipped" as const,
    orderDate: "2024-01-15",
    deliveryAddress: "123 Solar Street, Lusaka, Zambia",
    trackingNumber: "TRK_001_789",
    estimatedDelivery: "2024-01-20",
  },
  {
    id: "ORD_002",
    productName: "Efficient Cookstove Model A",
    quantity: 2,
    totalAmount: 150,
    status: "processing" as const,
    orderDate: "2024-01-18",
    deliveryAddress: "456 Cook Avenue, Ndola, Zambia",
    estimatedDelivery: "2024-01-25",
  },
  {
    id: "ORD_003",
    productName: "Water Purification System",
    quantity: 1,
    totalAmount: 200,
    status: "delivered" as const,
    orderDate: "2024-01-10",
    deliveryAddress: "789 Water Lane, Kitwe, Zambia",
    trackingNumber: "TRK_003_456",
  },
];

export const orderMachine = setup({
  types: {
    context: {} as OrderContext,
    events: {} as OrderEvent,
    input: {} as {
      sessionId: string;
      phoneNumber: string;
      serviceCode: string;
      walletId?: string;
    },
  },

  actions: {
    initializeContext: assign({
      currentPage: 1,
      totalPages: 1,
      error: undefined,
      validationError: undefined,
    }),

    setSelectedAction: assign({
      selectedAction: ({ event }) => {
        if (event.type === "VIEW_ORDERS") return "view_orders";
        if (event.type === "TRACK_ORDER") return "track_order";
        if (event.type === "MODIFY_ORDER") return "modify_order";
        if (event.type === "CANCEL_ORDER") return "cancel_order";
        return undefined;
      },
    }),

    loadOrders: assign({
      orders: () => MOCK_ORDERS,
      totalPages: () => Math.ceil(MOCK_ORDERS.length / 3), // 3 orders per page
      currentPage: 1,
    }),

    setSelectedOrder: assign({
      selectedOrderId: ({ event }) => {
        if (event.type === "SELECT_ORDER") return event.orderId;
        if (event.type === "SUBMIT_ORDER_ID") return event.orderId;
        return undefined;
      },
      selectedOrder: ({ event, context }) => {
        const orderId =
          event.type === "SELECT_ORDER"
            ? event.orderId
            : event.type === "SUBMIT_ORDER_ID"
              ? event.orderId
              : undefined;
        if (orderId && context.orders) {
          return context.orders.find(order => order.id === orderId);
        }
        return undefined;
      },
    }),

    setModificationRequest: assign({
      modificationRequest: ({ event }) => {
        if (event.type === "REQUEST_ADDRESS_CHANGE") {
          return {
            type: "address_change" as const,
            newAddress: event.newAddress,
          };
        }
        if (event.type === "REQUEST_QUANTITY_CHANGE") {
          return {
            type: "quantity_change" as const,
            newQuantity: event.newQuantity,
          };
        }
        if (event.type === "REQUEST_CANCELLATION") {
          return {
            type: "cancel_request" as const,
            reason: event.reason,
          };
        }
        return undefined;
      },
    }),

    processModification: assign({
      selectedOrder: ({ context }) => {
        if (context.selectedOrder && context.modificationRequest) {
          const updatedOrder = { ...context.selectedOrder };

          if (
            context.modificationRequest.type === "address_change" &&
            context.modificationRequest.newAddress
          ) {
            updatedOrder.deliveryAddress =
              context.modificationRequest.newAddress;
          }

          if (
            context.modificationRequest.type === "quantity_change" &&
            context.modificationRequest.newQuantity
          ) {
            updatedOrder.quantity = context.modificationRequest.newQuantity;
            // Recalculate total (simplified - would need product price)
            updatedOrder.totalAmount =
              (updatedOrder.totalAmount / context.selectedOrder.quantity) *
              context.modificationRequest.newQuantity;
          }

          if (context.modificationRequest.type === "cancel_request") {
            updatedOrder.status = "cancelled";
          }

          return updatedOrder;
        }
        return context.selectedOrder;
      },
      orders: ({ context }) => {
        if (
          context.orders &&
          context.selectedOrder &&
          context.modificationRequest
        ) {
          return context.orders.map(order =>
            order.id === context.selectedOrder?.id
              ? { ...context.selectedOrder }
              : order
          );
        }
        return context.orders;
      },
    }),

    nextPage: assign({
      currentPage: ({ context }) =>
        Math.min((context.currentPage || 1) + 1, context.totalPages || 1),
    }),

    previousPage: assign({
      currentPage: ({ context }) => Math.max((context.currentPage || 1) - 1, 1),
    }),

    refreshOrderStatus: assign({
      selectedOrder: ({ context }) => {
        // Simulate status update
        if (context.selectedOrder) {
          const statuses = [
            "pending",
            "confirmed",
            "processing",
            "shipped",
            "delivered",
          ];
          const currentIndex = statuses.indexOf(context.selectedOrder.status);
          const nextStatus =
            currentIndex < statuses.length - 1
              ? statuses[currentIndex + 1]
              : context.selectedOrder.status;

          return {
            ...context.selectedOrder,
            status: nextStatus as any,
          };
        }
        return context.selectedOrder;
      },
    }),

    resetForNewAction: assign({
      selectedAction: undefined,
      selectedOrderId: undefined,
      selectedOrder: undefined,
      modificationRequest: undefined,
      currentPage: 1,
      error: undefined,
      validationError: undefined,
    }),

    setError: assign({
      error: ({ event }) =>
        event.type === "ERROR" ? event.error : "An error occurred",
    }),

    setValidationError: assign({
      validationError: ({ context, event }) => {
        if (
          event.type === "SUBMIT_ORDER_ID" &&
          (!event.orderId || event.orderId.trim().length === 0)
        ) {
          return "Please enter a valid order ID";
        }
        if (
          event.type === "REQUEST_ADDRESS_CHANGE" &&
          (!event.newAddress || event.newAddress.trim().length < 10)
        ) {
          return "Please provide a complete delivery address";
        }
        if (
          event.type === "REQUEST_QUANTITY_CHANGE" &&
          (!event.newQuantity ||
            event.newQuantity <= 0 ||
            event.newQuantity > 10)
        ) {
          return "Please enter a valid quantity (1-10)";
        }
        if (
          event.type === "REQUEST_CANCELLATION" &&
          (!event.reason || event.reason.trim().length < 5)
        ) {
          return "Please provide a reason for cancellation";
        }
        if (context.selectedOrder?.status === "delivered") {
          return "Cannot modify delivered orders";
        }
        if (context.selectedOrder?.status === "cancelled") {
          return "Cannot modify cancelled orders";
        }
        return undefined;
      },
    }),

    clearErrors: assign({
      error: undefined,
      validationError: undefined,
    }),
  },

  guards: {
    isViewOrdersSelected: ({ context }) =>
      context.selectedAction === "view_orders",
    isTrackOrderSelected: ({ context }) =>
      context.selectedAction === "track_order",
    isModifyOrderSelected: ({ context }) =>
      context.selectedAction === "modify_order",
    isCancelOrderSelected: ({ context }) =>
      context.selectedAction === "cancel_order",

    hasOrders: ({ context }) =>
      Boolean(context.orders && context.orders.length > 0),
    hasSelectedOrder: ({ context }) => Boolean(context.selectedOrder),

    isValidOrderId: ({ event, context }) => {
      if (event.type === "SUBMIT_ORDER_ID") {
        return Boolean(
          event.orderId &&
            context.orders?.some(order => order.id === event.orderId)
        );
      }
      return false;
    },

    canModifyOrder: ({ context }) => {
      if (context.selectedOrder) {
        return !["delivered", "cancelled"].includes(
          context.selectedOrder.status
        );
      }
      return false;
    },

    canCancelOrder: ({ context }) => {
      if (context.selectedOrder) {
        return !["delivered", "cancelled", "shipped"].includes(
          context.selectedOrder.status
        );
      }
      return false;
    },

    isValidAddressChange: ({ event }) =>
      event.type === "REQUEST_ADDRESS_CHANGE" &&
      Boolean(event.newAddress && event.newAddress.trim().length >= 10),

    isValidQuantityChange: ({ event }) =>
      event.type === "REQUEST_QUANTITY_CHANGE" &&
      Boolean(
        event.newQuantity && event.newQuantity > 0 && event.newQuantity <= 10
      ),

    isValidCancellationReason: ({ event }) =>
      event.type === "REQUEST_CANCELLATION" &&
      Boolean(event.reason && event.reason.trim().length >= 5),

    hasNextPage: ({ context }) =>
      (context.currentPage || 1) < (context.totalPages || 1),

    hasPreviousPage: ({ context }) => (context.currentPage || 1) > 1,

    hasError: ({ context }) => Boolean(context.error),
    hasValidationError: ({ context }) => Boolean(context.validationError),
  },
}).createMachine({
  id: "orderMachine",
  initial: "idle",

  context: ({ input }) => ({
    sessionId: input?.sessionId || "",
    phoneNumber: input?.phoneNumber || "",
    serviceCode: input?.serviceCode || "",
    walletId: input?.walletId,
  }),

  states: {
    // Initial state - waiting for start
    idle: {
      on: {
        START: {
          target: "actionSelection",
          actions: "initializeContext",
        },
      },
    },

    // Order action selection menu
    actionSelection: {
      entry: "clearErrors",
      on: {
        VIEW_ORDERS: {
          target: "loadingOrders",
          actions: "setSelectedAction",
        },
        TRACK_ORDER: {
          target: "orderIdEntry",
          actions: "setSelectedAction",
        },
        MODIFY_ORDER: {
          target: "orderIdEntry",
          actions: "setSelectedAction",
        },
        CANCEL_ORDER: {
          target: "orderIdEntry",
          actions: "setSelectedAction",
        },
        BACK_TO_MENU: "routeToUserServices",
        BACK_TO_MAIN: "routeToMain",
        ERROR: {
          target: "error",
          actions: "setError",
        },
      },
    },

    // Loading orders
    loadingOrders: {
      entry: "loadOrders",
      always: "displayingOrders",
    },

    // Displaying orders list
    displayingOrders: {
      on: {
        SELECT_ORDER: {
          target: "orderDetails",
          actions: "setSelectedOrder",
        },
        NEXT_PAGE: {
          guard: "hasNextPage",
          actions: "nextPage",
        },
        PREVIOUS_PAGE: {
          guard: "hasPreviousPage",
          actions: "previousPage",
        },
        BACK_TO_MENU: "actionSelection",
        BACK_TO_MAIN: "routeToMain",
        ERROR: {
          target: "error",
          actions: "setError",
        },
      },
    },

    // Order ID entry for tracking/modification
    orderIdEntry: {
      on: {
        SUBMIT_ORDER_ID: [
          {
            target: "orderDetails",
            actions: "setSelectedOrder",
            guard: "isValidOrderId",
          },
          {
            target: "orderIdEntry",
            actions: "setValidationError",
          },
        ],
        BACK_TO_MENU: "actionSelection",
        BACK_TO_MAIN: "routeToMain",
        ERROR: {
          target: "error",
          actions: "setError",
        },
      },
    },

    // Order details display
    orderDetails: {
      on: {
        REFRESH_STATUS: {
          actions: "refreshOrderStatus",
        },
        REQUEST_ADDRESS_CHANGE: [
          {
            target: "modificationConfirmation",
            actions: "setModificationRequest",
            guard: "isValidAddressChange",
          },
          {
            target: "orderDetails",
            actions: "setValidationError",
          },
        ],
        REQUEST_QUANTITY_CHANGE: [
          {
            target: "modificationConfirmation",
            actions: "setModificationRequest",
            guard: "isValidQuantityChange",
          },
          {
            target: "orderDetails",
            actions: "setValidationError",
          },
        ],
        REQUEST_CANCELLATION: [
          {
            target: "cancellationConfirmation",
            actions: "setModificationRequest",
            guard: "isValidCancellationReason",
          },
          {
            target: "orderDetails",
            actions: "setValidationError",
          },
        ],
        BACK_TO_ORDERS: "displayingOrders",
        BACK_TO_MENU: "actionSelection",
        BACK_TO_MAIN: "routeToMain",
        ERROR: {
          target: "error",
          actions: "setError",
        },
      },
    },

    // Modification confirmation
    modificationConfirmation: {
      on: {
        CONFIRM_MODIFICATION: {
          target: "processingModification",
        },
        BACK_TO_ORDERS: "orderDetails",
        BACK_TO_MAIN: "routeToMain",
        ERROR: {
          target: "error",
          actions: "setError",
        },
      },
    },

    // Cancellation confirmation
    cancellationConfirmation: {
      on: {
        CONFIRM_CANCELLATION: {
          target: "processingModification",
        },
        BACK_TO_ORDERS: "orderDetails",
        BACK_TO_MAIN: "routeToMain",
        ERROR: {
          target: "error",
          actions: "setError",
        },
      },
    },

    // Processing modification
    processingModification: {
      entry: "processModification",
      on: {
        MODIFICATION_SUCCESS: "modificationSuccess",
        MODIFICATION_FAILED: {
          target: "modificationFailed",
          actions: "setError",
        },
      },
      after: {
        1000: "modificationSuccess", // Simulate processing time
      },
    },

    // Modification success
    modificationSuccess: {
      on: {
        NEW_ORDER_ACTION: {
          target: "actionSelection",
          actions: "resetForNewAction",
        },
        BACK_TO_ORDERS: "displayingOrders",
        BACK_TO_MAIN: "routeToMain",
      },
    },

    // Modification failed
    modificationFailed: {
      on: {
        NEW_ORDER_ACTION: {
          target: "actionSelection",
          actions: "resetForNewAction",
        },
        BACK_TO_ORDERS: "orderDetails",
        BACK_TO_MAIN: "routeToMain",
      },
    },

    // Error state
    error: {
      entry: "setError",
      on: {
        START: {
          target: "actionSelection",
          actions: "clearErrors",
        },
        BACK_TO_MENU: "routeToUserServices",
        BACK_TO_MAIN: "routeToMain",
      },
    },

    // Route back to user services
    routeToUserServices: {
      type: "final",
      output: {
        route: "userServices" as const,
        context: ({ context }: { context: OrderContext }) => context,
      },
    },

    // Route back to main menu
    routeToMain: {
      type: "final",
      output: {
        route: "main" as const,
        context: ({ context }: { context: OrderContext }) => context,
      },
    },
  },
});

export type OrderMachine = typeof orderMachine;
