/* eslint-disable no-console */
import { createActor } from "xstate";
import { accountMachine } from "./accountMachine.js";

/**
 * Account Machine Demo
 *
 * Demonstrates the account machine functionality including:
 * - Profile management and updates
 * - Security settings and PIN changes
 * - Notification preferences
 * - Account verification flows
 * - Settings management
 */

console.log("🚀 Account Machine Demo\n");

const mockInput = {
  sessionId: "demo-session-account-789",
  phoneNumber: "+************",
  serviceCode: "*2233#",
  walletId: "C21009802",
};

// Demo 1: Profile Management
console.log("=".repeat(50));
console.log("DEMO 1: Profile Management");
console.log("=".repeat(50));

const actor1 = createActor(accountMachine, { input: mockInput });
actor1.subscribe(snapshot => {
  console.log(`📍 State: ${snapshot.value}`);
  if (snapshot.context.selectedAction) {
    console.log(`🎯 Action: ${snapshot.context.selectedAction}`);
  }
  if (snapshot.context.profile) {
    const profile = snapshot.context.profile;
    console.log(`👤 Profile: ${profile.firstName} ${profile.lastName}`);
    console.log(`📧 Email: ${profile.email}`);
    console.log(`🏠 Address: ${profile.address}`);
    console.log(`💼 Occupation: ${profile.occupation}`);
  }
  if (snapshot.context.updateField && snapshot.context.updateValue) {
    console.log(
      `🔄 Updating ${snapshot.context.updateField}: ${snapshot.context.updateValue}`
    );
  }
  if (snapshot.context.validationError) {
    console.log(`⚠️ Validation Error: ${snapshot.context.validationError}`);
  }
  console.log("");
});

actor1.start();
actor1.send({ type: "START" });
console.log("📱 User started account management");

actor1.send({ type: "MANAGE_PROFILE" });
console.log("📱 User selected profile management");

actor1.send({ type: "UPDATE_OCCUPATION", value: "Software Engineer" });
console.log("📱 User updated occupation");

actor1.send({ type: "CONFIRM_UPDATE" });
console.log("📱 User confirmed profile update");

setTimeout(() => {
  console.log("✅ Profile management complete!\n");

  // Demo 2: Security Settings and PIN Change
  console.log("=".repeat(50));
  console.log("DEMO 2: Security Settings and PIN Change");
  console.log("=".repeat(50));

  const actor2 = createActor(accountMachine, { input: mockInput });
  actor2.subscribe(snapshot => {
    console.log(`📍 State: ${snapshot.value}`);
    if (snapshot.context.securitySettings) {
      const security = snapshot.context.securitySettings;
      console.log(`🔐 PIN Enabled: ${security.pinEnabled}`);
      console.log(`👆 Biometric: ${security.biometricEnabled}`);
      console.log(`🔒 Two-Factor: ${security.twoFactorEnabled}`);
      console.log(`⏱️ Session Timeout: ${security.sessionTimeout} minutes`);
    }
    if (snapshot.context.newPin) {
      console.log(`🔑 New PIN: ${"*".repeat(snapshot.context.newPin.length)}`);
    }
    if (snapshot.context.validationError) {
      console.log(`⚠️ Validation Error: ${snapshot.context.validationError}`);
    }
    console.log("");
  });

  actor2.start();
  actor2.send({ type: "START" });
  actor2.send({ type: "SECURITY_SETTINGS" });
  console.log("📱 User selected security settings");

  // Toggle biometric
  actor2.send({ type: "TOGGLE_BIOMETRIC" });
  console.log("📱 User toggled biometric authentication");

  // Change PIN
  actor2.send({ type: "CHANGE_PIN" });
  console.log("📱 User initiated PIN change");

  actor2.send({ type: "SUBMIT_CURRENT_PIN", pin: "1234" });
  console.log("📱 User entered current PIN");

  actor2.send({ type: "SUBMIT_NEW_PIN", pin: "5678" });
  console.log("📱 User entered new PIN");

  actor2.send({ type: "SUBMIT_CONFIRM_PIN", pin: "5678" });
  console.log("📱 User confirmed new PIN");

  setTimeout(() => {
    console.log("✅ Security settings complete!\n");

    // Demo 3: Notification Settings
    console.log("=".repeat(50));
    console.log("DEMO 3: Notification Settings");
    console.log("=".repeat(50));

    const actor3 = createActor(accountMachine, { input: mockInput });
    actor3.subscribe(snapshot => {
      console.log(`📍 State: ${snapshot.value}`);
      if (snapshot.context.notificationSettings) {
        const notifications = snapshot.context.notificationSettings;
        console.log(`📱 SMS: ${notifications.smsEnabled}`);
        console.log(`📧 Email: ${notifications.emailEnabled}`);
        console.log(`🔔 Push: ${notifications.pushEnabled}`);
        console.log(
          `💰 Transaction Alerts: ${notifications.transactionAlerts}`
        );
        console.log(`📢 Marketing: ${notifications.marketingMessages}`);
      }
      console.log("");
    });

    actor3.start();
    actor3.send({ type: "START" });
    actor3.send({ type: "NOTIFICATION_SETTINGS" });
    console.log("📱 User selected notification settings");

    actor3.send({ type: "TOGGLE_PUSH_NOTIFICATIONS" });
    console.log("📱 User enabled push notifications");

    actor3.send({ type: "TOGGLE_MARKETING_MESSAGES" });
    console.log("📱 User disabled marketing messages");

    console.log("✅ Notification settings complete!\n");

    // Demo 4: Account Verification
    console.log("=".repeat(50));
    console.log("DEMO 4: Account Verification");
    console.log("=".repeat(50));

    const actor4 = createActor(accountMachine, { input: mockInput });
    actor4.subscribe(snapshot => {
      console.log(`📍 State: ${snapshot.value}`);
      if (snapshot.context.verificationStatus) {
        const verification = snapshot.context.verificationStatus;
        console.log(`📞 Phone Verified: ${verification.phoneVerified}`);
        console.log(`📧 Email Verified: ${verification.emailVerified}`);
        console.log(`🆔 ID Verified: ${verification.idVerified}`);
        console.log(`🏠 Address Verified: ${verification.addressVerified}`);
        console.log(`📊 KYC Level: ${verification.kycLevel}`);
      }
      if (snapshot.context.verificationCode) {
        console.log(
          `🔢 Verification Code: ${snapshot.context.verificationCode}`
        );
      }
      console.log("");
    });

    actor4.start();
    actor4.send({ type: "START" });
    actor4.send({ type: "VERIFICATION_STATUS" });
    console.log("📱 User checked verification status");

    actor4.send({ type: "VERIFY_EMAIL" });
    console.log("📱 User initiated email verification");

    actor4.send({ type: "SUBMIT_VERIFICATION_CODE", code: "123456" });
    console.log("📱 User entered verification code");

    setTimeout(() => {
      console.log("✅ Email verification complete!\n");

      // Demo 5: Validation Errors
      console.log("=".repeat(50));
      console.log("DEMO 5: Validation Errors");
      console.log("=".repeat(50));

      const actor5 = createActor(accountMachine, { input: mockInput });
      actor5.subscribe(snapshot => {
        console.log(`📍 State: ${snapshot.value}`);
        if (snapshot.context.validationError) {
          console.log(
            `⚠️ Validation Error: ${snapshot.context.validationError}`
          );
        }
        console.log("");
      });

      actor5.start();
      actor5.send({ type: "START" });
      actor5.send({ type: "MANAGE_PROFILE" });

      // Test invalid email
      actor5.send({ type: "UPDATE_EMAIL", value: "invalid-email" });
      console.log("📱 User entered invalid email format");

      // Test invalid ID number
      actor5.send({ type: "UPDATE_ID_NUMBER", value: "123" });
      console.log("📱 User entered invalid ID number");

      // Test invalid PIN
      actor5.send({ type: "SECURITY_SETTINGS" });
      actor5.send({ type: "CHANGE_PIN" });
      actor5.send({ type: "SUBMIT_CURRENT_PIN", pin: "1234" });
      actor5.send({ type: "SUBMIT_NEW_PIN", pin: "123" }); // Too short
      console.log("📱 User entered invalid PIN format");

      console.log("✅ Validation testing complete!\n");

      // Demo 6: Navigation Flow
      console.log("=".repeat(50));
      console.log("DEMO 6: Navigation Flow");
      console.log("=".repeat(50));

      const actor6 = createActor(accountMachine, { input: mockInput });
      actor6.subscribe(snapshot => {
        console.log(`📍 State: ${snapshot.value}`);
        if (snapshot.output) {
          console.log(`🎯 Output:`, snapshot.output);
        }
        console.log("");
      });

      actor6.start();
      actor6.send({ type: "START" });
      console.log("📱 User started account management");

      actor6.send({ type: "MANAGE_PROFILE" });
      console.log("📱 User selected profile management");

      actor6.send({ type: "BACK_TO_ACTIONS" });
      console.log("📱 User navigated back to action selection");

      actor6.send({ type: "BACK_TO_MAIN" });
      console.log("📱 User navigated back to main menu");

      console.log("✅ Navigation flow complete!\n");

      console.log("\n🎉 Account Machine Demo Complete!");
      console.log("\n📊 Machine Summary:");
      console.log("   • Handles comprehensive account management workflows");
      console.log("   • Manages profile updates with validation");
      console.log("   • Provides security settings and PIN management");
      console.log("   • Controls notification preferences");
      console.log("   • Supports account verification and KYC flows");
      console.log("   • Includes robust validation and error handling");
      console.log("   • Supports navigation through account management flows");
      console.log("   • Type-safe with XState v5 setup() pattern");
    }, 1200);
  }, 1200);
}, 1200);
