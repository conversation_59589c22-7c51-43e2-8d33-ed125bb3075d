import { describe, it, expect } from "vitest";
import { createActor } from "xstate";
import { faultMachine } from "./faultMachine.js";

describe("faultMachine - Smoke Tests", () => {
  const mockInput = {
    sessionId: "test-session-123",
    phoneNumber: "+260987654321",
    serviceCode: "*2233#",
    walletId: "*********",
    currentBalance: 100,
  };

  describe("Basic Functionality", () => {
    it("should create and start successfully", () => {
      const actor = createActor(faultMachine, { input: mockInput });
      actor.start();

      const snapshot = actor.getSnapshot();
      expect(snapshot).toBeDefined();
      expect(snapshot.context.sessionId).toBe("test-session-123");
    });

    it("should handle START event", () => {
      const actor = createActor(faultMachine, { input: mockInput });
      actor.start();

      actor.send({ type: "START" });

      const snapshot = actor.getSnapshot();
      expect(snapshot.value).toBeDefined();
      expect(snapshot.context).toBeDefined();
    });

    it("should handle basic navigation events", () => {
      const actor = createActor(faultMachine, { input: mockInput });
      actor.start();

      actor.send({ type: "START" });
      actor.send({ type: "BACK_TO_MAIN" });

      const snapshot = actor.getSnapshot();
      expect(snapshot).toBeDefined();
    });

    it("should handle error events", () => {
      const actor = createActor(faultMachine, { input: mockInput });
      actor.start();

      actor.send({ type: "START" });
      actor.send({ type: "ERROR", error: "Test error" });

      const snapshot = actor.getSnapshot();
      expect(snapshot).toBeDefined();
    });
  });
});
