import { setup, assign } from "xstate";

/**
 * Top-up Machine - Balance Top-up and Payment Processing
 *
 * Handles:
 * - Balance top-up workflows
 * - Payment method selection
 * - Payment processing states
 * - Confirmation and receipt flows
 * - Transaction validation
 *
 * Entry Points: START
 * Exit Points: Outputs routing decision for main orchestrator
 */

export interface TopupContext {
  sessionId: string;
  phoneNumber: string;
  serviceCode: string;
  walletId?: string;
  currentBalance?: number;
  topupAmount?: number;
  paymentMethod?: "mobile_money" | "bank_transfer" | "card" | "agent";
  mobileMoneyNumber?: string;
  transactionId?: string;
  transactionStatus?: "pending" | "processing" | "completed" | "failed";
  receiptNumber?: string;
  error?: string;
  validationError?: string;
}

export type TopupEvent =
  | { type: "START" }
  | { type: "SELECT_AMOUNT"; amount: number }
  | { type: "CUSTOM_AMOUNT"; amount: number }
  | { type: "SELECT_MOBILE_MONEY" }
  | { type: "SELECT_BANK_TRANSFER" }
  | { type: "SELECT_CARD" }
  | { type: "SELECT_AGENT" }
  | { type: "SUBMIT_MOBILE_NUMBER"; number: string }
  | { type: "CONFIRM_PAYMENT" }
  | { type: "PAYMENT_SUCCESS"; transactionId: string; receiptNumber: string }
  | { type: "PAYMENT_FAILED"; error: string }
  | { type: "RETRY_PAYMENT" }
  | { type: "VIEW_RECEIPT" }
  | { type: "NEW_TOPUP" }
  | { type: "BACK_TO_MENU" }
  | { type: "BACK_TO_MAIN" }
  | { type: "ERROR"; error: string };

export const topupMachine = setup({
  types: {
    context: {} as TopupContext,
    events: {} as TopupEvent,
    input: {} as {
      sessionId: string;
      phoneNumber: string;
      serviceCode: string;
      walletId?: string;
      currentBalance?: number;
    },
  },

  actions: {
    initializeContext: assign({
      transactionStatus: undefined,
      error: undefined,
      validationError: undefined,
    }),

    setTopupAmount: assign({
      topupAmount: ({ event }) => {
        if (event.type === "SELECT_AMOUNT") return event.amount;
        if (event.type === "CUSTOM_AMOUNT") return event.amount;
        return undefined;
      },
    }),

    setPaymentMethod: assign({
      paymentMethod: ({ event }) => {
        if (event.type === "SELECT_MOBILE_MONEY") return "mobile_money";
        if (event.type === "SELECT_BANK_TRANSFER") return "bank_transfer";
        if (event.type === "SELECT_CARD") return "card";
        if (event.type === "SELECT_AGENT") return "agent";
        return undefined;
      },
    }),

    setMobileMoneyNumber: assign({
      mobileMoneyNumber: ({ event }) =>
        event.type === "SUBMIT_MOBILE_NUMBER" ? event.number : undefined,
    }),

    startPaymentProcessing: assign({
      transactionStatus: "processing" as const,
      transactionId: () =>
        `TXN_${Date.now()}_${Math.random().toString(36).substr(2, 6)}`,
    }),

    handlePaymentSuccess: assign({
      transactionStatus: "completed" as const,
      transactionId: ({ event }) =>
        event.type === "PAYMENT_SUCCESS" ? event.transactionId : undefined,
      receiptNumber: ({ event }) =>
        event.type === "PAYMENT_SUCCESS" ? event.receiptNumber : undefined,
      currentBalance: ({ context }) =>
        (context.currentBalance || 0) + (context.topupAmount || 0),
    }),

    handlePaymentFailure: assign({
      transactionStatus: "failed" as const,
      error: ({ event }) =>
        event.type === "PAYMENT_FAILED" ? event.error : "Payment failed",
    }),

    resetForNewTopup: assign({
      topupAmount: undefined,
      paymentMethod: undefined,
      mobileMoneyNumber: undefined,
      transactionId: undefined,
      transactionStatus: undefined,
      receiptNumber: undefined,
      error: undefined,
      validationError: undefined,
    }),

    setError: assign({
      error: ({ event }) =>
        event.type === "ERROR" ? event.error : "An error occurred",
    }),

    setValidationError: assign({
      validationError: ({ context }) => {
        if (!context.topupAmount || context.topupAmount <= 0) {
          return "Please enter a valid amount";
        }
        if (context.topupAmount < 10) {
          return "Minimum top-up amount is K10";
        }
        if (context.topupAmount > 10000) {
          return "Maximum top-up amount is K10,000";
        }
        return undefined;
      },
    }),

    clearErrors: assign({
      error: undefined,
      validationError: undefined,
    }),
  },

  guards: {
    isValidAmount: ({ context }) =>
      Boolean(
        context.topupAmount &&
          context.topupAmount >= 10 &&
          context.topupAmount <= 10000
      ),

    isMobileMoneySelected: ({ context }) =>
      context.paymentMethod === "mobile_money",
    isBankTransferSelected: ({ context }) =>
      context.paymentMethod === "bank_transfer",
    isCardSelected: ({ context }) => context.paymentMethod === "card",
    isAgentSelected: ({ context }) => context.paymentMethod === "agent",

    isValidMobileNumber: ({ context }) =>
      Boolean(
        context.mobileMoneyNumber && context.mobileMoneyNumber.length >= 10
      ),

    hasTransactionId: ({ context }) => Boolean(context.transactionId),
    hasReceiptNumber: ({ context }) => Boolean(context.receiptNumber),

    isPaymentCompleted: ({ context }) =>
      context.transactionStatus === "completed",
    isPaymentFailed: ({ context }) => context.transactionStatus === "failed",

    hasError: ({ context }) => Boolean(context.error),
    hasValidationError: ({ context }) => Boolean(context.validationError),
  },
}).createMachine({
  id: "topupMachine",
  initial: "idle",

  context: ({ input }) => ({
    sessionId: input?.sessionId || "",
    phoneNumber: input?.phoneNumber || "",
    serviceCode: input?.serviceCode || "",
    walletId: input?.walletId,
    currentBalance: input?.currentBalance || 0,
  }),

  states: {
    // Initial state - waiting for start
    idle: {
      on: {
        START: {
          target: "amountSelection",
          actions: "initializeContext",
        },
      },
    },

    // Amount selection menu
    amountSelection: {
      entry: "clearErrors",
      on: {
        SELECT_AMOUNT: {
          target: "paymentMethodSelection",
          actions: "setTopupAmount",
          guard: "isValidAmount",
        },
        CUSTOM_AMOUNT: [
          {
            target: "paymentMethodSelection",
            actions: "setTopupAmount",
            guard: "isValidAmount",
          },
          {
            target: "amountSelection",
            actions: "setValidationError",
          },
        ],
        BACK_TO_MENU: "routeToUserServices",
        BACK_TO_MAIN: "routeToMain",
        ERROR: {
          target: "error",
          actions: "setError",
        },
      },
    },

    // Payment method selection
    paymentMethodSelection: {
      on: {
        SELECT_MOBILE_MONEY: {
          target: "mobileMoneyDetails",
          actions: "setPaymentMethod",
        },
        SELECT_BANK_TRANSFER: {
          target: "paymentConfirmation",
          actions: "setPaymentMethod",
        },
        SELECT_CARD: {
          target: "paymentConfirmation",
          actions: "setPaymentMethod",
        },
        SELECT_AGENT: {
          target: "paymentConfirmation",
          actions: "setPaymentMethod",
        },
        BACK_TO_MENU: "amountSelection",
        BACK_TO_MAIN: "routeToMain",
        ERROR: {
          target: "error",
          actions: "setError",
        },
      },
    },

    // Mobile money number entry
    mobileMoneyDetails: {
      on: {
        SUBMIT_MOBILE_NUMBER: [
          {
            target: "paymentConfirmation",
            actions: "setMobileMoneyNumber",
            guard: "isValidMobileNumber",
          },
          {
            target: "mobileMoneyDetails",
            actions: "setValidationError",
          },
        ],
        BACK_TO_MENU: "paymentMethodSelection",
        BACK_TO_MAIN: "routeToMain",
        ERROR: {
          target: "error",
          actions: "setError",
        },
      },
    },

    // Payment confirmation
    paymentConfirmation: {
      on: {
        CONFIRM_PAYMENT: {
          target: "processingPayment",
          actions: "startPaymentProcessing",
        },
        BACK_TO_MENU: "paymentMethodSelection",
        BACK_TO_MAIN: "routeToMain",
        ERROR: {
          target: "error",
          actions: "setError",
        },
      },
    },

    // Processing payment
    processingPayment: {
      on: {
        PAYMENT_SUCCESS: {
          target: "paymentSuccess",
          actions: "handlePaymentSuccess",
        },
        PAYMENT_FAILED: {
          target: "paymentFailed",
          actions: "handlePaymentFailure",
        },
        ERROR: {
          target: "error",
          actions: "setError",
        },
      },
    },

    // Payment success
    paymentSuccess: {
      on: {
        VIEW_RECEIPT: "receiptDisplay",
        NEW_TOPUP: {
          target: "amountSelection",
          actions: "resetForNewTopup",
        },
        BACK_TO_MENU: "routeToUserServices",
        BACK_TO_MAIN: "routeToMain",
      },
    },

    // Receipt display
    receiptDisplay: {
      on: {
        NEW_TOPUP: {
          target: "amountSelection",
          actions: "resetForNewTopup",
        },
        BACK_TO_MENU: "routeToUserServices",
        BACK_TO_MAIN: "routeToMain",
      },
    },

    // Payment failed
    paymentFailed: {
      on: {
        RETRY_PAYMENT: "paymentConfirmation",
        NEW_TOPUP: {
          target: "amountSelection",
          actions: "resetForNewTopup",
        },
        BACK_TO_MENU: "routeToUserServices",
        BACK_TO_MAIN: "routeToMain",
      },
    },

    // Error state
    error: {
      entry: "setError",
      on: {
        START: {
          target: "amountSelection",
          actions: "clearErrors",
        },
        BACK_TO_MENU: "routeToUserServices",
        BACK_TO_MAIN: "routeToMain",
      },
    },

    // Route back to user services
    routeToUserServices: {
      type: "final",
      output: {
        route: "userServices" as const,
        context: ({ context }: { context: TopupContext }) => context,
      },
    },

    // Route back to main menu
    routeToMain: {
      type: "final",
      output: {
        route: "main" as const,
        context: ({ context }: { context: TopupContext }) => context,
      },
    },
  },
});

export type TopupMachine = typeof topupMachine;
