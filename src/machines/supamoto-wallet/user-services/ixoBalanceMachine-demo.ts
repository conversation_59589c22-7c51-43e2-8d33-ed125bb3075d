/* eslint-disable no-console */
/**
 * IXO Balance Machine Demo
 *
 * Interactive demonstration of the IXO balance checking functionality.
 * This demo shows how users can check IXO account balances.
 */

import { createActor } from "xstate";
import { ixoBalanceMachine } from "./ixoBalanceMachine.js";

// Demo input data
const demoInput = {
  sessionId: "demo-session-ixo-balance",
  phoneNumber: "+**********",
  serviceCode: "*123#",
};

// Create and start the actor
const actor = createActor(ixoBalanceMachine, { input: demoInput });

// Subscribe to state changes
actor.subscribe(snapshot => {
  console.log("\n" + "=".repeat(50));
  console.log(`State: ${snapshot.value}`);
  console.log(`Status: ${snapshot.status}`);

  if (snapshot.context.message) {
    console.log(`Message: ${snapshot.context.message}`);
  }

  if (snapshot.context.ixoAddress) {
    console.log(`IXO Address: ${snapshot.context.ixoAddress}`);
  }

  if (snapshot.context.balanceDisplay) {
    console.log(`Balance: ${snapshot.context.balanceDisplay}`);
  }

  if (snapshot.context.error) {
    console.log(`Error: ${snapshot.context.error}`);
  }

  if (snapshot.context.validationError) {
    console.log(`Validation Error: ${snapshot.context.validationError}`);
  }

  console.log("=".repeat(50));
});

// Start the demo
console.log("🚀 Starting IXO Balance Machine Demo");
actor.start();

// Demo flow
async function runDemo() {
  console.log("\n📱 Step 1: Starting the machine");
  actor.send({ type: "START" });

  await new Promise(resolve => setTimeout(resolve, 1000));

  console.log("\n📱 Step 2: Testing invalid address");
  actor.send({ type: "INPUT_ADDRESS", input: "invalid" });

  await new Promise(resolve => setTimeout(resolve, 1000));

  console.log(
    "\n📱 Step 3: Testing valid address format (will likely fail with 'Account Not Found')"
  );
  actor.send({
    type: "INPUT_ADDRESS",
    input: "ixo**********abcdef**********abcdef12345678",
  });

  await new Promise(resolve => setTimeout(resolve, 3000));

  console.log("\n📱 Step 4: Navigating back to services");
  actor.send({ type: "BACK_TO_SERVICES" });

  await new Promise(resolve => setTimeout(resolve, 1000));

  console.log("\n✅ Demo completed!");
}

// Run the demo
runDemo().catch(console.error);
