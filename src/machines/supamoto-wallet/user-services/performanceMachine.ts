import { setup, assign } from "xstate";

/**
 * Performance Machine - Performance Monitoring and Metrics Display
 *
 * Handles:
 * - Performance monitoring workflows
 * - Metrics display and visualization
 * - Performance reporting and analytics
 * - Environmental impact tracking
 * - Usage statistics and trends
 *
 * Entry Points: START
 * Exit Points: Outputs routing decision for main orchestrator
 */

export interface PerformanceContext {
  sessionId: string;
  phoneNumber: string;
  serviceCode: string;
  walletId?: string;
  selectedMetric?:
    | "energy"
    | "environmental"
    | "usage"
    | "financial"
    | "maintenance";
  selectedPeriod?: "daily" | "weekly" | "monthly" | "yearly";
  performanceData?: {
    energyGenerated?: number; // kWh
    energyConsumed?: number; // kWh
    co2Saved?: number; // kg
    treesEquivalent?: number;
    costSavings?: number; // currency
    systemEfficiency?: number; // percentage
    uptime?: number; // percentage
    maintenanceAlerts?: number;
    usageHours?: number;
    peakPerformance?: number;
  };
  comparisonData?: {
    previousPeriod?: any;
    averageUser?: any;
    targetGoals?: any;
  };
  reportType?: "summary" | "detailed" | "comparison" | "trends";
  currentPage?: number;
  totalPages?: number;
  error?: string;
  validationError?: string;
}

export type PerformanceEvent =
  | { type: "START" }
  | { type: "VIEW_ENERGY_METRICS" }
  | { type: "VIEW_ENVIRONMENTAL_IMPACT" }
  | { type: "VIEW_USAGE_STATISTICS" }
  | { type: "VIEW_FINANCIAL_METRICS" }
  | { type: "VIEW_MAINTENANCE_STATUS" }
  | { type: "SELECT_DAILY_PERIOD" }
  | { type: "SELECT_WEEKLY_PERIOD" }
  | { type: "SELECT_MONTHLY_PERIOD" }
  | { type: "SELECT_YEARLY_PERIOD" }
  | { type: "GENERATE_SUMMARY_REPORT" }
  | { type: "GENERATE_DETAILED_REPORT" }
  | { type: "GENERATE_COMPARISON_REPORT" }
  | { type: "GENERATE_TRENDS_REPORT" }
  | { type: "REFRESH_DATA" }
  | { type: "EXPORT_REPORT" }
  | { type: "NEXT_PAGE" }
  | { type: "PREVIOUS_PAGE" }
  | { type: "BACK_TO_METRICS" }
  | { type: "BACK_TO_MENU" }
  | { type: "BACK_TO_MAIN" }
  | { type: "ERROR"; error: string };

// Mock performance data
const MOCK_PERFORMANCE_DATA = {
  daily: {
    energyGenerated: 12.5,
    energyConsumed: 8.2,
    co2Saved: 6.8,
    treesEquivalent: 0.3,
    costSavings: 15.5,
    systemEfficiency: 87,
    uptime: 95,
    maintenanceAlerts: 0,
    usageHours: 14,
    peakPerformance: 92,
  },
  weekly: {
    energyGenerated: 85.2,
    energyConsumed: 62.1,
    co2Saved: 42.3,
    treesEquivalent: 1.8,
    costSavings: 98.75,
    systemEfficiency: 89,
    uptime: 96,
    maintenanceAlerts: 1,
    usageHours: 98,
    peakPerformance: 94,
  },
  monthly: {
    energyGenerated: 365.8,
    energyConsumed: 278.4,
    co2Saved: 187.2,
    treesEquivalent: 8.1,
    costSavings: 425.3,
    systemEfficiency: 91,
    uptime: 97,
    maintenanceAlerts: 2,
    usageHours: 420,
    peakPerformance: 96,
  },
  yearly: {
    energyGenerated: 4389.6,
    energyConsumed: 3341.2,
    co2Saved: 2246.4,
    treesEquivalent: 97.2,
    costSavings: 5103.6,
    systemEfficiency: 93,
    uptime: 98,
    maintenanceAlerts: 8,
    usageHours: 5040,
    peakPerformance: 98,
  },
};

export const performanceMachine = setup({
  types: {
    context: {} as PerformanceContext,
    events: {} as PerformanceEvent,
    input: {} as {
      sessionId: string;
      phoneNumber: string;
      serviceCode: string;
      walletId?: string;
    },
  },

  actions: {
    initializeContext: assign({
      selectedPeriod: "monthly" as const,
      currentPage: 1,
      totalPages: 1,
      error: undefined,
      validationError: undefined,
    }),

    setSelectedMetric: assign({
      selectedMetric: ({ event }) => {
        if (event.type === "VIEW_ENERGY_METRICS") return "energy";
        if (event.type === "VIEW_ENVIRONMENTAL_IMPACT") return "environmental";
        if (event.type === "VIEW_USAGE_STATISTICS") return "usage";
        if (event.type === "VIEW_FINANCIAL_METRICS") return "financial";
        if (event.type === "VIEW_MAINTENANCE_STATUS") return "maintenance";
        return undefined;
      },
    }),

    setSelectedPeriod: assign({
      selectedPeriod: ({ event }) => {
        if (event.type === "SELECT_DAILY_PERIOD") return "daily";
        if (event.type === "SELECT_WEEKLY_PERIOD") return "weekly";
        if (event.type === "SELECT_MONTHLY_PERIOD") return "monthly";
        if (event.type === "SELECT_YEARLY_PERIOD") return "yearly";
        return undefined;
      },
    }),

    loadPerformanceData: assign({
      performanceData: ({ context }) => {
        const period = context.selectedPeriod || "monthly";
        return MOCK_PERFORMANCE_DATA[period];
      },
    }),

    setReportType: assign({
      reportType: ({ event }) => {
        if (event.type === "GENERATE_SUMMARY_REPORT") return "summary";
        if (event.type === "GENERATE_DETAILED_REPORT") return "detailed";
        if (event.type === "GENERATE_COMPARISON_REPORT") return "comparison";
        if (event.type === "GENERATE_TRENDS_REPORT") return "trends";
        return undefined;
      },
    }),

    generateComparisonData: assign({
      comparisonData: ({ context }) => {
        const current = context.performanceData;
        if (!current) return undefined;

        return {
          previousPeriod: {
            energyGenerated: (current.energyGenerated || 0) * 0.85,
            co2Saved: (current.co2Saved || 0) * 0.88,
            costSavings: (current.costSavings || 0) * 0.82,
            systemEfficiency: (current.systemEfficiency || 0) - 3,
          },
          averageUser: {
            energyGenerated: (current.energyGenerated || 0) * 0.75,
            co2Saved: (current.co2Saved || 0) * 0.78,
            costSavings: (current.costSavings || 0) * 0.72,
            systemEfficiency: (current.systemEfficiency || 0) - 8,
          },
          targetGoals: {
            energyGenerated: (current.energyGenerated || 0) * 1.2,
            co2Saved: (current.co2Saved || 0) * 1.25,
            costSavings: (current.costSavings || 0) * 1.15,
            systemEfficiency: Math.min(
              (current.systemEfficiency || 0) + 5,
              100
            ),
          },
        };
      },
    }),

    refreshPerformanceData: assign({
      performanceData: ({ context }) => {
        const period = context.selectedPeriod || "monthly";
        const baseData = MOCK_PERFORMANCE_DATA[period];

        // Simulate slight variations in refreshed data
        return {
          ...baseData,
          energyGenerated:
            baseData.energyGenerated * (0.95 + Math.random() * 0.1),
          systemEfficiency: Math.min(
            baseData.systemEfficiency + Math.floor(Math.random() * 3),
            100
          ),
          uptime: Math.min(
            baseData.uptime + Math.floor(Math.random() * 2),
            100
          ),
        };
      },
    }),

    nextPage: assign({
      currentPage: ({ context }) =>
        Math.min((context.currentPage || 1) + 1, context.totalPages || 1),
    }),

    previousPage: assign({
      currentPage: ({ context }) => Math.max((context.currentPage || 1) - 1, 1),
    }),

    setTotalPages: assign({
      totalPages: ({ context }) => {
        if (context.reportType === "detailed") return 3;
        if (context.reportType === "trends") return 4;
        return 1;
      },
    }),

    resetContext: assign({
      selectedMetric: undefined,
      performanceData: undefined,
      comparisonData: undefined,
      reportType: undefined,
      currentPage: 1,
      totalPages: 1,
      error: undefined,
      validationError: undefined,
    }),

    setError: assign({
      error: ({ event }) =>
        event.type === "ERROR" ? event.error : "An error occurred",
    }),

    clearErrors: assign({
      error: undefined,
      validationError: undefined,
    }),
  },

  guards: {
    isEnergyMetric: ({ context }) => context.selectedMetric === "energy",
    isEnvironmentalMetric: ({ context }) =>
      context.selectedMetric === "environmental",
    isUsageMetric: ({ context }) => context.selectedMetric === "usage",
    isFinancialMetric: ({ context }) => context.selectedMetric === "financial",
    isMaintenanceMetric: ({ context }) =>
      context.selectedMetric === "maintenance",

    isDailyPeriod: ({ context }) => context.selectedPeriod === "daily",
    isWeeklyPeriod: ({ context }) => context.selectedPeriod === "weekly",
    isMonthlyPeriod: ({ context }) => context.selectedPeriod === "monthly",
    isYearlyPeriod: ({ context }) => context.selectedPeriod === "yearly",

    hasPerformanceData: ({ context }) => Boolean(context.performanceData),
    hasComparisonData: ({ context }) => Boolean(context.comparisonData),

    isComparisonReport: ({ context }) => context.reportType === "comparison",
    isTrendsReport: ({ context }) => context.reportType === "trends",
    isDetailedReport: ({ context }) => context.reportType === "detailed",

    hasNextPage: ({ context }) =>
      (context.currentPage || 1) < (context.totalPages || 1),

    hasPreviousPage: ({ context }) => (context.currentPage || 1) > 1,

    hasError: ({ context }) => Boolean(context.error),
  },
}).createMachine({
  id: "performanceMachine",
  initial: "idle",

  context: ({ input }) => ({
    sessionId: input?.sessionId || "",
    phoneNumber: input?.phoneNumber || "",
    serviceCode: input?.serviceCode || "",
    walletId: input?.walletId,
  }),

  states: {
    // Initial state - waiting for start
    idle: {
      on: {
        START: {
          target: "metricSelection",
          actions: "initializeContext",
        },
      },
    },

    // Metric selection menu
    metricSelection: {
      entry: "clearErrors",
      on: {
        VIEW_ENERGY_METRICS: {
          target: "periodSelection",
          actions: "setSelectedMetric",
        },
        VIEW_ENVIRONMENTAL_IMPACT: {
          target: "periodSelection",
          actions: "setSelectedMetric",
        },
        VIEW_USAGE_STATISTICS: {
          target: "periodSelection",
          actions: "setSelectedMetric",
        },
        VIEW_FINANCIAL_METRICS: {
          target: "periodSelection",
          actions: "setSelectedMetric",
        },
        VIEW_MAINTENANCE_STATUS: {
          target: "loadingData",
          actions: ["setSelectedMetric", "loadPerformanceData"],
        },
        BACK_TO_MENU: "routeToUserServices",
        BACK_TO_MAIN: "routeToMain",
        ERROR: {
          target: "error",
          actions: "setError",
        },
      },
    },

    // Period selection for time-based metrics
    periodSelection: {
      on: {
        SELECT_DAILY_PERIOD: {
          target: "loadingData",
          actions: ["setSelectedPeriod", "loadPerformanceData"],
        },
        SELECT_WEEKLY_PERIOD: {
          target: "loadingData",
          actions: ["setSelectedPeriod", "loadPerformanceData"],
        },
        SELECT_MONTHLY_PERIOD: {
          target: "loadingData",
          actions: ["setSelectedPeriod", "loadPerformanceData"],
        },
        SELECT_YEARLY_PERIOD: {
          target: "loadingData",
          actions: ["setSelectedPeriod", "loadPerformanceData"],
        },
        BACK_TO_METRICS: "metricSelection",
        BACK_TO_MAIN: "routeToMain",
        ERROR: {
          target: "error",
          actions: "setError",
        },
      },
    },

    // Loading performance data
    loadingData: {
      always: "displayingMetrics",
    },

    // Displaying performance metrics
    displayingMetrics: {
      on: {
        GENERATE_SUMMARY_REPORT: {
          target: "generatingReport",
          actions: "setReportType",
        },
        GENERATE_DETAILED_REPORT: {
          target: "generatingReport",
          actions: ["setReportType", "setTotalPages"],
        },
        GENERATE_COMPARISON_REPORT: {
          target: "generatingReport",
          actions: ["setReportType", "generateComparisonData"],
        },
        GENERATE_TRENDS_REPORT: {
          target: "generatingReport",
          actions: ["setReportType", "setTotalPages"],
        },
        REFRESH_DATA: {
          actions: "refreshPerformanceData",
        },
        BACK_TO_METRICS: "metricSelection",
        BACK_TO_MAIN: "routeToMain",
        ERROR: {
          target: "error",
          actions: "setError",
        },
      },
    },

    // Generating performance report
    generatingReport: {
      after: {
        500: "displayingReport", // Simulate report generation time
      },
      on: {
        ERROR: {
          target: "error",
          actions: "setError",
        },
      },
    },

    // Displaying generated report
    displayingReport: {
      on: {
        NEXT_PAGE: {
          guard: "hasNextPage",
          actions: "nextPage",
        },
        PREVIOUS_PAGE: {
          guard: "hasPreviousPage",
          actions: "previousPage",
        },
        EXPORT_REPORT: "exportingReport",
        REFRESH_DATA: {
          target: "loadingData",
          actions: "refreshPerformanceData",
        },
        BACK_TO_METRICS: "displayingMetrics",
        BACK_TO_MAIN: "routeToMain",
        ERROR: {
          target: "error",
          actions: "setError",
        },
      },
    },

    // Exporting report
    exportingReport: {
      after: {
        1000: "exportSuccess", // Simulate export time
      },
      on: {
        ERROR: {
          target: "error",
          actions: "setError",
        },
      },
    },

    // Export successful
    exportSuccess: {
      on: {
        BACK_TO_METRICS: "displayingReport",
        BACK_TO_MAIN: "routeToMain",
      },
    },

    // Error state
    error: {
      entry: "setError",
      on: {
        START: {
          target: "metricSelection",
          actions: "clearErrors",
        },
        BACK_TO_MENU: "routeToUserServices",
        BACK_TO_MAIN: "routeToMain",
      },
    },

    // Route back to user services
    routeToUserServices: {
      type: "final",
      output: {
        route: "userServices" as const,
        context: ({ context }: { context: PerformanceContext }) => context,
      },
    },

    // Route back to main menu
    routeToMain: {
      type: "final",
      output: {
        route: "main" as const,
        context: ({ context }: { context: PerformanceContext }) => context,
      },
    },
  },
});

export type PerformanceMachine = typeof performanceMachine;
