/* eslint-disable no-console */
import { createActor } from "xstate";
import { purchaseMachine } from "./purchaseMachine.js";

/**
 * Purchase Machine Demo
 *
 * Demonstrates the purchase machine functionality including:
 * - Product category and selection
 * - Inventory checking
 * - Quantity and address validation
 * - Payment method selection
 * - Order processing and confirmation
 * - Error handling and navigation
 */

console.log("🚀 Purchase Machine Demo\n");

const mockInput = {
  sessionId: "demo-session-purchase-456",
  phoneNumber: "+260987654321",
  serviceCode: "*2233#",
  walletId: "*********",
  currentBalance: 500,
};

// Demo 1: Successful Solar System Purchase
console.log("=".repeat(50));
console.log("DEMO 1: Successful Solar System Purchase");
console.log("=".repeat(50));

const actor1 = createActor(purchaseMachine, { input: mockInput });
actor1.subscribe(snapshot => {
  console.log(`📍 State: ${snapshot.value}`);
  if (snapshot.context.selectedCategory) {
    console.log(`📂 Category: ${snapshot.context.selectedCategory}`);
  }
  if (snapshot.context.selectedProduct) {
    console.log(
      `📦 Product: ${snapshot.context.selectedProduct.name} - K${snapshot.context.selectedProduct.price}`
    );
  }
  if (snapshot.context.quantity && snapshot.context.totalAmount) {
    console.log(
      `🔢 Quantity: ${snapshot.context.quantity} | Total: K${snapshot.context.totalAmount}`
    );
  }
  if (snapshot.context.deliveryAddress) {
    console.log(`🏠 Address: ${snapshot.context.deliveryAddress}`);
  }
  if (snapshot.context.paymentMethod) {
    console.log(`💳 Payment: ${snapshot.context.paymentMethod}`);
  }
  if (snapshot.context.orderId) {
    console.log(`🆔 Order ID: ${snapshot.context.orderId}`);
  }
  if (snapshot.context.orderStatus) {
    console.log(`📋 Status: ${snapshot.context.orderStatus}`);
  }
  if (snapshot.context.currentBalance !== undefined) {
    console.log(`💰 Balance: K${snapshot.context.currentBalance}`);
  }
  if (snapshot.output) {
    console.log(`🎯 Output:`, snapshot.output);
  }
  console.log("");
});

actor1.start();
actor1.send({ type: "START" });
console.log("📱 User started purchase flow");

actor1.send({ type: "SELECT_SOLAR" });
console.log("📱 User selected solar category");

actor1.send({ type: "SELECT_PRODUCT", productId: "SOL001" });
console.log("📱 User selected 20W Solar Home System");

actor1.send({ type: "SET_QUANTITY", quantity: 2 });
console.log("📱 User set quantity to 2");

actor1.send({
  type: "SUBMIT_ADDRESS",
  address: "123 Solar Street, Lusaka, Zambia",
});
console.log("📱 User submitted delivery address");

actor1.send({ type: "SELECT_WALLET_PAYMENT" });
console.log("📱 User selected wallet payment");

actor1.send({ type: "CONFIRM_ORDER" });
console.log("📱 User confirmed order");

// Simulate successful payment
setTimeout(() => {
  actor1.send({
    type: "PAYMENT_SUCCESS",
    transactionId: "TXN_SOLAR_789",
    orderId: "ORD_SOLAR_123",
  });
  console.log("✅ Solar system purchase completed!\n");

  // Demo 2: Out of Stock Product
  console.log("=".repeat(50));
  console.log("DEMO 2: Out of Stock Product");
  console.log("=".repeat(50));

  const actor2 = createActor(purchaseMachine, { input: mockInput });
  actor2.subscribe(snapshot => {
    console.log(`📍 State: ${snapshot.value}`);
    if (snapshot.context.selectedProduct) {
      console.log(`📦 Product: ${snapshot.context.selectedProduct.name}`);
      console.log(`📊 In Stock: ${snapshot.context.selectedProduct.inStock}`);
    }
    console.log("");
  });

  actor2.start();
  actor2.send({ type: "START" });
  actor2.send({ type: "SELECT_SOLAR" });
  actor2.send({ type: "SELECT_PRODUCT", productId: "SOL003" }); // Out of stock
  console.log("📱 User selected out-of-stock 100W Solar System");

  actor2.send({ type: "BACK_TO_PRODUCTS" });
  console.log("📱 User navigated back to product selection");

  console.log("✅ Out of stock handling complete!\n");

  // Demo 3: Validation Errors
  console.log("=".repeat(50));
  console.log("DEMO 3: Validation Errors");
  console.log("=".repeat(50));

  const actor3 = createActor(purchaseMachine, { input: mockInput });
  actor3.subscribe(snapshot => {
    console.log(`📍 State: ${snapshot.value}`);
    if (snapshot.context.validationError) {
      console.log(`⚠️ Validation Error: ${snapshot.context.validationError}`);
    }
    console.log("");
  });

  actor3.start();
  actor3.send({ type: "START" });
  actor3.send({ type: "SELECT_COOKSTOVES" });
  actor3.send({ type: "SELECT_PRODUCT", productId: "COOK001" });

  // Test invalid quantity
  actor3.send({ type: "SET_QUANTITY", quantity: 0 });
  console.log("📱 User entered invalid quantity (0)");

  actor3.send({ type: "SET_QUANTITY", quantity: 15 });
  console.log("📱 User entered quantity above maximum (15)");

  actor3.send({ type: "SET_QUANTITY", quantity: 1 });
  console.log("📱 User entered valid quantity (1)");

  // Test invalid address
  actor3.send({ type: "SUBMIT_ADDRESS", address: "Short" });
  console.log("📱 User entered too short address");

  actor3.send({
    type: "SUBMIT_ADDRESS",
    address: "789 Cookstove Avenue, Kitwe, Zambia",
  });
  console.log("📱 User entered valid address");

  console.log("✅ Validation testing complete!\n");

  // Demo 4: Insufficient Balance
  console.log("=".repeat(50));
  console.log("DEMO 4: Insufficient Balance");
  console.log("=".repeat(50));

  const lowBalanceInput = { ...mockInput, currentBalance: 50 };
  const actor4 = createActor(purchaseMachine, { input: lowBalanceInput });
  actor4.subscribe(snapshot => {
    console.log(`📍 State: ${snapshot.value}`);
    if (snapshot.context.validationError) {
      console.log(`⚠️ Validation Error: ${snapshot.context.validationError}`);
    }
    if (snapshot.context.totalAmount) {
      console.log(`💰 Total Amount: K${snapshot.context.totalAmount}`);
      console.log(`💵 Wallet Balance: K${snapshot.context.currentBalance}`);
    }
    console.log("");
  });

  actor4.start();
  actor4.send({ type: "START" });
  actor4.send({ type: "SELECT_SOLAR" });
  actor4.send({ type: "SELECT_PRODUCT", productId: "SOL001" }); // K150
  actor4.send({ type: "SET_QUANTITY", quantity: 1 });
  actor4.send({
    type: "SUBMIT_ADDRESS",
    address: "456 Balance Street, Ndola, Zambia",
  });

  actor4.send({ type: "SELECT_WALLET_PAYMENT" });
  console.log("📱 User tried wallet payment with insufficient balance");

  actor4.send({ type: "SELECT_MOBILE_MONEY" });
  console.log("📱 User switched to mobile money payment");

  console.log("✅ Insufficient balance handling complete!\n");

  // Demo 5: Failed Payment with Mobile Money
  console.log("=".repeat(50));
  console.log("DEMO 5: Failed Payment Recovery");
  console.log("=".repeat(50));

  const actor5 = createActor(purchaseMachine, { input: mockInput });
  actor5.subscribe(snapshot => {
    console.log(`📍 State: ${snapshot.value}`);
    if (snapshot.context.error) {
      console.log(`❌ Error: ${snapshot.context.error}`);
    }
    if (snapshot.context.orderStatus) {
      console.log(`📋 Order Status: ${snapshot.context.orderStatus}`);
    }
    console.log("");
  });

  actor5.start();
  actor5.send({ type: "START" });
  actor5.send({ type: "SELECT_WATER" });
  actor5.send({ type: "SELECT_PRODUCT", productId: "WAT001" });
  actor5.send({ type: "SET_QUANTITY", quantity: 1 });
  actor5.send({
    type: "SUBMIT_ADDRESS",
    address: "321 Water Lane, Livingstone, Zambia",
  });
  actor5.send({ type: "SELECT_MOBILE_MONEY" });
  actor5.send({ type: "CONFIRM_ORDER" });
  console.log("📱 User confirmed water purification system order");

  // Simulate failed payment
  setTimeout(() => {
    actor5.send({
      type: "PAYMENT_FAILED",
      error: "Mobile money service unavailable",
    });
    console.log("❌ Payment failed");

    actor5.send({ type: "NEW_PURCHASE" });
    console.log("📱 User started new purchase after failure");

    console.log("✅ Payment failure recovery complete!\n");

    // Demo 6: Navigation Flow
    console.log("=".repeat(50));
    console.log("DEMO 6: Navigation Flow");
    console.log("=".repeat(50));

    const actor6 = createActor(purchaseMachine, { input: mockInput });
    actor6.subscribe(snapshot => {
      console.log(`📍 State: ${snapshot.value}`);
      if (snapshot.output) {
        console.log(`🎯 Output:`, snapshot.output);
      }
      console.log("");
    });

    actor6.start();
    actor6.send({ type: "START" });
    console.log("📱 User started purchase flow");

    actor6.send({ type: "SELECT_LIGHTING" });
    console.log("📱 User selected lighting category");

    actor6.send({ type: "BACK_TO_CATEGORIES" });
    console.log("📱 User navigated back to categories");

    actor6.send({ type: "BACK_TO_MAIN" });
    console.log("📱 User navigated back to main menu");

    console.log("✅ Navigation flow complete!\n");

    console.log("\n🎉 Purchase Machine Demo Complete!");
    console.log("\n📊 Machine Summary:");
    console.log("   • Handles product catalog browsing by category");
    console.log("   • Manages product selection with inventory checking");
    console.log("   • Validates quantities and delivery addresses");
    console.log(
      "   • Supports multiple payment methods (wallet, mobile money, agent)"
    );
    console.log("   • Processes orders with confirmation flows");
    console.log("   • Handles payment success and failure scenarios");
    console.log("   • Updates wallet balance for successful wallet payments");
    console.log("   • Provides comprehensive error handling and validation");
    console.log("   • Supports navigation back through the purchase flow");
    console.log("   • Type-safe with XState v5 setup() pattern");
  }, 1000);
}, 1000);
