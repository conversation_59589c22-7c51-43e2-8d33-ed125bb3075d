import { describe, it, expect } from "vitest";
import { createActor } from "xstate";
import { ixoBalanceMachine } from "./ixoBalanceMachine.js";

describe("ixoBalanceMachine - Smoke Tests", () => {
  const mockInput = {
    sessionId: "test-session-123",
    phoneNumber: "+1234567890",
    serviceCode: "*123#",
  };

  describe("Basic Functionality", () => {
    it("should create and start successfully", () => {
      const actor = createActor(ixoBalanceMachine, { input: mockInput });
      actor.start();

      expect(actor.getSnapshot().status).toBe("active");
      expect(actor.getSnapshot().value).toBe("idle");
      expect(actor.getSnapshot().context.sessionId).toBe(mockInput.sessionId);
      expect(actor.getSnapshot().context.phoneNumber).toBe(
        mockInput.phoneNumber
      );
      expect(actor.getSnapshot().context.serviceCode).toBe(
        mockInput.serviceCode
      );
    });

    it("should handle START event", () => {
      const actor = createActor(ixoBalanceMachine, { input: mockInput });
      actor.start();

      actor.send({ type: "START" });

      const snapshot = actor.getSnapshot();
      expect(snapshot.value).toBe("addressInput");
      expect(snapshot.context.message).toContain("Enter IXO account address");
    });

    it("should handle address input", () => {
      const actor = createActor(ixoBalanceMachine, { input: mockInput });
      actor.start();

      actor.send({ type: "START" });
      actor.send({ type: "INPUT_ADDRESS", input: "ixo1test123456789" });

      const snapshot = actor.getSnapshot();
      expect(snapshot.context.ixoAddress).toBe("ixo1test123456789");
    });

    it("should handle navigation events", () => {
      const actor = createActor(ixoBalanceMachine, { input: mockInput });
      actor.start();

      actor.send({ type: "START" });

      // Check that we're in addressInput state
      let snapshot = actor.getSnapshot();
      expect(snapshot.value).toBe("addressInput");

      actor.send({ type: "BACK_TO_SERVICES" });

      // Check final state
      snapshot = actor.getSnapshot();
      expect(snapshot.status).toBe("done");
      expect(snapshot.value).toBe("routeToUserServices");
    });
  });
});
