import { setup, assign, fromPromise } from "xstate";
import { topupMachine } from "./topupMachine.js";
import { purchaseMachine } from "./purchaseMachine.js";
import { orderMachine } from "./orderMachine.js";
import { faultMachine } from "./faultMachine.js";
import { performanceMachine } from "./performanceMachine.js";
import { voucherMachine } from "./voucherMachine.js";
import { accountMachine } from "./accountMachine.js";
import { ixoBalanceMachine } from "./ixoBalanceMachine.js";
import { withNavigation } from "../utils/navigation-mixin.js";
import { navigationGuards } from "../guards/navigation.guards.js";
import { NavigationPatterns } from "../utils/navigation-patterns.js";

/**
 * User Services Machine - Orchestrator for User Service Workflows
 *
 * Coordinates and routes between different user service machines:
 * - Top-up services (airtime, wallet, mobile money)
 * - Purchase services (product catalog and ordering)
 * - Order management (tracking, modification, cancellation)
 * - Fault reporting (issue categorization and escalation)
 * - Performance monitoring (metrics and analytics)
 * - Voucher management (redemption, purchase, transfer)
 * - Account management (profile, security, verification)
 * - IXO balance checking (query any IXO account balance)
 *
 * Entry Points: START
 * Exit Points: Outputs routing decision for main orchestrator
 */

export interface UserServicesContext {
  sessionId: string;
  phoneNumber: string;
  serviceCode: string;
  walletId?: string;
  customerName?: string;
  currentBalance?: number;
  selectedService?:
    | "topup"
    | "purchase"
    | "orders"
    | "faults"
    | "performance"
    | "vouchers"
    | "account"
    | "ixoBalance";
  childMachineOutput?: any;
  serviceHistory?: Array<{
    service: string;
    timestamp: string;
    duration?: number;
  }>;
  message?: string;
  error?: string;
  validationError?: string;
}

export type UserServicesEvent =
  | { type: "START" }
  | { type: "INPUT"; input: string }
  | { type: "SELECT_TOPUP_SERVICES" }
  | { type: "SELECT_PURCHASE_SERVICES" }
  | { type: "SELECT_ORDER_MANAGEMENT" }
  | { type: "SELECT_FAULT_REPORTING" }
  | { type: "SELECT_PERFORMANCE_MONITORING" }
  | { type: "SELECT_VOUCHER_MANAGEMENT" }
  | { type: "SELECT_ACCOUNT_MANAGEMENT" }
  | { type: "SELECT_IXO_BALANCE_CHECK" }
  | { type: "CHILD_MACHINE_DONE"; output: any }
  | { type: "BACK_TO_SERVICES" }
  | { type: "BACK_TO_MAIN" }
  | { type: "ERROR"; error: string };

export const userServicesMachine = setup({
  types: {
    context: {} as UserServicesContext,
    events: {} as UserServicesEvent,
    input: {} as {
      sessionId: string;
      phoneNumber: string;
      serviceCode: string;
      walletId?: string;
      customerName?: string;
      currentBalance?: number;
    },
  },

  actors: {
    topupMachine,
    purchaseMachine,
    orderMachine,
    faultMachine,
    performanceMachine,
    voucherMachine,
    accountMachine,
    ixoBalanceMachine,

    // Mock balance fetcher for demonstration
    fetchBalance: fromPromise(
      async ({ input }: { input: { walletId?: string } }) => {
        // Simulate API call to fetch current balance
        await new Promise(resolve => setTimeout(resolve, 500));
        return {
          balance: 250.75 + Math.random() * 100, // Mock balance
          currency: "ZMW",
          lastUpdated: new Date().toISOString(),
          walletId: input.walletId, // Use input parameter
        };
      }
    ),
  },

  actions: {
    initializeContext: assign({
      serviceHistory: [],
      error: undefined,
      validationError: undefined,
    }),

    setSelectedService: assign({
      selectedService: ({ event }) => {
        if (event.type === "SELECT_TOPUP_SERVICES") return "topup";
        if (event.type === "SELECT_PURCHASE_SERVICES") return "purchase";
        if (event.type === "SELECT_ORDER_MANAGEMENT") return "orders";
        if (event.type === "SELECT_FAULT_REPORTING") return "faults";
        if (event.type === "SELECT_PERFORMANCE_MONITORING")
          return "performance";
        if (event.type === "SELECT_VOUCHER_MANAGEMENT") return "vouchers";
        if (event.type === "SELECT_ACCOUNT_MANAGEMENT") return "account";
        if (event.type === "SELECT_IXO_BALANCE_CHECK") return "ixoBalance";
        return undefined;
      },
    }),

    recordServiceEntry: assign({
      serviceHistory: ({ context }) => [
        ...(context.serviceHistory || []),
        {
          service: context.selectedService || "unknown",
          timestamp: new Date().toISOString(),
        },
      ],
    }),

    updateServiceHistory: assign({
      serviceHistory: ({ context }) => {
        const history = context.serviceHistory || [];
        if (history.length > 0) {
          const lastEntry = history[history.length - 1];
          const updatedEntry = {
            ...lastEntry,
            duration: Date.now() - new Date(lastEntry.timestamp).getTime(),
          };
          return [...history.slice(0, -1), updatedEntry];
        }
        return history;
      },
    }),

    setChildMachineOutput: assign({
      childMachineOutput: ({ event }) =>
        event.type === "CHILD_MACHINE_DONE" ? event.output : undefined,
    }),

    updateBalanceFromChild: assign({
      currentBalance: ({ context, event }) => {
        if (
          event.type === "CHILD_MACHINE_DONE" &&
          event.output?.context?.currentBalance !== undefined
        ) {
          return event.output.context.currentBalance;
        }
        return context.currentBalance;
      },
    }),

    setError: assign({
      error: ({ event }) =>
        event.type === "ERROR" ? event.error : "An error occurred",
    }),

    clearErrors: assign({
      error: undefined,
      validationError: undefined,
    }),

    resetChildData: assign({
      selectedService: undefined,
      childMachineOutput: undefined,
    }),
  },

  guards: {
    isTopupService: ({ context }) => context.selectedService === "topup",
    isPurchaseService: ({ context }) => context.selectedService === "purchase",
    isOrderService: ({ context }) => context.selectedService === "orders",
    isFaultService: ({ context }) => context.selectedService === "faults",
    isPerformanceService: ({ context }) =>
      context.selectedService === "performance",
    isVoucherService: ({ context }) => context.selectedService === "vouchers",
    isAccountService: ({ context }) => context.selectedService === "account",
    isIxoBalanceService: ({ context }) =>
      context.selectedService === "ixoBalance",

    shouldRouteToMain: ({ event }) =>
      event.type === "CHILD_MACHINE_DONE" && event.output?.route === "main",

    shouldRouteToUserServices: ({ event }) =>
      event.type === "CHILD_MACHINE_DONE" &&
      event.output?.route === "userServices",

    hasError: ({ context }) => Boolean(context.error),

    // Input guards for menu selection
    isInput1: ({ event }) => event.type === "INPUT" && event.input === "1",
    isInput2: ({ event }) => event.type === "INPUT" && event.input === "2",
    isInput3: ({ event }) => event.type === "INPUT" && event.input === "3",
    isInput4: ({ event }) => event.type === "INPUT" && event.input === "4",
    isInput5: ({ event }) => event.type === "INPUT" && event.input === "5",
    isInput6: ({ event }) => event.type === "INPUT" && event.input === "6",
    isInput7: ({ event }) => event.type === "INPUT" && event.input === "7",
    isInput8: ({ event }) => event.type === "INPUT" && event.input === "8",

    // Navigation guards
    isBack: ({ event }) =>
      navigationGuards.isBackCommand(null as any, event as any),
    isExit: ({ event }) =>
      navigationGuards.isExitCommand(null as any, event as any),
  },
}).createMachine({
  id: "userServicesMachine",
  initial: "idle",

  context: ({ input }) => ({
    sessionId: input?.sessionId || "",
    phoneNumber: input?.phoneNumber || "",
    serviceCode: input?.serviceCode || "",
    walletId: input?.walletId,
    customerName: input?.customerName,
    currentBalance: input?.currentBalance,
    message:
      input?.customerName && input?.currentBalance
        ? `Welcome ${input.customerName}!\nBalance: ZMW ${input.currentBalance.toFixed(2)}\n1. Top up & Balance\n2. Purchase\n3. Orders\n4. Report Fault\n5. Performance\n6. Vouchers\n7. Account\n8. Check IXO Account Balance`
        : "Loading user services...",
  }),

  states: {
    // Initial state - waiting for start
    idle: {
      on: {
        START: {
          target: "loadingBalance",
          actions: "initializeContext",
        },
      },
    },

    // Loading current balance
    loadingBalance: {
      invoke: {
        src: "fetchBalance",
        input: ({ context }) => ({ walletId: context.walletId }),
        onDone: {
          target: "serviceSelection",
          actions: assign({
            currentBalance: ({ event }) => event.output.balance,
          }),
        },
        onError: {
          target: "serviceSelection", // Continue even if balance fetch fails
          actions: assign({
            currentBalance: ({ context }) => context.currentBalance || 0,
          }),
        },
      },
    },

    // User service selection menu
    serviceSelection: {
      entry: "clearErrors",
      on: {
        INPUT: withNavigation(
          [
            {
              target: "topupService",
              guard: "isInput1",
              actions: [
                assign({ selectedService: "topup" }),
                "recordServiceEntry",
              ],
            },
            {
              target: "purchaseService",
              guard: "isInput2",
              actions: [
                assign({ selectedService: "purchase" }),
                "recordServiceEntry",
              ],
            },
            {
              target: "orderService",
              guard: "isInput3",
              actions: [
                assign({ selectedService: "orders" }),
                "recordServiceEntry",
              ],
            },
            {
              target: "faultService",
              guard: "isInput4",
              actions: [
                assign({ selectedService: "faults" }),
                "recordServiceEntry",
              ],
            },
            {
              target: "performanceService",
              guard: "isInput5",
              actions: [
                assign({ selectedService: "performance" }),
                "recordServiceEntry",
              ],
            },
            {
              target: "voucherService",
              guard: "isInput6",
              actions: [
                assign({ selectedService: "vouchers" }),
                "recordServiceEntry",
              ],
            },
            {
              target: "accountService",
              guard: "isInput7",
              actions: [
                assign({ selectedService: "account" }),
                "recordServiceEntry",
              ],
            },
            {
              target: "ixoBalanceService",
              guard: "isInput8",
              actions: [
                assign({ selectedService: "ixoBalance" }),
                "recordServiceEntry",
              ],
            },
          ],
          NavigationPatterns.userServicesChild
        ),
        SELECT_TOPUP_SERVICES: {
          target: "topupService",
          actions: ["setSelectedService", "recordServiceEntry"],
        },
        SELECT_PURCHASE_SERVICES: {
          target: "purchaseService",
          actions: ["setSelectedService", "recordServiceEntry"],
        },
        SELECT_ORDER_MANAGEMENT: {
          target: "orderService",
          actions: ["setSelectedService", "recordServiceEntry"],
        },
        SELECT_FAULT_REPORTING: {
          target: "faultService",
          actions: ["setSelectedService", "recordServiceEntry"],
        },
        SELECT_PERFORMANCE_MONITORING: {
          target: "performanceService",
          actions: ["setSelectedService", "recordServiceEntry"],
        },
        SELECT_VOUCHER_MANAGEMENT: {
          target: "voucherService",
          actions: ["setSelectedService", "recordServiceEntry"],
        },
        SELECT_ACCOUNT_MANAGEMENT: {
          target: "accountService",
          actions: ["setSelectedService", "recordServiceEntry"],
        },
        SELECT_IXO_BALANCE_CHECK: {
          target: "ixoBalanceService",
          actions: ["setSelectedService", "recordServiceEntry"],
        },
        BACK_TO_MAIN: "routeToMain",
        ERROR: {
          target: "error",
          actions: "setError",
        },
      },
    },

    // Top-up service
    topupService: {
      invoke: {
        src: "topupMachine",
        input: ({ context }) => ({
          sessionId: context.sessionId,
          phoneNumber: context.phoneNumber,
          serviceCode: context.serviceCode,
          walletId: context.walletId,
          currentBalance: context.currentBalance,
        }),
        onDone: [
          {
            target: "routeToMain",
            guard: "shouldRouteToMain",
            actions: [
              "setChildMachineOutput",
              "updateBalanceFromChild",
              "updateServiceHistory",
            ],
          },
          {
            target: "serviceSelection",
            guard: "shouldRouteToUserServices",
            actions: [
              "setChildMachineOutput",
              "updateBalanceFromChild",
              "updateServiceHistory",
              "resetChildData",
            ],
          },
          {
            target: "serviceSelection",
            actions: [
              "setChildMachineOutput",
              "updateBalanceFromChild",
              "updateServiceHistory",
              "resetChildData",
            ],
          },
        ],
        onError: {
          target: "error",
          actions: "setError",
        },
      },
    },

    // Purchase service
    purchaseService: {
      invoke: {
        src: "purchaseMachine",
        input: ({ context }) => ({
          sessionId: context.sessionId,
          phoneNumber: context.phoneNumber,
          serviceCode: context.serviceCode,
          walletId: context.walletId,
          currentBalance: context.currentBalance,
        }),
        onDone: [
          {
            target: "routeToMain",
            guard: "shouldRouteToMain",
            actions: [
              "setChildMachineOutput",
              "updateBalanceFromChild",
              "updateServiceHistory",
            ],
          },
          {
            target: "serviceSelection",
            actions: [
              "setChildMachineOutput",
              "updateBalanceFromChild",
              "updateServiceHistory",
              "resetChildData",
            ],
          },
        ],
        onError: {
          target: "error",
          actions: "setError",
        },
      },
    },

    // Order service
    orderService: {
      invoke: {
        src: "orderMachine",
        input: ({ context }) => ({
          sessionId: context.sessionId,
          phoneNumber: context.phoneNumber,
          serviceCode: context.serviceCode,
          walletId: context.walletId,
        }),
        onDone: [
          {
            target: "routeToMain",
            guard: "shouldRouteToMain",
            actions: ["setChildMachineOutput", "updateServiceHistory"],
          },
          {
            target: "serviceSelection",
            actions: [
              "setChildMachineOutput",
              "updateServiceHistory",
              "resetChildData",
            ],
          },
        ],
        onError: {
          target: "error",
          actions: "setError",
        },
      },
    },

    // Fault service
    faultService: {
      invoke: {
        src: "faultMachine",
        input: ({ context }) => ({
          sessionId: context.sessionId,
          phoneNumber: context.phoneNumber,
          serviceCode: context.serviceCode,
          walletId: context.walletId,
        }),
        onDone: [
          {
            target: "routeToMain",
            guard: "shouldRouteToMain",
            actions: ["setChildMachineOutput", "updateServiceHistory"],
          },
          {
            target: "serviceSelection",
            actions: [
              "setChildMachineOutput",
              "updateServiceHistory",
              "resetChildData",
            ],
          },
        ],
        onError: {
          target: "error",
          actions: "setError",
        },
      },
    },

    // Performance service
    performanceService: {
      invoke: {
        src: "performanceMachine",
        input: ({ context }) => ({
          sessionId: context.sessionId,
          phoneNumber: context.phoneNumber,
          serviceCode: context.serviceCode,
          walletId: context.walletId,
        }),
        onDone: [
          {
            target: "routeToMain",
            guard: "shouldRouteToMain",
            actions: ["setChildMachineOutput", "updateServiceHistory"],
          },
          {
            target: "serviceSelection",
            actions: [
              "setChildMachineOutput",
              "updateServiceHistory",
              "resetChildData",
            ],
          },
        ],
        onError: {
          target: "error",
          actions: "setError",
        },
      },
    },

    // Voucher service
    voucherService: {
      invoke: {
        src: "voucherMachine",
        input: ({ context }) => ({
          sessionId: context.sessionId,
          phoneNumber: context.phoneNumber,
          serviceCode: context.serviceCode,
          walletId: context.walletId,
          currentBalance: context.currentBalance,
        }),
        onDone: [
          {
            target: "routeToMain",
            guard: "shouldRouteToMain",
            actions: [
              "setChildMachineOutput",
              "updateBalanceFromChild",
              "updateServiceHistory",
            ],
          },
          {
            target: "serviceSelection",
            actions: [
              "setChildMachineOutput",
              "updateBalanceFromChild",
              "updateServiceHistory",
              "resetChildData",
            ],
          },
        ],
        onError: {
          target: "error",
          actions: "setError",
        },
      },
    },

    // Account service
    accountService: {
      invoke: {
        src: "accountMachine",
        input: ({ context }) => ({
          sessionId: context.sessionId,
          phoneNumber: context.phoneNumber,
          serviceCode: context.serviceCode,
          walletId: context.walletId,
        }),
        onDone: [
          {
            target: "routeToMain",
            guard: "shouldRouteToMain",
            actions: ["setChildMachineOutput", "updateServiceHistory"],
          },
          {
            target: "serviceSelection",
            actions: [
              "setChildMachineOutput",
              "updateServiceHistory",
              "resetChildData",
            ],
          },
        ],
        onError: {
          target: "error",
          actions: "setError",
        },
      },
    },

    // IXO Balance service
    ixoBalanceService: {
      invoke: {
        src: "ixoBalanceMachine",
        input: ({ context }) => ({
          sessionId: context.sessionId,
          phoneNumber: context.phoneNumber,
          serviceCode: context.serviceCode,
        }),
        onDone: [
          {
            target: "routeToMain",
            guard: "shouldRouteToMain",
            actions: ["setChildMachineOutput", "updateServiceHistory"],
          },
          {
            target: "serviceSelection",
            actions: [
              "setChildMachineOutput",
              "updateServiceHistory",
              "resetChildData",
            ],
          },
        ],
        onError: {
          target: "error",
          actions: "setError",
        },
      },
    },

    // Error state
    error: {
      entry: "setError",
      on: {
        START: {
          target: "serviceSelection",
          actions: "clearErrors",
        },
        BACK_TO_MAIN: "routeToMain",
      },
    },

    // Route back to main menu
    routeToMain: {
      type: "final",
    },
  },
});

export type UserServicesMachine = typeof userServicesMachine;
