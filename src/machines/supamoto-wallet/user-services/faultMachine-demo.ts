/* eslint-disable no-console */
import { createActor } from "xstate";
import { faultMachine } from "./faultMachine.js";

/**
 * Fault Machine Demo
 *
 * Demonstrates the fault machine functionality including:
 * - Fault category and type selection
 * - Description entry and validation
 * - Priority assignment and contact preferences
 * - Fault submission and acknowledgment
 * - Escalation flows and status tracking
 */

console.log("🚀 Fault Machine Demo\n");

const mockInput = {
  sessionId: "demo-session-fault-456",
  phoneNumber: "+260987654321",
  serviceCode: "*2233#",
  walletId: "C21009802",
};

// Demo 1: Product Fault Reporting
console.log("=".repeat(50));
console.log("DEMO 1: Product Fault Reporting");
console.log("=".repeat(50));

const actor1 = createActor(faultMachine, { input: mockInput });
actor1.subscribe(snapshot => {
  console.log(`📍 State: ${snapshot.value}`);
  if (snapshot.context.faultCategory) {
    console.log(`📂 Category: ${snapshot.context.faultCategory}`);
  }
  if (snapshot.context.faultType) {
    console.log(`🔧 Type: ${snapshot.context.faultType}`);
  }
  if (snapshot.context.faultDescription) {
    console.log(`📝 Description: ${snapshot.context.faultDescription}`);
  }
  if (snapshot.context.faultPriority) {
    console.log(`⚡ Priority: ${snapshot.context.faultPriority}`);
  }
  if (snapshot.context.contactPreference) {
    console.log(`📞 Contact: ${snapshot.context.contactPreference}`);
  }
  if (snapshot.context.faultId) {
    console.log(`🆔 Fault ID: ${snapshot.context.faultId}`);
  }
  if (snapshot.context.faultStatus) {
    console.log(`📋 Status: ${snapshot.context.faultStatus}`);
  }
  if (snapshot.context.assignedAgent) {
    console.log(`👤 Agent: ${snapshot.context.assignedAgent}`);
  }
  if (snapshot.context.estimatedResolution) {
    console.log(`📅 Est. Resolution: ${snapshot.context.estimatedResolution}`);
  }
  if (snapshot.context.validationError) {
    console.log(`⚠️ Validation Error: ${snapshot.context.validationError}`);
  }
  console.log("");
});

actor1.start();
actor1.send({ type: "START" });
console.log("📱 User started fault reporting");

actor1.send({ type: "SELECT_PRODUCT_FAULT" });
console.log("📱 User selected product fault category");

actor1.send({ type: "SELECT_FAULT_TYPE", faultType: "Not working properly" });
console.log("📱 User selected fault type");

actor1.send({
  type: "SUBMIT_DESCRIPTION",
  description:
    "The solar panel stopped charging the battery after the recent storm. No lights are working.",
});
console.log("📱 User submitted fault description");

actor1.send({
  type: "SELECT_PRODUCT",
  productId: "SOL001",
  productName: "20W Solar Home System",
  purchaseDate: "2024-01-15",
});
console.log("📱 User selected affected product");

actor1.send({ type: "SET_PRIORITY", priority: "high" });
console.log("📱 User set priority to high");

actor1.send({ type: "SELECT_AGENT_VISIT" });
console.log("📱 User requested agent visit");

actor1.send({ type: "SUBMIT_FAULT" });
console.log("📱 User submitted fault report");

setTimeout(() => {
  console.log("✅ Product fault reporting complete!\n");

  // Demo 2: Delivery Fault with Escalation
  console.log("=".repeat(50));
  console.log("DEMO 2: Delivery Fault with Escalation");
  console.log("=".repeat(50));

  const actor2 = createActor(faultMachine, { input: mockInput });
  actor2.subscribe(snapshot => {
    console.log(`📍 State: ${snapshot.value}`);
    if (snapshot.context.faultCategory) {
      console.log(`📂 Category: ${snapshot.context.faultCategory}`);
    }
    if (snapshot.context.faultPriority) {
      console.log(`⚡ Priority: ${snapshot.context.faultPriority}`);
    }
    if (snapshot.context.faultStatus) {
      console.log(`📋 Status: ${snapshot.context.faultStatus}`);
    }
    if (snapshot.context.escalationReason) {
      console.log(`🔺 Escalation Reason: ${snapshot.context.escalationReason}`);
    }
    console.log("");
  });

  actor2.start();
  actor2.send({ type: "START" });
  actor2.send({ type: "SELECT_DELIVERY_FAULT" });
  actor2.send({ type: "SELECT_FAULT_TYPE", faultType: "Late delivery" });
  actor2.send({
    type: "SUBMIT_DESCRIPTION",
    description:
      "My cookstove order was supposed to arrive 5 days ago but I have not received it yet.",
  });
  actor2.send({ type: "SET_PRIORITY", priority: "low" });
  actor2.send({ type: "SELECT_SMS_CONTACT" });
  actor2.send({ type: "SUBMIT_FAULT" });
  console.log("📱 User submitted delivery fault");

  setTimeout(() => {
    // Escalate the fault
    actor2.send({
      type: "ESCALATE_FAULT",
      reason: "Still no response after 2 days, need urgent resolution",
    });
    console.log("📱 User escalated the fault");

    setTimeout(() => {
      console.log("✅ Delivery fault with escalation complete!\n");

      // Demo 3: Validation Errors
      console.log("=".repeat(50));
      console.log("DEMO 3: Validation Errors");
      console.log("=".repeat(50));

      const actor3 = createActor(faultMachine, { input: mockInput });
      actor3.subscribe(snapshot => {
        console.log(`📍 State: ${snapshot.value}`);
        if (snapshot.context.validationError) {
          console.log(
            `⚠️ Validation Error: ${snapshot.context.validationError}`
          );
        }
        console.log("");
      });

      actor3.start();
      actor3.send({ type: "START" });
      actor3.send({ type: "SELECT_TECHNICAL_FAULT" });
      actor3.send({ type: "SELECT_FAULT_TYPE", faultType: "USSD not working" });

      // Test invalid description
      actor3.send({ type: "SUBMIT_DESCRIPTION", description: "Short" });
      console.log("📱 User entered too short description");

      // Test valid description
      actor3.send({
        type: "SUBMIT_DESCRIPTION",
        description:
          "The USSD service keeps timing out when I try to check my balance.",
      });
      console.log("📱 User entered valid description");

      actor3.send({ type: "SET_PRIORITY", priority: "medium" });
      actor3.send({ type: "SELECT_CALL_CONTACT" });
      actor3.send({ type: "SUBMIT_FAULT" });

      setTimeout(() => {
        // Test invalid escalation reason
        actor3.send({ type: "ESCALATE_FAULT", reason: "No" });
        console.log("📱 User tried invalid escalation reason");

        console.log("✅ Validation testing complete!\n");

        // Demo 4: Payment Fault
        console.log("=".repeat(50));
        console.log("DEMO 4: Payment Fault");
        console.log("=".repeat(50));

        const actor4 = createActor(faultMachine, { input: mockInput });
        actor4.subscribe(snapshot => {
          console.log(`📍 State: ${snapshot.value}`);
          if (snapshot.context.faultCategory) {
            console.log(`📂 Category: ${snapshot.context.faultCategory}`);
          }
          if (snapshot.context.faultType) {
            console.log(`🔧 Type: ${snapshot.context.faultType}`);
          }
          if (snapshot.context.contactPreference) {
            console.log(`📞 Contact: ${snapshot.context.contactPreference}`);
          }
          console.log("");
        });

        actor4.start();
        actor4.send({ type: "START" });
        actor4.send({ type: "SELECT_PAYMENT_FAULT" });
        console.log("📱 User selected payment fault");

        actor4.send({
          type: "SELECT_FAULT_TYPE",
          faultType: "Payment not processed",
        });
        console.log("📱 User selected payment not processed");

        actor4.send({
          type: "SUBMIT_DESCRIPTION",
          description:
            "I made a payment for my top-up 2 hours ago but the money has not been added to my wallet.",
        });
        console.log("📱 User described payment issue");

        actor4.send({ type: "SET_PRIORITY", priority: "critical" });
        console.log("📱 User set critical priority");

        actor4.send({ type: "SELECT_CALL_CONTACT" });
        console.log("📱 User requested phone call");

        actor4.send({ type: "SUBMIT_FAULT" });
        console.log("📱 User submitted payment fault");

        setTimeout(() => {
          console.log("✅ Payment fault reporting complete!\n");

          // Demo 5: Navigation Flow
          console.log("=".repeat(50));
          console.log("DEMO 5: Navigation Flow");
          console.log("=".repeat(50));

          const actor5 = createActor(faultMachine, { input: mockInput });
          actor5.subscribe(snapshot => {
            console.log(`📍 State: ${snapshot.value}`);
            if (snapshot.output) {
              console.log(`🎯 Output:`, snapshot.output);
            }
            console.log("");
          });

          actor5.start();
          actor5.send({ type: "START" });
          console.log("📱 User started fault reporting");

          actor5.send({ type: "SELECT_SERVICE_FAULT" });
          console.log("📱 User selected service fault");

          actor5.send({ type: "BACK_TO_CATEGORIES" });
          console.log("📱 User navigated back to categories");

          actor5.send({ type: "BACK_TO_MAIN" });
          console.log("📱 User navigated back to main menu");

          console.log("✅ Navigation flow complete!\n");

          console.log("\n🎉 Fault Machine Demo Complete!");
          console.log("\n📊 Machine Summary:");
          console.log("   • Handles comprehensive fault reporting workflows");
          console.log(
            "   • Supports 5 fault categories (product, delivery, payment, service, technical)"
          );
          console.log(
            "   • Validates fault descriptions and escalation reasons"
          );
          console.log(
            "   • Manages priority assignment and contact preferences"
          );
          console.log("   • Generates unique fault IDs and assigns agents");
          console.log(
            "   • Supports fault escalation with priority auto-upgrade"
          );
          console.log("   • Provides estimated resolution dates");
          console.log(
            "   • Includes comprehensive error handling and validation"
          );
          console.log("   • Supports navigation through fault reporting flows");
          console.log("   • Type-safe with XState v5 setup() pattern");
        }, 1200);
      }, 1200);
    }, 1200);
  }, 1200);
}, 1200);
