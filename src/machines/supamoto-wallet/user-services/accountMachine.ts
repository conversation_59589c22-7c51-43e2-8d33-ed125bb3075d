import { setup, assign } from "xstate";

/**
 * Account Machine - Account Management and Profile Settings
 *
 * Handles:
 * - Account management workflows
 * - Profile updates and personal information
 * - Security settings and PIN management
 * - Notification preferences
 * - Account verification and KYC
 *
 * Entry Points: START
 * Exit Points: Outputs routing decision for main orchestrator
 */

export interface AccountContext {
  sessionId: string;
  phoneNumber: string;
  serviceCode: string;
  walletId?: string;
  selectedAction?:
    | "profile"
    | "security"
    | "notifications"
    | "verification"
    | "settings";
  profile?: {
    firstName?: string;
    lastName?: string;
    email?: string;
    address?: string;
    dateOfBirth?: string;
    idNumber?: string;
    occupation?: string;
  };
  securitySettings?: {
    pinEnabled: boolean;
    biometricEnabled: boolean;
    twoFactorEnabled: boolean;
    sessionTimeout: number; // minutes
  };
  notificationSettings?: {
    smsEnabled: boolean;
    emailEnabled: boolean;
    pushEnabled: boolean;
    transactionAlerts: boolean;
    marketingMessages: boolean;
  };
  verificationStatus?: {
    phoneVerified: boolean;
    emailVerified: boolean;
    idVerified: boolean;
    addressVerified: boolean;
    kycLevel: "basic" | "intermediate" | "advanced";
  };
  newPin?: string;
  confirmPin?: string;
  currentPin?: string;
  verificationCode?: string;
  updateField?: string;
  updateValue?: string;
  error?: string;
  validationError?: string;
}

export type AccountEvent =
  | { type: "START" }
  | { type: "MANAGE_PROFILE" }
  | { type: "SECURITY_SETTINGS" }
  | { type: "NOTIFICATION_SETTINGS" }
  | { type: "VERIFICATION_STATUS" }
  | { type: "GENERAL_SETTINGS" }
  | { type: "UPDATE_FIRST_NAME"; value: string }
  | { type: "UPDATE_LAST_NAME"; value: string }
  | { type: "UPDATE_EMAIL"; value: string }
  | { type: "UPDATE_ADDRESS"; value: string }
  | { type: "UPDATE_DATE_OF_BIRTH"; value: string }
  | { type: "UPDATE_ID_NUMBER"; value: string }
  | { type: "UPDATE_OCCUPATION"; value: string }
  | { type: "CHANGE_PIN" }
  | { type: "SUBMIT_CURRENT_PIN"; pin: string }
  | { type: "SUBMIT_NEW_PIN"; pin: string }
  | { type: "SUBMIT_CONFIRM_PIN"; pin: string }
  | { type: "TOGGLE_BIOMETRIC" }
  | { type: "TOGGLE_TWO_FACTOR" }
  | { type: "SET_SESSION_TIMEOUT"; minutes: number }
  | { type: "TOGGLE_SMS_NOTIFICATIONS" }
  | { type: "TOGGLE_EMAIL_NOTIFICATIONS" }
  | { type: "TOGGLE_PUSH_NOTIFICATIONS" }
  | { type: "TOGGLE_TRANSACTION_ALERTS" }
  | { type: "TOGGLE_MARKETING_MESSAGES" }
  | { type: "VERIFY_EMAIL" }
  | { type: "VERIFY_ID_DOCUMENT" }
  | { type: "VERIFY_ADDRESS" }
  | { type: "SUBMIT_VERIFICATION_CODE"; code: string }
  | { type: "CONFIRM_UPDATE" }
  | { type: "UPDATE_SUCCESS" }
  | { type: "UPDATE_FAILED"; error: string }
  | { type: "VERIFICATION_SUCCESS" }
  | { type: "VERIFICATION_FAILED"; error: string }
  | { type: "BACK_TO_ACTIONS" }
  | { type: "BACK_TO_MENU" }
  | { type: "BACK_TO_MAIN" }
  | { type: "ERROR"; error: string };

// Mock account data
const MOCK_PROFILE = {
  firstName: "John",
  lastName: "Mwanza",
  email: "<EMAIL>",
  address: "123 Independence Avenue, Lusaka, Zambia",
  dateOfBirth: "1985-06-15",
  idNumber: "NRC123456789",
  occupation: "Teacher",
};

const MOCK_SECURITY_SETTINGS = {
  pinEnabled: true,
  biometricEnabled: false,
  twoFactorEnabled: true,
  sessionTimeout: 15,
};

const MOCK_NOTIFICATION_SETTINGS = {
  smsEnabled: true,
  emailEnabled: true,
  pushEnabled: false,
  transactionAlerts: true,
  marketingMessages: false,
};

const MOCK_VERIFICATION_STATUS = {
  phoneVerified: true,
  emailVerified: true,
  idVerified: false,
  addressVerified: false,
  kycLevel: "intermediate" as const,
};

export const accountMachine = setup({
  types: {
    context: {} as AccountContext,
    events: {} as AccountEvent,
    input: {} as {
      sessionId: string;
      phoneNumber: string;
      serviceCode: string;
      walletId?: string;
    },
  },

  actions: {
    initializeContext: assign({
      profile: () => MOCK_PROFILE,
      securitySettings: () => MOCK_SECURITY_SETTINGS,
      notificationSettings: () => MOCK_NOTIFICATION_SETTINGS,
      verificationStatus: () => MOCK_VERIFICATION_STATUS,
      error: undefined,
      validationError: undefined,
    }),

    setSelectedAction: assign({
      selectedAction: ({ event }) => {
        if (event.type === "MANAGE_PROFILE") return "profile";
        if (event.type === "SECURITY_SETTINGS") return "security";
        if (event.type === "NOTIFICATION_SETTINGS") return "notifications";
        if (event.type === "VERIFICATION_STATUS") return "verification";
        if (event.type === "GENERAL_SETTINGS") return "settings";
        return undefined;
      },
    }),

    setUpdateField: assign({
      updateField: ({ event }) => {
        if (event.type === "UPDATE_FIRST_NAME") return "firstName";
        if (event.type === "UPDATE_LAST_NAME") return "lastName";
        if (event.type === "UPDATE_EMAIL") return "email";
        if (event.type === "UPDATE_ADDRESS") return "address";
        if (event.type === "UPDATE_DATE_OF_BIRTH") return "dateOfBirth";
        if (event.type === "UPDATE_ID_NUMBER") return "idNumber";
        if (event.type === "UPDATE_OCCUPATION") return "occupation";
        return undefined;
      },
      updateValue: ({ event }) => {
        if (event.type === "UPDATE_FIRST_NAME") return event.value;
        if (event.type === "UPDATE_LAST_NAME") return event.value;
        if (event.type === "UPDATE_EMAIL") return event.value;
        if (event.type === "UPDATE_ADDRESS") return event.value;
        if (event.type === "UPDATE_DATE_OF_BIRTH") return event.value;
        if (event.type === "UPDATE_ID_NUMBER") return event.value;
        if (event.type === "UPDATE_OCCUPATION") return event.value;
        return undefined;
      },
    }),

    setPinData: assign({
      currentPin: ({ event }) =>
        event.type === "SUBMIT_CURRENT_PIN" ? event.pin : undefined,
      newPin: ({ event }) =>
        event.type === "SUBMIT_NEW_PIN" ? event.pin : undefined,
      confirmPin: ({ event }) =>
        event.type === "SUBMIT_CONFIRM_PIN" ? event.pin : undefined,
    }),

    setVerificationCode: assign({
      verificationCode: ({ event }) =>
        event.type === "SUBMIT_VERIFICATION_CODE" ? event.code : undefined,
    }),

    updateProfile: assign({
      profile: ({ context }) => {
        if (context.profile && context.updateField && context.updateValue) {
          return {
            ...context.profile,
            [context.updateField]: context.updateValue,
          };
        }
        return context.profile;
      },
    }),

    updateSecuritySettings: assign({
      securitySettings: ({ context, event }) => {
        if (!context.securitySettings) return context.securitySettings;

        if (event.type === "TOGGLE_BIOMETRIC") {
          return {
            ...context.securitySettings,
            biometricEnabled: !context.securitySettings.biometricEnabled,
          };
        }
        if (event.type === "TOGGLE_TWO_FACTOR") {
          return {
            ...context.securitySettings,
            twoFactorEnabled: !context.securitySettings.twoFactorEnabled,
          };
        }
        if (event.type === "SET_SESSION_TIMEOUT") {
          return {
            ...context.securitySettings,
            sessionTimeout: event.minutes,
          };
        }
        return context.securitySettings;
      },
    }),

    updateNotificationSettings: assign({
      notificationSettings: ({ context, event }) => {
        if (!context.notificationSettings) return context.notificationSettings;

        if (event.type === "TOGGLE_SMS_NOTIFICATIONS") {
          return {
            ...context.notificationSettings,
            smsEnabled: !context.notificationSettings.smsEnabled,
          };
        }
        if (event.type === "TOGGLE_EMAIL_NOTIFICATIONS") {
          return {
            ...context.notificationSettings,
            emailEnabled: !context.notificationSettings.emailEnabled,
          };
        }
        if (event.type === "TOGGLE_PUSH_NOTIFICATIONS") {
          return {
            ...context.notificationSettings,
            pushEnabled: !context.notificationSettings.pushEnabled,
          };
        }
        if (event.type === "TOGGLE_TRANSACTION_ALERTS") {
          return {
            ...context.notificationSettings,
            transactionAlerts: !context.notificationSettings.transactionAlerts,
          };
        }
        if (event.type === "TOGGLE_MARKETING_MESSAGES") {
          return {
            ...context.notificationSettings,
            marketingMessages: !context.notificationSettings.marketingMessages,
          };
        }
        return context.notificationSettings;
      },
    }),

    updateVerificationStatus: assign({
      verificationStatus: ({ context, event }) => {
        if (!context.verificationStatus) return context.verificationStatus;

        if (event.type === "VERIFY_EMAIL") {
          return {
            ...context.verificationStatus,
            emailVerified: true,
          };
        }
        if (event.type === "VERIFY_ID_DOCUMENT") {
          return {
            ...context.verificationStatus,
            idVerified: true,
            kycLevel: "advanced" as const,
          };
        }
        if (event.type === "VERIFY_ADDRESS") {
          return {
            ...context.verificationStatus,
            addressVerified: true,
          };
        }
        return context.verificationStatus;
      },
    }),

    resetPinData: assign({
      currentPin: undefined,
      newPin: undefined,
      confirmPin: undefined,
    }),

    resetUpdateData: assign({
      updateField: undefined,
      updateValue: undefined,
      verificationCode: undefined,
    }),

    setError: assign({
      error: ({ event }) =>
        event.type === "ERROR" ? event.error : "An error occurred",
    }),

    setValidationError: assign({
      validationError: ({ context, event }) => {
        if (event.type === "SUBMIT_NEW_PIN" && event.pin.length !== 4) {
          return "PIN must be 4 digits";
        }
        if (
          event.type === "SUBMIT_CONFIRM_PIN" &&
          event.pin !== context.newPin
        ) {
          return "PIN confirmation does not match";
        }
        if (event.type === "UPDATE_EMAIL" && !event.value.includes("@")) {
          return "Please enter a valid email address";
        }
        if (event.type === "UPDATE_ID_NUMBER" && event.value.length < 8) {
          return "Please enter a valid ID number";
        }
        if (
          event.type === "SUBMIT_VERIFICATION_CODE" &&
          event.code.length !== 6
        ) {
          return "Verification code must be 6 digits";
        }
        return undefined;
      },
    }),

    clearErrors: assign({
      error: undefined,
      validationError: undefined,
    }),
  },

  guards: {
    isProfileAction: ({ context }) => context.selectedAction === "profile",
    isSecurityAction: ({ context }) => context.selectedAction === "security",
    isNotificationAction: ({ context }) =>
      context.selectedAction === "notifications",
    isVerificationAction: ({ context }) =>
      context.selectedAction === "verification",
    isSettingsAction: ({ context }) => context.selectedAction === "settings",

    isValidPin: ({ event }) =>
      event.type === "SUBMIT_NEW_PIN" &&
      event.pin.length === 5 &&
      /^\d{5}$/.test(event.pin),

    isPinMatch: ({ context, event }) =>
      event.type === "SUBMIT_CONFIRM_PIN" && event.pin === context.newPin,

    isValidEmail: ({ event }) =>
      event.type === "UPDATE_EMAIL" &&
      event.value.includes("@") &&
      event.value.includes("."),

    isValidIdNumber: ({ event }) =>
      event.type === "UPDATE_ID_NUMBER" && event.value.length >= 8,

    isValidVerificationCode: ({ event }) =>
      event.type === "SUBMIT_VERIFICATION_CODE" &&
      event.code.length === 6 &&
      /^\d{6}$/.test(event.code),

    hasError: ({ context }) => Boolean(context.error),
    hasValidationError: ({ context }) => Boolean(context.validationError),
  },
}).createMachine({
  id: "accountMachine",
  initial: "idle",

  context: ({ input }) => ({
    sessionId: input?.sessionId || "",
    phoneNumber: input?.phoneNumber || "",
    serviceCode: input?.serviceCode || "",
    walletId: input?.walletId,
  }),

  states: {
    // Initial state - waiting for start
    idle: {
      on: {
        START: {
          target: "actionSelection",
          actions: "initializeContext",
        },
      },
    },

    // Account action selection menu
    actionSelection: {
      entry: "clearErrors",
      on: {
        MANAGE_PROFILE: {
          target: "profileManagement",
          actions: "setSelectedAction",
        },
        SECURITY_SETTINGS: {
          target: "securityManagement",
          actions: "setSelectedAction",
        },
        NOTIFICATION_SETTINGS: {
          target: "notificationManagement",
          actions: "setSelectedAction",
        },
        VERIFICATION_STATUS: {
          target: "verificationManagement",
          actions: "setSelectedAction",
        },
        GENERAL_SETTINGS: {
          target: "generalSettings",
          actions: "setSelectedAction",
        },
        BACK_TO_MENU: "routeToUserServices",
        BACK_TO_MAIN: "routeToMain",
        ERROR: {
          target: "error",
          actions: "setError",
        },
      },
    },

    // Profile management
    profileManagement: {
      on: {
        UPDATE_FIRST_NAME: {
          target: "updateConfirmation",
          actions: "setUpdateField",
        },
        UPDATE_LAST_NAME: {
          target: "updateConfirmation",
          actions: "setUpdateField",
        },
        UPDATE_EMAIL: [
          {
            target: "updateConfirmation",
            actions: "setUpdateField",
            guard: "isValidEmail",
          },
          {
            target: "profileManagement",
            actions: "setValidationError",
          },
        ],
        UPDATE_ADDRESS: {
          target: "updateConfirmation",
          actions: "setUpdateField",
        },
        UPDATE_DATE_OF_BIRTH: {
          target: "updateConfirmation",
          actions: "setUpdateField",
        },
        UPDATE_ID_NUMBER: [
          {
            target: "updateConfirmation",
            actions: "setUpdateField",
            guard: "isValidIdNumber",
          },
          {
            target: "profileManagement",
            actions: "setValidationError",
          },
        ],
        UPDATE_OCCUPATION: {
          target: "updateConfirmation",
          actions: "setUpdateField",
        },
        BACK_TO_ACTIONS: "actionSelection",
        BACK_TO_MAIN: "routeToMain",
        ERROR: {
          target: "error",
          actions: "setError",
        },
      },
    },

    // Security management
    securityManagement: {
      on: {
        CHANGE_PIN: "pinChangeFlow",
        TOGGLE_BIOMETRIC: {
          actions: "updateSecuritySettings",
        },
        TOGGLE_TWO_FACTOR: {
          actions: "updateSecuritySettings",
        },
        SET_SESSION_TIMEOUT: {
          actions: "updateSecuritySettings",
        },
        BACK_TO_ACTIONS: "actionSelection",
        BACK_TO_MAIN: "routeToMain",
        ERROR: {
          target: "error",
          actions: "setError",
        },
      },
    },

    // PIN change flow
    pinChangeFlow: {
      on: {
        SUBMIT_CURRENT_PIN: "newPinEntry",
        BACK_TO_ACTIONS: "securityManagement",
        BACK_TO_MAIN: "routeToMain",
        ERROR: {
          target: "error",
          actions: "setError",
        },
      },
    },

    // New PIN entry
    newPinEntry: {
      on: {
        SUBMIT_NEW_PIN: [
          {
            target: "confirmPinEntry",
            actions: "setPinData",
            guard: "isValidPin",
          },
          {
            target: "newPinEntry",
            actions: "setValidationError",
          },
        ],
        BACK_TO_ACTIONS: "pinChangeFlow",
        BACK_TO_MAIN: "routeToMain",
        ERROR: {
          target: "error",
          actions: "setError",
        },
      },
    },

    // Confirm PIN entry
    confirmPinEntry: {
      on: {
        SUBMIT_CONFIRM_PIN: [
          {
            target: "processingUpdate",
            actions: "setPinData",
            guard: "isPinMatch",
          },
          {
            target: "confirmPinEntry",
            actions: "setValidationError",
          },
        ],
        BACK_TO_ACTIONS: "newPinEntry",
        BACK_TO_MAIN: "routeToMain",
        ERROR: {
          target: "error",
          actions: "setError",
        },
      },
    },

    // Notification management
    notificationManagement: {
      on: {
        TOGGLE_SMS_NOTIFICATIONS: {
          actions: "updateNotificationSettings",
        },
        TOGGLE_EMAIL_NOTIFICATIONS: {
          actions: "updateNotificationSettings",
        },
        TOGGLE_PUSH_NOTIFICATIONS: {
          actions: "updateNotificationSettings",
        },
        TOGGLE_TRANSACTION_ALERTS: {
          actions: "updateNotificationSettings",
        },
        TOGGLE_MARKETING_MESSAGES: {
          actions: "updateNotificationSettings",
        },
        BACK_TO_ACTIONS: "actionSelection",
        BACK_TO_MAIN: "routeToMain",
        ERROR: {
          target: "error",
          actions: "setError",
        },
      },
    },

    // Verification management
    verificationManagement: {
      on: {
        VERIFY_EMAIL: "verificationCodeEntry",
        VERIFY_ID_DOCUMENT: "processingVerification",
        VERIFY_ADDRESS: "processingVerification",
        BACK_TO_ACTIONS: "actionSelection",
        BACK_TO_MAIN: "routeToMain",
        ERROR: {
          target: "error",
          actions: "setError",
        },
      },
    },

    // Verification code entry
    verificationCodeEntry: {
      on: {
        SUBMIT_VERIFICATION_CODE: [
          {
            target: "processingVerification",
            actions: "setVerificationCode",
            guard: "isValidVerificationCode",
          },
          {
            target: "verificationCodeEntry",
            actions: "setValidationError",
          },
        ],
        BACK_TO_ACTIONS: "verificationManagement",
        BACK_TO_MAIN: "routeToMain",
        ERROR: {
          target: "error",
          actions: "setError",
        },
      },
    },

    // General settings
    generalSettings: {
      on: {
        BACK_TO_ACTIONS: "actionSelection",
        BACK_TO_MAIN: "routeToMain",
        ERROR: {
          target: "error",
          actions: "setError",
        },
      },
    },

    // Update confirmation
    updateConfirmation: {
      on: {
        CONFIRM_UPDATE: "processingUpdate",
        BACK_TO_ACTIONS: "profileManagement",
        BACK_TO_MAIN: "routeToMain",
        ERROR: {
          target: "error",
          actions: "setError",
        },
      },
    },

    // Processing update
    processingUpdate: {
      entry: "updateProfile",
      after: {
        1000: "updateSuccess", // Simulate processing time
      },
      on: {
        UPDATE_FAILED: {
          target: "updateFailed",
          actions: "setError",
        },
      },
    },

    // Processing verification
    processingVerification: {
      entry: "updateVerificationStatus",
      after: {
        1000: "verificationSuccess", // Simulate processing time
      },
      on: {
        VERIFICATION_FAILED: {
          target: "verificationFailed",
          actions: "setError",
        },
      },
    },

    // Update successful
    updateSuccess: {
      entry: "resetUpdateData",
      on: {
        BACK_TO_ACTIONS: "actionSelection",
        BACK_TO_MAIN: "routeToMain",
      },
    },

    // Verification successful
    verificationSuccess: {
      entry: "resetUpdateData",
      on: {
        BACK_TO_ACTIONS: "actionSelection",
        BACK_TO_MAIN: "routeToMain",
      },
    },

    // Update failed
    updateFailed: {
      on: {
        BACK_TO_ACTIONS: "actionSelection",
        BACK_TO_MAIN: "routeToMain",
      },
    },

    // Verification failed
    verificationFailed: {
      on: {
        BACK_TO_ACTIONS: "actionSelection",
        BACK_TO_MAIN: "routeToMain",
      },
    },

    // Error state
    error: {
      entry: "setError",
      on: {
        START: {
          target: "actionSelection",
          actions: "clearErrors",
        },
        BACK_TO_MENU: "routeToUserServices",
        BACK_TO_MAIN: "routeToMain",
      },
    },

    // Route back to user services
    routeToUserServices: {
      type: "final",
      output: {
        route: "userServices" as const,
        context: ({ context }: { context: AccountContext }) => context,
      },
    },

    // Route back to main menu
    routeToMain: {
      type: "final",
      output: {
        route: "main" as const,
        context: ({ context }: { context: AccountContext }) => context,
      },
    },
  },
});

export type AccountMachine = typeof accountMachine;
