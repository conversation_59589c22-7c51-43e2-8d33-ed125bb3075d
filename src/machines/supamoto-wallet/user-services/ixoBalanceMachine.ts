import { setup, assign, from<PERSON>rom<PERSON> } from "xstate";
import { getIxoAccountBalance } from "../../../services/ixo-queries.js";
import { CHAIN_RPC_URL } from "../../../constants/common.js";

/**
 * IXO Balance Check Machine
 *
 * Allows users to check the IXO token balance of any IXO account address.
 *
 * Flow:
 * 1. User enters IXO account address
 * 2. System queries blockchain for balance
 * 3. Display balance with "$IXO" suffix or "IXO Account Not Found"
 *
 * Entry Points: START
 * Exit Points: Outputs routing decision for parent machine
 */

export interface IxoBalanceContext {
  sessionId: string;
  phoneNumber: string;
  serviceCode: string;
  ixoAddress?: string;
  balance?: string;
  balanceDisplay?: string;
  message?: string;
  error?: string;
  validationError?: string;
}

export type IxoBalanceEvent =
  | { type: "START" }
  | { type: "INPUT_ADDRESS"; input: string }
  | { type: "CHECK_BALANCE" }
  | { type: "BACK_TO_SERVICES" }
  | { type: "BACK_TO_MAIN" }
  | { type: "ERROR"; error: string };

export const ixoBalanceMachine = setup({
  types: {
    context: {} as IxoBalanceContext,
    events: {} as IxoBalanceEvent,
    input: {} as {
      sessionId: string;
      phoneNumber: string;
      serviceCode: string;
    },
  },

  actors: {
    checkIxoBalance: fromPromise(
      async ({ input }: { input: { address: string } }) => {
        if (!input.address) {
          throw new Error("IXO address is required");
        }

        // Validate basic address format (IXO addresses are typically bech32 format starting with "ixo")
        if (!input.address.startsWith("ixo") || input.address.length < 20) {
          throw new Error("Invalid IXO address format");
        }

        try {
          const balance = await getIxoAccountBalance(
            input.address,
            CHAIN_RPC_URL
          );

          // Convert from uixo to IXO (divide by 1,000,000)
          const balanceInIxo = parseFloat(balance) / 1000000;

          return {
            address: input.address,
            balance: balance,
            balanceInIxo: balanceInIxo,
            found: balance !== "0" || true, // Consider any response as found, even 0 balance
          };
        } catch (error) {
          // If we get an error, it likely means the account doesn't exist
          throw new Error("IXO Account Not Found");
        }
      }
    ),
  },

  actions: {
    initializeContext: assign({
      ixoAddress: undefined,
      balance: undefined,
      balanceDisplay: undefined,
      error: undefined,
      validationError: undefined,
      message: "Check IXO Account Balance\n\nEnter IXO account address:",
    }),

    setIxoAddress: assign({
      ixoAddress: ({ event }) =>
        event.type === "INPUT_ADDRESS" ? event.input.trim() : undefined,
      validationError: undefined,
    }),

    validateAddress: assign({
      validationError: ({ context }) => {
        if (!context.ixoAddress) {
          return "Please enter an IXO address";
        }
        if (!context.ixoAddress.startsWith("ixo")) {
          return "IXO address must start with 'ixo'";
        }
        if (context.ixoAddress.length < 20) {
          return "IXO address is too short";
        }
        return undefined;
      },
    }),

    setBalanceResult: assign({
      balance: ({ event }) => (event as any).output?.balance,
      balanceDisplay: ({ event }) => {
        const balanceInIxo = (event as any).output?.balanceInIxo || 0;
        return `Balance: ${balanceInIxo.toFixed(6)} $IXO`;
      },
      message: ({ event, context }) => {
        const balanceInIxo = (event as any).output?.balanceInIxo || 0;
        return `IXO Account: ${context.ixoAddress}\nBalance: ${balanceInIxo.toFixed(6)} $IXO\n\n1. Check Another\n0. Back to Services`;
      },
      error: undefined,
    }),

    setBalanceError: assign({
      error: ({ event }) =>
        ((event as any).error as Error)?.message || "Failed to check balance",
      message: ({ event, context }) => {
        const errorMessage =
          ((event as any).error as Error)?.message || "Failed to check balance";
        return `${errorMessage}\n\nIXO Address: ${context.ixoAddress}\n\n1. Try Again\n0. Back to Services`;
      },
    }),

    setError: assign({
      error: ({ event }) =>
        event.type === "ERROR" ? event.error : "An error occurred",
    }),

    clearErrors: assign({
      error: undefined,
      validationError: undefined,
    }),

    resetForNewCheck: assign({
      ixoAddress: undefined,
      balance: undefined,
      balanceDisplay: undefined,
      error: undefined,
      validationError: undefined,
      message: "Check IXO Account Balance\n\nEnter IXO account address:",
    }),
  },

  guards: {
    hasValidAddress: ({ context }) =>
      Boolean(context.ixoAddress && !context.validationError),

    hasValidationError: ({ context }) => Boolean(context.validationError),
  },
}).createMachine({
  id: "ixoBalanceMachine",
  initial: "idle",

  context: ({ input }) => ({
    sessionId: input?.sessionId || "",
    phoneNumber: input?.phoneNumber || "",
    serviceCode: input?.serviceCode || "",
    message: "Check IXO Account Balance\n\nEnter IXO account address:",
  }),

  states: {
    // Initial state - waiting for start
    idle: {
      on: {
        START: {
          target: "addressInput",
          actions: "initializeContext",
        },
      },
    },

    // Address input state
    addressInput: {
      entry: "clearErrors",
      on: {
        INPUT_ADDRESS: {
          target: "validatingAddress",
          actions: "setIxoAddress",
        },
        BACK_TO_SERVICES: "routeToUserServices",
        BACK_TO_MAIN: "routeToMain",
        ERROR: {
          target: "error",
          actions: "setError",
        },
      },
    },

    // Validating address format
    validatingAddress: {
      entry: "validateAddress",
      always: [
        {
          target: "checkingBalance",
          guard: "hasValidAddress",
        },
        {
          target: "addressInput",
          guard: "hasValidationError",
        },
      ],
    },

    // Checking balance on blockchain
    checkingBalance: {
      invoke: {
        src: "checkIxoBalance",
        input: ({ context }) => ({ address: context.ixoAddress! }),
        onDone: {
          target: "balanceResult",
          actions: "setBalanceResult",
        },
        onError: {
          target: "balanceError",
          actions: "setBalanceError",
        },
      },
    },

    // Successfully retrieved balance
    balanceResult: {
      on: {
        INPUT_ADDRESS: [
          {
            target: "addressInput",
            guard: ({ event }) => event.input === "1",
            actions: "resetForNewCheck",
          },
          {
            target: "routeToUserServices",
            guard: ({ event }) => event.input === "0",
          },
        ],
        BACK_TO_SERVICES: "routeToUserServices",
        BACK_TO_MAIN: "routeToMain",
      },
    },

    // Error retrieving balance (account not found, etc.)
    balanceError: {
      on: {
        INPUT_ADDRESS: [
          {
            target: "addressInput",
            guard: ({ event }) => event.input === "1",
            actions: "resetForNewCheck",
          },
          {
            target: "routeToUserServices",
            guard: ({ event }) => event.input === "0",
          },
        ],
        BACK_TO_SERVICES: "routeToUserServices",
        BACK_TO_MAIN: "routeToMain",
      },
    },

    // General error state
    error: {
      entry: "setError",
      on: {
        START: {
          target: "addressInput",
          actions: "clearErrors",
        },
        BACK_TO_SERVICES: "routeToUserServices",
        BACK_TO_MAIN: "routeToMain",
      },
    },

    // Route back to user services
    routeToUserServices: {
      type: "final",
      output: {
        route: "userServices" as const,
        context: ({ context }: { context: IxoBalanceContext }) => context,
      },
    },

    // Route back to main menu
    routeToMain: {
      type: "final",
      output: {
        route: "main" as const,
        context: ({ context }: { context: IxoBalanceContext }) => context,
      },
    },
  },
});

export type IxoBalanceMachine = typeof ixoBalanceMachine;
