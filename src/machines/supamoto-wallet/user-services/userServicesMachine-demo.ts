/* eslint-disable no-console */
import { createActor } from "xstate";
import { userServicesMachine } from "./userServicesMachine.js";

/**
 * User Services Machine Demo
 *
 * Demonstrates the user services orchestrator functionality including:
 * - Service selection and routing
 * - Child machine coordination
 * - Balance management and updates
 * - Service history tracking
 * - Error handling and recovery
 */

console.log("🚀 User Services Machine Demo\n");

const mockInput = {
  sessionId: "demo-session-userservices-123",
  phoneNumber: "+************",
  serviceCode: "*2233#",
  walletId: "*********",
  currentBalance: 250.75,
};

// Demo 1: Service Selection and Navigation
console.log("=".repeat(50));
console.log("DEMO 1: Service Selection and Navigation");
console.log("=".repeat(50));

const actor1 = createActor(userServicesMachine, { input: mockInput });
actor1.subscribe(snapshot => {
  console.log(`📍 State: ${snapshot.value}`);
  if (snapshot.context.selectedService) {
    console.log(`🎯 Selected Service: ${snapshot.context.selectedService}`);
  }
  if (snapshot.context.currentBalance !== undefined) {
    console.log(
      `💰 Current Balance: K${snapshot.context.currentBalance.toFixed(2)}`
    );
  }
  if (
    snapshot.context.serviceHistory &&
    snapshot.context.serviceHistory.length > 0
  ) {
    console.log(
      `📊 Service History: ${snapshot.context.serviceHistory.length} entries`
    );
    snapshot.context.serviceHistory.forEach((entry, index) => {
      const duration = entry.duration ? ` (${entry.duration}ms)` : "";
      console.log(
        `   ${index + 1}. ${entry.service} - ${new Date(entry.timestamp).toLocaleTimeString()}${duration}`
      );
    });
  }
  if (snapshot.output) {
    console.log(`🎯 Output:`, snapshot.output);
  }
  console.log("");
});

actor1.start();
actor1.send({ type: "START" });
console.log("📱 User started user services");

setTimeout(() => {
  actor1.send({ type: "SELECT_TOPUP_SERVICES" });
  console.log("📱 User selected top-up services");

  setTimeout(() => {
    console.log("✅ Top-up service completed, returned to service selection");

    actor1.send({ type: "SELECT_VOUCHER_MANAGEMENT" });
    console.log("📱 User selected voucher management");

    setTimeout(() => {
      console.log("✅ Voucher service completed");

      actor1.send({ type: "BACK_TO_MAIN" });
      console.log("📱 User navigated back to main menu");

      console.log("✅ Service selection demo complete!\n");

      // Demo 2: Service History Tracking
      console.log("=".repeat(50));
      console.log("DEMO 2: Service History Tracking");
      console.log("=".repeat(50));

      const actor2 = createActor(userServicesMachine, { input: mockInput });
      actor2.subscribe(snapshot => {
        console.log(`📍 State: ${snapshot.value}`);
        if (
          snapshot.context.serviceHistory &&
          snapshot.context.serviceHistory.length > 0
        ) {
          console.log(`📊 Service History:`);
          snapshot.context.serviceHistory.forEach((entry, index) => {
            const timestamp = new Date(entry.timestamp).toLocaleTimeString();
            const duration = entry.duration
              ? ` - Duration: ${entry.duration}ms`
              : " - In Progress";
            console.log(
              `   ${index + 1}. ${entry.service.toUpperCase()} at ${timestamp}${duration}`
            );
          });
        }
        console.log("");
      });

      actor2.start();
      actor2.send({ type: "START" });
      console.log("📱 User started services for history tracking");

      setTimeout(() => {
        actor2.send({ type: "SELECT_PURCHASE_SERVICES" });
        console.log("📱 User selected purchase services");

        setTimeout(() => {
          actor2.send({ type: "SELECT_ORDER_MANAGEMENT" });
          console.log("📱 User selected order management");

          setTimeout(() => {
            actor2.send({ type: "SELECT_FAULT_REPORTING" });
            console.log("📱 User selected fault reporting");

            setTimeout(() => {
              console.log("✅ Service history tracking demo complete!\n");

              // Demo 3: Balance Management
              console.log("=".repeat(50));
              console.log("DEMO 3: Balance Management");
              console.log("=".repeat(50));

              const actor3 = createActor(userServicesMachine, {
                input: { ...mockInput, currentBalance: 100 },
              });
              actor3.subscribe(snapshot => {
                console.log(`📍 State: ${snapshot.value}`);
                if (snapshot.context.currentBalance !== undefined) {
                  console.log(
                    `💰 Balance: K${snapshot.context.currentBalance.toFixed(2)}`
                  );
                }
                if (snapshot.context.childMachineOutput) {
                  console.log(`🔄 Child Machine Output Received`);
                }
                console.log("");
              });

              actor3.start();
              actor3.send({ type: "START" });
              console.log("📱 User started with K100 balance");

              setTimeout(() => {
                console.log("💰 Balance loaded from server");

                actor3.send({ type: "SELECT_TOPUP_SERVICES" });
                console.log(
                  "📱 User performed top-up (simulated balance increase)"
                );

                setTimeout(() => {
                  console.log("✅ Balance updated after top-up service");

                  actor3.send({ type: "SELECT_VOUCHER_MANAGEMENT" });
                  console.log(
                    "📱 User used voucher service (simulated balance change)"
                  );

                  setTimeout(() => {
                    console.log("✅ Balance management demo complete!\n");

                    // Demo 4: Error Handling
                    console.log("=".repeat(50));
                    console.log("DEMO 4: Error Handling");
                    console.log("=".repeat(50));

                    const actor4 = createActor(userServicesMachine, {
                      input: mockInput,
                    });
                    actor4.subscribe(snapshot => {
                      console.log(`📍 State: ${snapshot.value}`);
                      if (snapshot.context.error) {
                        console.log(`❌ Error: ${snapshot.context.error}`);
                      }
                      console.log("");
                    });

                    actor4.start();
                    actor4.send({ type: "START" });
                    console.log("📱 User started services");

                    setTimeout(() => {
                      actor4.send({
                        type: "ERROR",
                        error: "Network connection failed",
                      });
                      console.log("📱 Network error occurred");

                      setTimeout(() => {
                        actor4.send({ type: "START" });
                        console.log("📱 User restarted after error recovery");

                        setTimeout(() => {
                          console.log("✅ Error handling demo complete!\n");

                          console.log(
                            "\n🎉 User Services Machine Demo Complete!"
                          );
                          console.log("\n📊 Machine Summary:");
                          console.log(
                            "   • Orchestrates all user service workflows"
                          );
                          console.log(
                            "   • Coordinates 7 different service machines:"
                          );
                          console.log(
                            "     - Top-up services (airtime, wallet, mobile money)"
                          );
                          console.log(
                            "     - Purchase services (product catalog and ordering)"
                          );
                          console.log(
                            "     - Order management (tracking, modification)"
                          );
                          console.log(
                            "     - Fault reporting (issue categorization)"
                          );
                          console.log(
                            "     - Performance monitoring (metrics and analytics)"
                          );
                          console.log(
                            "     - Voucher management (redemption, purchase)"
                          );
                          console.log(
                            "     - Account management (profile, security)"
                          );
                          console.log(
                            "   • Manages balance updates across services"
                          );
                          console.log(
                            "   • Tracks service usage history with timestamps"
                          );
                          console.log(
                            "   • Handles routing between services and main menu"
                          );
                          console.log(
                            "   • Provides comprehensive error handling"
                          );
                          console.log(
                            "   • Type-safe with XState v5 setup() pattern"
                          );
                        }, 1000);
                      }, 1000);
                    }, 1000);
                  }, 1500);
                }, 1500);
              }, 600);
            }, 1500);
          }, 1500);
        }, 1500);
      }, 600);
    }, 1500);
  }, 1500);
}, 600);
