/* eslint-disable no-console */
import { createActor } from "xstate";
import { performanceMachine } from "./performanceMachine.js";

/**
 * Performance Machine Demo
 *
 * Demonstrates performance monitoring functionality including:
 * - Metric selection and period filtering
 * - Performance data display and refresh
 * - Report generation and export
 * - Environmental impact tracking
 */

console.log("🚀 Performance Machine Demo\n");

const mockInput = {
  sessionId: "demo-session-perf-123",
  phoneNumber: "+260987654321",
  serviceCode: "*2233#",
  walletId: "C21009802",
};

// Demo 1: Energy Metrics with Monthly Report
console.log("=".repeat(50));
console.log("DEMO 1: Energy Metrics with Monthly Report");
console.log("=".repeat(50));

const actor1 = createActor(performanceMachine, { input: mockInput });
actor1.subscribe(snapshot => {
  console.log(`📍 State: ${snapshot.value}`);
  if (snapshot.context.selectedMetric) {
    console.log(`📊 Metric: ${snapshot.context.selectedMetric}`);
  }
  if (snapshot.context.selectedPeriod) {
    console.log(`📅 Period: ${snapshot.context.selectedPeriod}`);
  }
  if (snapshot.context.performanceData) {
    const data = snapshot.context.performanceData;
    console.log(`⚡ Energy Generated: ${data.energyGenerated} kWh`);
    console.log(`🔋 Energy Consumed: ${data.energyConsumed} kWh`);
    console.log(`📈 System Efficiency: ${data.systemEfficiency}%`);
    console.log(`⏱️ Uptime: ${data.uptime}%`);
  }
  if (snapshot.context.reportType) {
    console.log(`📋 Report Type: ${snapshot.context.reportType}`);
  }
  console.log("");
});

actor1.start();
actor1.send({ type: "START" });
console.log("📱 User started performance monitoring");

actor1.send({ type: "VIEW_ENERGY_METRICS" });
console.log("📱 User selected energy metrics");

actor1.send({ type: "SELECT_MONTHLY_PERIOD" });
console.log("📱 User selected monthly period");

actor1.send({ type: "GENERATE_SUMMARY_REPORT" });
console.log("📱 User generated summary report");

setTimeout(() => {
  console.log("✅ Energy metrics demo complete!\n");

  // Demo 2: Environmental Impact
  console.log("=".repeat(50));
  console.log("DEMO 2: Environmental Impact Tracking");
  console.log("=".repeat(50));

  const actor2 = createActor(performanceMachine, { input: mockInput });
  actor2.subscribe(snapshot => {
    console.log(`📍 State: ${snapshot.value}`);
    if (snapshot.context.performanceData) {
      const data = snapshot.context.performanceData;
      console.log(`🌱 CO2 Saved: ${data.co2Saved} kg`);
      console.log(`🌳 Trees Equivalent: ${data.treesEquivalent}`);
    }
    if (snapshot.context.comparisonData) {
      console.log(`📊 Comparison data generated`);
    }
    console.log("");
  });

  actor2.start();
  actor2.send({ type: "START" });
  actor2.send({ type: "VIEW_ENVIRONMENTAL_IMPACT" });
  actor2.send({ type: "SELECT_YEARLY_PERIOD" });
  console.log("📱 User viewed yearly environmental impact");

  actor2.send({ type: "GENERATE_COMPARISON_REPORT" });
  console.log("📱 User generated comparison report");

  setTimeout(() => {
    console.log("✅ Environmental impact demo complete!\n");

    // Demo 3: Financial Metrics with Export
    console.log("=".repeat(50));
    console.log("DEMO 3: Financial Metrics with Export");
    console.log("=".repeat(50));

    const actor3 = createActor(performanceMachine, { input: mockInput });
    actor3.subscribe(snapshot => {
      console.log(`📍 State: ${snapshot.value}`);
      if (snapshot.context.performanceData) {
        const data = snapshot.context.performanceData;
        console.log(`💰 Cost Savings: K${data.costSavings}`);
      }
      console.log("");
    });

    actor3.start();
    actor3.send({ type: "START" });
    actor3.send({ type: "VIEW_FINANCIAL_METRICS" });
    actor3.send({ type: "SELECT_WEEKLY_PERIOD" });
    actor3.send({ type: "GENERATE_DETAILED_REPORT" });
    console.log("📱 User generated detailed financial report");

    setTimeout(() => {
      actor3.send({ type: "EXPORT_REPORT" });
      console.log("📱 User exported report");

      setTimeout(() => {
        console.log("✅ Financial metrics with export complete!\n");

        console.log("\n🎉 Performance Machine Demo Complete!");
        console.log("\n📊 Machine Summary:");
        console.log("   • Handles performance monitoring workflows");
        console.log(
          "   • Supports 5 metric types (energy, environmental, usage, financial, maintenance)"
        );
        console.log(
          "   • Provides multiple time periods (daily, weekly, monthly, yearly)"
        );
        console.log(
          "   • Generates various report types (summary, detailed, comparison, trends)"
        );
        console.log("   • Includes data refresh and export functionality");
        console.log("   • Tracks environmental impact and cost savings");
        console.log("   • Supports report pagination for detailed views");
        console.log("   • Type-safe with XState v5 setup() pattern");
      }, 1200);
    }, 600);
  }, 600);
}, 600);
