/* eslint-disable no-console */
import { createActor } from "xstate";
import { orderMachine } from "./orderMachine.js";

/**
 * Order Machine Demo
 *
 * Demonstrates the order machine functionality including:
 * - Order viewing and pagination
 * - Order tracking by ID
 * - Order modification requests
 * - Order cancellation flows
 * - Status updates and validation
 */

console.log("🚀 Order Machine Demo\n");

const mockInput = {
  sessionId: "demo-session-order-789",
  phoneNumber: "+260987654321",
  serviceCode: "*2233#",
  walletId: "C21009802",
};

// Demo 1: View Orders List
console.log("=".repeat(50));
console.log("DEMO 1: View Orders List");
console.log("=".repeat(50));

const actor1 = createActor(orderMachine, { input: mockInput });
actor1.subscribe(snapshot => {
  console.log(`📍 State: ${snapshot.value}`);
  if (snapshot.context.selectedAction) {
    console.log(`🎯 Action: ${snapshot.context.selectedAction}`);
  }
  if (snapshot.context.orders) {
    console.log(`📦 Orders Found: ${snapshot.context.orders.length}`);
    snapshot.context.orders.forEach((order, index) => {
      console.log(
        `   ${index + 1}. ${order.productName} - ${order.status} (${order.id})`
      );
    });
  }
  if (snapshot.context.currentPage && snapshot.context.totalPages) {
    console.log(
      `📄 Page: ${snapshot.context.currentPage}/${snapshot.context.totalPages}`
    );
  }
  console.log("");
});

actor1.start();
actor1.send({ type: "START" });
console.log("📱 User started order management");

actor1.send({ type: "VIEW_ORDERS" });
console.log("📱 User selected view orders");

actor1.send({ type: "SELECT_ORDER", orderId: "ORD_001" });
console.log("📱 User selected order ORD_001");

console.log("✅ Order viewing complete!\n");

// Demo 2: Track Order by ID
console.log("=".repeat(50));
console.log("DEMO 2: Track Order by ID");
console.log("=".repeat(50));

const actor2 = createActor(orderMachine, { input: mockInput });
actor2.subscribe(snapshot => {
  console.log(`📍 State: ${snapshot.value}`);
  if (snapshot.context.selectedOrder) {
    const order = snapshot.context.selectedOrder;
    console.log(`📦 Order: ${order.productName}`);
    console.log(`📋 Status: ${order.status}`);
    console.log(`📅 Date: ${order.orderDate}`);
    console.log(`🏠 Address: ${order.deliveryAddress}`);
    if (order.trackingNumber) {
      console.log(`🚚 Tracking: ${order.trackingNumber}`);
    }
    if (order.estimatedDelivery) {
      console.log(`📅 Est. Delivery: ${order.estimatedDelivery}`);
    }
  }
  if (snapshot.context.validationError) {
    console.log(`⚠️ Validation Error: ${snapshot.context.validationError}`);
  }
  console.log("");
});

actor2.start();
actor2.send({ type: "START" });
actor2.send({ type: "TRACK_ORDER" });
console.log("📱 User selected track order");

actor2.send({ type: "SUBMIT_ORDER_ID", orderId: "INVALID_ID" });
console.log("📱 User entered invalid order ID");

actor2.send({ type: "SUBMIT_ORDER_ID", orderId: "ORD_002" });
console.log("📱 User entered valid order ID (ORD_002)");

actor2.send({ type: "REFRESH_STATUS" });
console.log("📱 User refreshed order status");

console.log("✅ Order tracking complete!\n");

// Demo 3: Order Modification
console.log("=".repeat(50));
console.log("DEMO 3: Order Modification");
console.log("=".repeat(50));

const actor3 = createActor(orderMachine, { input: mockInput });
actor3.subscribe(snapshot => {
  console.log(`📍 State: ${snapshot.value}`);
  if (snapshot.context.modificationRequest) {
    const mod = snapshot.context.modificationRequest;
    console.log(`🔧 Modification Type: ${mod.type}`);
    if (mod.newAddress) console.log(`🏠 New Address: ${mod.newAddress}`);
    if (mod.newQuantity) console.log(`🔢 New Quantity: ${mod.newQuantity}`);
  }
  if (snapshot.context.validationError) {
    console.log(`⚠️ Validation Error: ${snapshot.context.validationError}`);
  }
  console.log("");
});

actor3.start();
actor3.send({ type: "START" });
actor3.send({ type: "MODIFY_ORDER" });
actor3.send({ type: "SUBMIT_ORDER_ID", orderId: "ORD_002" }); // Processing order
console.log("📱 User selected order ORD_002 for modification");

// Test invalid address change
actor3.send({ type: "REQUEST_ADDRESS_CHANGE", newAddress: "Short" });
console.log("📱 User tried invalid address change");

// Test valid address change
actor3.send({
  type: "REQUEST_ADDRESS_CHANGE",
  newAddress: "555 Modified Avenue, Lusaka, Zambia",
});
console.log("📱 User requested valid address change");

actor3.send({ type: "CONFIRM_MODIFICATION" });
console.log("📱 User confirmed modification");

setTimeout(() => {
  console.log("✅ Order modification complete!\n");

  // Demo 4: Order Cancellation
  console.log("=".repeat(50));
  console.log("DEMO 4: Order Cancellation");
  console.log("=".repeat(50));

  const actor4 = createActor(orderMachine, { input: mockInput });
  actor4.subscribe(snapshot => {
    console.log(`📍 State: ${snapshot.value}`);
    if (snapshot.context.selectedOrder) {
      console.log(`📦 Order: ${snapshot.context.selectedOrder.productName}`);
      console.log(`📋 Status: ${snapshot.context.selectedOrder.status}`);
    }
    if (snapshot.context.modificationRequest?.reason) {
      console.log(`💭 Reason: ${snapshot.context.modificationRequest.reason}`);
    }
    if (snapshot.context.validationError) {
      console.log(`⚠️ Validation Error: ${snapshot.context.validationError}`);
    }
    console.log("");
  });

  actor4.start();
  actor4.send({ type: "START" });
  actor4.send({ type: "CANCEL_ORDER" });
  actor4.send({ type: "SUBMIT_ORDER_ID", orderId: "ORD_002" });
  console.log("📱 User selected order ORD_002 for cancellation");

  // Test invalid cancellation reason
  actor4.send({ type: "REQUEST_CANCELLATION", reason: "No" });
  console.log("📱 User provided invalid cancellation reason");

  // Test valid cancellation
  actor4.send({
    type: "REQUEST_CANCELLATION",
    reason: "Found a better alternative product",
  });
  console.log("📱 User provided valid cancellation reason");

  actor4.send({ type: "CONFIRM_CANCELLATION" });
  console.log("📱 User confirmed cancellation");

  setTimeout(() => {
    console.log("✅ Order cancellation complete!\n");

    // Demo 5: Delivered Order Modification (Should Fail)
    console.log("=".repeat(50));
    console.log("DEMO 5: Delivered Order Modification (Validation)");
    console.log("=".repeat(50));

    const actor5 = createActor(orderMachine, { input: mockInput });
    actor5.subscribe(snapshot => {
      console.log(`📍 State: ${snapshot.value}`);
      if (snapshot.context.selectedOrder) {
        console.log(`📦 Order: ${snapshot.context.selectedOrder.productName}`);
        console.log(`📋 Status: ${snapshot.context.selectedOrder.status}`);
      }
      if (snapshot.context.validationError) {
        console.log(`⚠️ Validation Error: ${snapshot.context.validationError}`);
      }
      console.log("");
    });

    actor5.start();
    actor5.send({ type: "START" });
    actor5.send({ type: "MODIFY_ORDER" });
    actor5.send({ type: "SUBMIT_ORDER_ID", orderId: "ORD_003" }); // Delivered order
    console.log("📱 User selected delivered order ORD_003");

    actor5.send({
      type: "REQUEST_ADDRESS_CHANGE",
      newAddress: "999 Cannot Change Street, Lusaka, Zambia",
    });
    console.log("📱 User tried to modify delivered order");

    console.log("✅ Validation testing complete!\n");

    // Demo 6: Navigation Flow
    console.log("=".repeat(50));
    console.log("DEMO 6: Navigation Flow");
    console.log("=".repeat(50));

    const actor6 = createActor(orderMachine, { input: mockInput });
    actor6.subscribe(snapshot => {
      console.log(`📍 State: ${snapshot.value}`);
      if (snapshot.output) {
        console.log(`🎯 Output:`, snapshot.output);
      }
      console.log("");
    });

    actor6.start();
    actor6.send({ type: "START" });
    console.log("📱 User started order management");

    actor6.send({ type: "VIEW_ORDERS" });
    console.log("📱 User viewed orders");

    actor6.send({ type: "BACK_TO_MENU" });
    console.log("📱 User navigated back to action selection");

    actor6.send({ type: "BACK_TO_MAIN" });
    console.log("📱 User navigated back to main menu");

    console.log("✅ Navigation flow complete!\n");

    console.log("\n🎉 Order Machine Demo Complete!");
    console.log("\n📊 Machine Summary:");
    console.log("   • Handles order viewing with pagination");
    console.log("   • Supports order tracking by ID");
    console.log("   • Manages order modifications (address, quantity)");
    console.log("   • Processes order cancellation requests");
    console.log("   • Validates modification requests based on order status");
    console.log("   • Provides status refresh functionality");
    console.log("   • Includes comprehensive error handling and validation");
    console.log("   • Supports navigation through order management flows");
    console.log("   • Type-safe with XState v5 setup() pattern");
  }, 1200);
}, 1200);
