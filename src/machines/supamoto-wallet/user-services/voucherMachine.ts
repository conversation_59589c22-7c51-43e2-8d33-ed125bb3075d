import { setup, assign } from "xstate";

/**
 * Voucher Machine - Digital Voucher Management and Redemption
 *
 * Handles:
 * - Digital voucher workflows
 * - Voucher redemption and validation
 * - Voucher balance management
 * - Voucher purchase and distribution
 * - Voucher history and tracking
 *
 * Entry Points: START
 * Exit Points: Outputs routing decision for main orchestrator
 */

export interface VoucherContext {
  sessionId: string;
  phoneNumber: string;
  serviceCode: string;
  walletId?: string;
  currentBalance?: number;
  selectedAction?: "redeem" | "purchase" | "balance" | "history" | "transfer";
  voucherCode?: string;
  voucherAmount?: number;
  voucherType?: "energy" | "product" | "service" | "cash";
  recipientPhone?: string;
  purchaseAmount?: number;
  vouchers?: Array<{
    id: string;
    code: string;
    type: "energy" | "product" | "service" | "cash";
    amount: number;
    status: "active" | "used" | "expired";
    expiryDate: string;
    createdDate: string;
  }>;
  selectedVoucher?: {
    id: string;
    code: string;
    type: "energy" | "product" | "service" | "cash";
    amount: number;
    status: "active" | "used" | "expired";
    expiryDate: string;
    createdDate: string;
  };
  transactionId?: string;
  error?: string;
  validationError?: string;
}

export type VoucherEvent =
  | { type: "START" }
  | { type: "REDEEM_VOUCHER" }
  | { type: "PURCHASE_VOUCHER" }
  | { type: "CHECK_BALANCE" }
  | { type: "VIEW_HISTORY" }
  | { type: "TRANSFER_VOUCHER" }
  | { type: "SUBMIT_VOUCHER_CODE"; code: string }
  | { type: "SELECT_ENERGY_VOUCHER" }
  | { type: "SELECT_PRODUCT_VOUCHER" }
  | { type: "SELECT_SERVICE_VOUCHER" }
  | { type: "SELECT_CASH_VOUCHER" }
  | { type: "SET_PURCHASE_AMOUNT"; amount: number }
  | { type: "SUBMIT_RECIPIENT_PHONE"; phone: string }
  | { type: "CONFIRM_REDEMPTION" }
  | { type: "CONFIRM_PURCHASE" }
  | { type: "CONFIRM_TRANSFER" }
  | { type: "REDEMPTION_SUCCESS"; transactionId: string }
  | { type: "REDEMPTION_FAILED"; error: string }
  | { type: "PURCHASE_SUCCESS"; voucherCode: string; transactionId: string }
  | { type: "PURCHASE_FAILED"; error: string }
  | { type: "TRANSFER_SUCCESS"; transactionId: string }
  | { type: "TRANSFER_FAILED"; error: string }
  | { type: "SELECT_VOUCHER"; voucherId: string }
  | { type: "NEW_VOUCHER_ACTION" }
  | { type: "BACK_TO_ACTIONS" }
  | { type: "BACK_TO_MENU" }
  | { type: "BACK_TO_MAIN" }
  | { type: "ERROR"; error: string };

// Mock voucher data
const MOCK_VOUCHERS = [
  {
    id: "VCH001",
    code: "ENERGY2024001",
    type: "energy" as const,
    amount: 50,
    status: "active" as const,
    expiryDate: "2024-06-30",
    createdDate: "2024-01-15",
  },
  {
    id: "VCH002",
    code: "PRODUCT2024002",
    type: "product" as const,
    amount: 100,
    status: "active" as const,
    expiryDate: "2024-12-31",
    createdDate: "2024-01-10",
  },
  {
    id: "VCH003",
    code: "SERVICE2024003",
    type: "service" as const,
    amount: 25,
    status: "used" as const,
    expiryDate: "2024-03-31",
    createdDate: "2024-01-05",
  },
];

export const voucherMachine = setup({
  types: {
    context: {} as VoucherContext,
    events: {} as VoucherEvent,
    input: {} as {
      sessionId: string;
      phoneNumber: string;
      serviceCode: string;
      walletId?: string;
      currentBalance?: number;
    },
  },

  actions: {
    initializeContext: assign({
      error: undefined,
      validationError: undefined,
    }),

    setSelectedAction: assign({
      selectedAction: ({ event }) => {
        if (event.type === "REDEEM_VOUCHER") return "redeem";
        if (event.type === "PURCHASE_VOUCHER") return "purchase";
        if (event.type === "CHECK_BALANCE") return "balance";
        if (event.type === "VIEW_HISTORY") return "history";
        if (event.type === "TRANSFER_VOUCHER") return "transfer";
        return undefined;
      },
    }),

    setVoucherCode: assign({
      voucherCode: ({ event }) =>
        event.type === "SUBMIT_VOUCHER_CODE" ? event.code : undefined,
    }),

    setVoucherType: assign({
      voucherType: ({ event }) => {
        if (event.type === "SELECT_ENERGY_VOUCHER") return "energy";
        if (event.type === "SELECT_PRODUCT_VOUCHER") return "product";
        if (event.type === "SELECT_SERVICE_VOUCHER") return "service";
        if (event.type === "SELECT_CASH_VOUCHER") return "cash";
        return undefined;
      },
    }),

    setPurchaseAmount: assign({
      purchaseAmount: ({ event }) =>
        event.type === "SET_PURCHASE_AMOUNT" ? event.amount : undefined,
    }),

    setRecipientPhone: assign({
      recipientPhone: ({ event }) =>
        event.type === "SUBMIT_RECIPIENT_PHONE" ? event.phone : undefined,
    }),

    loadVouchers: assign({
      vouchers: () => MOCK_VOUCHERS,
    }),

    setSelectedVoucher: assign({
      selectedVoucher: ({ event, context }) => {
        if (event.type === "SELECT_VOUCHER" && context.vouchers) {
          return context.vouchers.find(v => v.id === event.voucherId);
        }
        return undefined;
      },
    }),

    validateVoucherCode: assign({
      voucherAmount: ({ context }) => {
        // Mock validation - in real app would call API
        if (context.voucherCode === "ENERGY2024001") return 50;
        if (context.voucherCode === "PRODUCT2024002") return 100;
        if (context.voucherCode === "SERVICE2024003") return 25;
        return undefined;
      },
      voucherType: ({ context }) => {
        // Mock type detection
        if (context.voucherCode?.startsWith("ENERGY")) return "energy";
        if (context.voucherCode?.startsWith("PRODUCT")) return "product";
        if (context.voucherCode?.startsWith("SERVICE")) return "service";
        return "cash";
      },
    }),

    processRedemption: assign({
      transactionId: () =>
        `TXN_REDEEM_${Date.now()}_${Math.random().toString(36).substr(2, 6)}`,
      currentBalance: ({ context }) => {
        if (context.voucherType === "cash" && context.voucherAmount) {
          return (context.currentBalance || 0) + context.voucherAmount;
        }
        return context.currentBalance;
      },
    }),

    processPurchase: assign({
      transactionId: () =>
        `TXN_PURCHASE_${Date.now()}_${Math.random().toString(36).substr(2, 6)}`,
      voucherCode: () =>
        `${Date.now().toString().slice(-6)}${Math.random().toString(36).substr(2, 4).toUpperCase()}`,
      currentBalance: ({ context }) => {
        if (context.purchaseAmount) {
          return (context.currentBalance || 0) - context.purchaseAmount;
        }
        return context.currentBalance;
      },
    }),

    processTransfer: assign({
      transactionId: () =>
        `TXN_TRANSFER_${Date.now()}_${Math.random().toString(36).substr(2, 6)}`,
    }),

    resetForNewAction: assign({
      selectedAction: undefined,
      voucherCode: undefined,
      voucherAmount: undefined,
      voucherType: undefined,
      recipientPhone: undefined,
      purchaseAmount: undefined,
      selectedVoucher: undefined,
      transactionId: undefined,
      error: undefined,
      validationError: undefined,
    }),

    setError: assign({
      error: ({ event }) =>
        event.type === "ERROR" ? event.error : "An error occurred",
    }),

    setValidationError: assign({
      validationError: ({ context, event }) => {
        if (
          event.type === "SUBMIT_VOUCHER_CODE" &&
          (!event.code || event.code.trim().length < 5)
        ) {
          return "Please enter a valid voucher code";
        }
        if (
          event.type === "SET_PURCHASE_AMOUNT" &&
          (!event.amount || event.amount <= 0)
        ) {
          return "Please enter a valid purchase amount";
        }
        if (
          event.type === "SET_PURCHASE_AMOUNT" &&
          context.currentBalance &&
          event.amount > context.currentBalance
        ) {
          return "Insufficient balance for voucher purchase";
        }
        if (
          event.type === "SUBMIT_RECIPIENT_PHONE" &&
          (!event.phone || event.phone.length < 10)
        ) {
          return "Please enter a valid phone number";
        }
        if (!context.voucherAmount && context.selectedAction === "redeem") {
          return "Invalid or expired voucher code";
        }
        return undefined;
      },
    }),

    clearErrors: assign({
      error: undefined,
      validationError: undefined,
    }),
  },

  guards: {
    isRedeemAction: ({ context }) => context.selectedAction === "redeem",
    isPurchaseAction: ({ context }) => context.selectedAction === "purchase",
    isBalanceAction: ({ context }) => context.selectedAction === "balance",
    isHistoryAction: ({ context }) => context.selectedAction === "history",
    isTransferAction: ({ context }) => context.selectedAction === "transfer",

    isValidVoucherCode: ({ event, context }) => {
      if (event.type === "SUBMIT_VOUCHER_CODE") {
        // Mock validation - would call API in real app
        return ["ENERGY2024001", "PRODUCT2024002", "SERVICE2024003"].includes(
          event.code
        );
      }
      return Boolean(context.voucherAmount);
    },

    isValidPurchaseAmount: ({ event, context }) => {
      if (event.type === "SET_PURCHASE_AMOUNT") {
        return (
          event.amount > 0 &&
          (!context.currentBalance || event.amount <= context.currentBalance)
        );
      }
      return Boolean(context.purchaseAmount && context.purchaseAmount > 0);
    },

    isValidRecipientPhone: ({ event }) =>
      event.type === "SUBMIT_RECIPIENT_PHONE" &&
      Boolean(event.phone && event.phone.length >= 10),

    hasSufficientBalance: ({ context }) => {
      if (context.purchaseAmount && context.currentBalance) {
        return context.currentBalance >= context.purchaseAmount;
      }
      return true;
    },

    hasVouchers: ({ context }) =>
      Boolean(context.vouchers && context.vouchers.length > 0),
    hasSelectedVoucher: ({ context }) => Boolean(context.selectedVoucher),

    hasError: ({ context }) => Boolean(context.error),
    hasValidationError: ({ context }) => Boolean(context.validationError),
  },
}).createMachine({
  id: "voucherMachine",
  initial: "idle",

  context: ({ input }) => ({
    sessionId: input?.sessionId || "",
    phoneNumber: input?.phoneNumber || "",
    serviceCode: input?.serviceCode || "",
    walletId: input?.walletId,
    currentBalance: input?.currentBalance || 0,
  }),

  states: {
    // Initial state - waiting for start
    idle: {
      on: {
        START: {
          target: "actionSelection",
          actions: "initializeContext",
        },
      },
    },

    // Voucher action selection menu
    actionSelection: {
      entry: "clearErrors",
      on: {
        REDEEM_VOUCHER: {
          target: "voucherCodeEntry",
          actions: "setSelectedAction",
        },
        PURCHASE_VOUCHER: {
          target: "voucherTypeSelection",
          actions: "setSelectedAction",
        },
        CHECK_BALANCE: {
          target: "loadingVouchers",
          actions: "setSelectedAction",
        },
        VIEW_HISTORY: {
          target: "loadingVouchers",
          actions: "setSelectedAction",
        },
        TRANSFER_VOUCHER: {
          target: "loadingVouchers",
          actions: "setSelectedAction",
        },
        BACK_TO_MENU: "routeToUserServices",
        BACK_TO_MAIN: "routeToMain",
        ERROR: {
          target: "error",
          actions: "setError",
        },
      },
    },

    // Voucher code entry for redemption
    voucherCodeEntry: {
      on: {
        SUBMIT_VOUCHER_CODE: [
          {
            target: "redemptionConfirmation",
            actions: ["setVoucherCode", "validateVoucherCode"],
            guard: "isValidVoucherCode",
          },
          {
            target: "voucherCodeEntry",
            actions: "setValidationError",
          },
        ],
        BACK_TO_ACTIONS: "actionSelection",
        BACK_TO_MAIN: "routeToMain",
        ERROR: {
          target: "error",
          actions: "setError",
        },
      },
    },

    // Voucher type selection for purchase
    voucherTypeSelection: {
      on: {
        SELECT_ENERGY_VOUCHER: {
          target: "purchaseAmountEntry",
          actions: "setVoucherType",
        },
        SELECT_PRODUCT_VOUCHER: {
          target: "purchaseAmountEntry",
          actions: "setVoucherType",
        },
        SELECT_SERVICE_VOUCHER: {
          target: "purchaseAmountEntry",
          actions: "setVoucherType",
        },
        SELECT_CASH_VOUCHER: {
          target: "purchaseAmountEntry",
          actions: "setVoucherType",
        },
        BACK_TO_ACTIONS: "actionSelection",
        BACK_TO_MAIN: "routeToMain",
        ERROR: {
          target: "error",
          actions: "setError",
        },
      },
    },

    // Purchase amount entry
    purchaseAmountEntry: {
      on: {
        SET_PURCHASE_AMOUNT: [
          {
            target: "purchaseConfirmation",
            actions: "setPurchaseAmount",
            guard: "isValidPurchaseAmount",
          },
          {
            target: "purchaseAmountEntry",
            actions: "setValidationError",
          },
        ],
        BACK_TO_ACTIONS: "voucherTypeSelection",
        BACK_TO_MAIN: "routeToMain",
        ERROR: {
          target: "error",
          actions: "setError",
        },
      },
    },

    // Loading vouchers for balance/history/transfer
    loadingVouchers: {
      entry: "loadVouchers",
      always: [
        {
          target: "displayingVouchers",
          guard: "hasVouchers",
        },
        {
          target: "noVouchers",
        },
      ],
    },

    // Displaying vouchers
    displayingVouchers: {
      on: {
        SELECT_VOUCHER: {
          target: "voucherDetails",
          actions: "setSelectedVoucher",
        },
        BACK_TO_ACTIONS: "actionSelection",
        BACK_TO_MAIN: "routeToMain",
        ERROR: {
          target: "error",
          actions: "setError",
        },
      },
    },

    // No vouchers available
    noVouchers: {
      on: {
        BACK_TO_ACTIONS: "actionSelection",
        BACK_TO_MAIN: "routeToMain",
      },
    },

    // Voucher details view
    voucherDetails: {
      on: {
        SUBMIT_RECIPIENT_PHONE: [
          {
            target: "transferConfirmation",
            actions: "setRecipientPhone",
            guard: "isValidRecipientPhone",
          },
          {
            target: "voucherDetails",
            actions: "setValidationError",
          },
        ],
        BACK_TO_ACTIONS: "displayingVouchers",
        BACK_TO_MAIN: "routeToMain",
        ERROR: {
          target: "error",
          actions: "setError",
        },
      },
    },

    // Redemption confirmation
    redemptionConfirmation: {
      on: {
        CONFIRM_REDEMPTION: "processingRedemption",
        BACK_TO_ACTIONS: "voucherCodeEntry",
        BACK_TO_MAIN: "routeToMain",
        ERROR: {
          target: "error",
          actions: "setError",
        },
      },
    },

    // Purchase confirmation
    purchaseConfirmation: {
      on: {
        CONFIRM_PURCHASE: "processingPurchase",
        BACK_TO_ACTIONS: "purchaseAmountEntry",
        BACK_TO_MAIN: "routeToMain",
        ERROR: {
          target: "error",
          actions: "setError",
        },
      },
    },

    // Transfer confirmation
    transferConfirmation: {
      on: {
        CONFIRM_TRANSFER: "processingTransfer",
        BACK_TO_ACTIONS: "voucherDetails",
        BACK_TO_MAIN: "routeToMain",
        ERROR: {
          target: "error",
          actions: "setError",
        },
      },
    },

    // Processing redemption
    processingRedemption: {
      entry: "processRedemption",
      after: {
        1000: "redemptionSuccess", // Simulate processing time
      },
      on: {
        REDEMPTION_FAILED: {
          target: "redemptionFailed",
          actions: "setError",
        },
      },
    },

    // Processing purchase
    processingPurchase: {
      entry: "processPurchase",
      after: {
        1000: "purchaseSuccess", // Simulate processing time
      },
      on: {
        PURCHASE_FAILED: {
          target: "purchaseFailed",
          actions: "setError",
        },
      },
    },

    // Processing transfer
    processingTransfer: {
      entry: "processTransfer",
      after: {
        1000: "transferSuccess", // Simulate processing time
      },
      on: {
        TRANSFER_FAILED: {
          target: "transferFailed",
          actions: "setError",
        },
      },
    },

    // Redemption successful
    redemptionSuccess: {
      on: {
        NEW_VOUCHER_ACTION: {
          target: "actionSelection",
          actions: "resetForNewAction",
        },
        BACK_TO_MENU: "routeToUserServices",
        BACK_TO_MAIN: "routeToMain",
      },
    },

    // Purchase successful
    purchaseSuccess: {
      on: {
        NEW_VOUCHER_ACTION: {
          target: "actionSelection",
          actions: "resetForNewAction",
        },
        BACK_TO_MENU: "routeToUserServices",
        BACK_TO_MAIN: "routeToMain",
      },
    },

    // Transfer successful
    transferSuccess: {
      on: {
        NEW_VOUCHER_ACTION: {
          target: "actionSelection",
          actions: "resetForNewAction",
        },
        BACK_TO_MENU: "routeToUserServices",
        BACK_TO_MAIN: "routeToMain",
      },
    },

    // Redemption failed
    redemptionFailed: {
      on: {
        NEW_VOUCHER_ACTION: {
          target: "actionSelection",
          actions: "resetForNewAction",
        },
        BACK_TO_MENU: "routeToUserServices",
        BACK_TO_MAIN: "routeToMain",
      },
    },

    // Purchase failed
    purchaseFailed: {
      on: {
        NEW_VOUCHER_ACTION: {
          target: "actionSelection",
          actions: "resetForNewAction",
        },
        BACK_TO_MENU: "routeToUserServices",
        BACK_TO_MAIN: "routeToMain",
      },
    },

    // Transfer failed
    transferFailed: {
      on: {
        NEW_VOUCHER_ACTION: {
          target: "actionSelection",
          actions: "resetForNewAction",
        },
        BACK_TO_MENU: "routeToUserServices",
        BACK_TO_MAIN: "routeToMain",
      },
    },

    // Error state
    error: {
      entry: "setError",
      on: {
        START: {
          target: "actionSelection",
          actions: "clearErrors",
        },
        BACK_TO_MENU: "routeToUserServices",
        BACK_TO_MAIN: "routeToMain",
      },
    },

    // Route back to user services
    routeToUserServices: {
      type: "final",
      output: {
        route: "userServices" as const,
        context: ({ context }: { context: VoucherContext }) => context,
      },
    },

    // Route back to main menu
    routeToMain: {
      type: "final",
      output: {
        route: "main" as const,
        context: ({ context }: { context: VoucherContext }) => context,
      },
    },
  },
});

export type VoucherMachine = typeof voucherMachine;
