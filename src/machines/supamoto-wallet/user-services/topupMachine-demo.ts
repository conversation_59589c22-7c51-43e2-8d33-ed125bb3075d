/* eslint-disable no-console */
import { createActor } from "xstate";
import { topupMachine } from "./topupMachine.js";

/**
 * Top-up Machine Demo
 *
 * Demonstrates the top-up machine functionality including:
 * - Amount selection and validation
 * - Payment method selection
 * - Mobile money number entry
 * - Payment processing flow
 * - Success and failure handling
 * - Receipt generation
 */

console.log("🚀 Top-up Machine Demo\n");

const mockInput = {
  sessionId: "demo-session-topup-123",
  phoneNumber: "+260987654321",
  serviceCode: "*2233#",
  walletId: "*********",
  currentBalance: 100,
};

// Demo 1: Successful Mobile Money Top-up
console.log("=".repeat(50));
console.log("DEMO 1: Successful Mobile Money Top-up");
console.log("=".repeat(50));

const actor1 = createActor(topupMachine, { input: mockInput });
actor1.subscribe(snapshot => {
  console.log(`📍 State: ${snapshot.value}`);
  if (snapshot.context.topupAmount) {
    console.log(`💰 Top-up Amount: K${snapshot.context.topupAmount}`);
  }
  if (snapshot.context.paymentMethod) {
    console.log(`💳 Payment Method: ${snapshot.context.paymentMethod}`);
  }
  if (snapshot.context.mobileMoneyNumber) {
    console.log(`📱 Mobile Number: ${snapshot.context.mobileMoneyNumber}`);
  }
  if (snapshot.context.transactionStatus) {
    console.log(`🔄 Transaction Status: ${snapshot.context.transactionStatus}`);
  }
  if (snapshot.context.transactionId) {
    console.log(`🆔 Transaction ID: ${snapshot.context.transactionId}`);
  }
  if (snapshot.context.receiptNumber) {
    console.log(`🧾 Receipt Number: ${snapshot.context.receiptNumber}`);
  }
  if (snapshot.context.currentBalance) {
    console.log(`💵 Current Balance: K${snapshot.context.currentBalance}`);
  }
  if (snapshot.output) {
    console.log(`🎯 Output:`, snapshot.output);
  }
  console.log("");
});

actor1.start();
actor1.send({ type: "START" });
console.log("📱 User started top-up flow");

actor1.send({ type: "SELECT_AMOUNT", amount: 50 });
console.log("📱 User selected K50 top-up amount");

actor1.send({ type: "SELECT_MOBILE_MONEY" });
console.log("📱 User selected mobile money payment");

actor1.send({ type: "SUBMIT_MOBILE_NUMBER", number: "+260987654321" });
console.log("📱 User entered mobile money number");

actor1.send({ type: "CONFIRM_PAYMENT" });
console.log("📱 User confirmed payment");

// Simulate successful payment
setTimeout(() => {
  actor1.send({
    type: "PAYMENT_SUCCESS",
    transactionId: "TXN_MM_789123",
    receiptNumber: "RCP_456789",
  });
  console.log("✅ Payment processed successfully");
}, 1000);

setTimeout(() => {
  actor1.send({ type: "VIEW_RECEIPT" });
  console.log("📱 User viewed receipt");
  console.log("✅ Mobile money top-up complete!\n");

  // Demo 2: Card Payment Top-up
  console.log("=".repeat(50));
  console.log("DEMO 2: Card Payment Top-up");
  console.log("=".repeat(50));

  const actor2 = createActor(topupMachine, { input: mockInput });
  actor2.subscribe(snapshot => {
    console.log(`📍 State: ${snapshot.value}`);
    if (snapshot.context.topupAmount) {
      console.log(`💰 Amount: K${snapshot.context.topupAmount}`);
    }
    if (snapshot.context.paymentMethod) {
      console.log(`💳 Method: ${snapshot.context.paymentMethod}`);
    }
    console.log("");
  });

  actor2.start();
  actor2.send({ type: "START" });
  actor2.send({ type: "CUSTOM_AMOUNT", amount: 100 });
  console.log("📱 User entered custom amount K100");

  actor2.send({ type: "SELECT_CARD" });
  console.log("📱 User selected card payment");

  actor2.send({ type: "CONFIRM_PAYMENT" });
  console.log("📱 User confirmed card payment");

  // Simulate successful payment
  setTimeout(() => {
    actor2.send({
      type: "PAYMENT_SUCCESS",
      transactionId: "TXN_CARD_456",
      receiptNumber: "RCP_CARD_789",
    });
    console.log("✅ Card payment successful!\n");

    // Demo 3: Failed Payment with Retry
    console.log("=".repeat(50));
    console.log("DEMO 3: Failed Payment with Retry");
    console.log("=".repeat(50));

    const actor3 = createActor(topupMachine, { input: mockInput });
    actor3.subscribe(snapshot => {
      console.log(`📍 State: ${snapshot.value}`);
      if (snapshot.context.error) {
        console.log(`❌ Error: ${snapshot.context.error}`);
      }
      console.log("");
    });

    actor3.start();
    actor3.send({ type: "START" });
    actor3.send({ type: "SELECT_AMOUNT", amount: 25 });
    actor3.send({ type: "SELECT_BANK_TRANSFER" });
    actor3.send({ type: "CONFIRM_PAYMENT" });
    console.log("📱 User confirmed bank transfer payment");

    // Simulate failed payment
    setTimeout(() => {
      actor3.send({
        type: "PAYMENT_FAILED",
        error: "Insufficient bank balance",
      });
      console.log("❌ Payment failed");

      actor3.send({ type: "RETRY_PAYMENT" });
      console.log("📱 User chose to retry payment");

      // Simulate successful retry
      setTimeout(() => {
        actor3.send({ type: "CONFIRM_PAYMENT" });
        actor3.send({
          type: "PAYMENT_SUCCESS",
          transactionId: "TXN_RETRY_123",
          receiptNumber: "RCP_RETRY_456",
        });
        console.log("✅ Retry payment successful!\n");

        // Demo 4: Validation Errors
        console.log("=".repeat(50));
        console.log("DEMO 4: Validation Errors");
        console.log("=".repeat(50));

        const actor4 = createActor(topupMachine, { input: mockInput });
        actor4.subscribe(snapshot => {
          console.log(`📍 State: ${snapshot.value}`);
          if (snapshot.context.validationError) {
            console.log(
              `⚠️ Validation Error: ${snapshot.context.validationError}`
            );
          }
          console.log("");
        });

        actor4.start();
        actor4.send({ type: "START" });

        // Test minimum amount validation
        actor4.send({ type: "CUSTOM_AMOUNT", amount: 5 });
        console.log("📱 User entered amount below minimum (K5)");

        // Test maximum amount validation
        actor4.send({ type: "CUSTOM_AMOUNT", amount: 15000 });
        console.log("📱 User entered amount above maximum (K15,000)");

        // Test valid amount
        actor4.send({ type: "CUSTOM_AMOUNT", amount: 75 });
        console.log("📱 User entered valid amount (K75)");

        console.log("✅ Validation testing complete!\n");

        // Demo 5: Navigation Flow
        console.log("=".repeat(50));
        console.log("DEMO 5: Navigation Flow");
        console.log("=".repeat(50));

        const actor5 = createActor(topupMachine, { input: mockInput });
        actor5.subscribe(snapshot => {
          console.log(`📍 State: ${snapshot.value}`);
          if (snapshot.output) {
            console.log(`🎯 Output:`, snapshot.output);
          }
          console.log("");
        });

        actor5.start();
        actor5.send({ type: "START" });
        console.log("📱 User started top-up flow");

        actor5.send({ type: "SELECT_AMOUNT", amount: 30 });
        console.log("📱 User selected amount");

        actor5.send({ type: "BACK_TO_MENU" });
        console.log("📱 User navigated back to amount selection");

        actor5.send({ type: "BACK_TO_MAIN" });
        console.log("📱 User navigated back to main menu");

        console.log("✅ Navigation flow complete!\n");

        console.log("\n🎉 Top-up Machine Demo Complete!");
        console.log("\n📊 Machine Summary:");
        console.log("   • Handles balance top-up workflows");
        console.log(
          "   • Supports multiple payment methods (mobile money, card, bank, agent)"
        );
        console.log("   • Validates amounts and mobile numbers");
        console.log("   • Processes payments with success/failure handling");
        console.log("   • Generates transaction IDs and receipts");
        console.log("   • Updates wallet balance on successful payment");
        console.log("   • Provides retry functionality for failed payments");
        console.log("   • Includes comprehensive error handling");
        console.log("   • Type-safe with XState v5 setup() pattern");
      }, 1000);
    }, 1000);
  }, 1000);
}, 2000);
