/**
 * SupaMoto Guards for SupaMoto Wallet Machine
 *
 * This module contains guards related to SupaMoto-specific business logic.
 * These guards handle SupaMoto wallet verification, agent operations, and order management.
 */

import { guardLimits } from "../../../config/index.js";
import type { CombinedGuard } from "../types.js";

// =================================================================================================
// SUPAMOTO WALLET GUARDS
// =================================================================================================

/**
 * Checks if SupaMoto wallet ID is verified
 */
// eslint-disable-next-line @typescript-eslint/no-unused-vars
export const isWalletIdVerified: CombinedGuard = (context, event) => {
  return context.supamoto.walletIdVerified === true;
};

/**
 * Checks if user has sufficient balance for top-up
 */
// eslint-disable-next-line @typescript-eslint/no-unused-vars
export const hasSufficientBalance: CombinedGuard = (context, event) => {
  const amount = parseFloat(context.supamoto.topUpAmount || "0");
  const balance = parseFloat(context.supamoto.balance || "0");
  return balance >= amount;
};

// =================================================================================================
// SUPAMOTO AGENT GUARDS
// =================================================================================================

/**
 * Checks if agent is logged in
 */
// eslint-disable-next-line @typescript-eslint/no-unused-vars
export const isAgentLoggedIn: CombinedGuard = (context, event) => {
  return context.userState.isAgentLoggedIn === true;
};

/**
 * Verifies agent PIN
 */
// eslint-disable-next-line @typescript-eslint/no-unused-vars
export const isAgentPinCorrect: CombinedGuard = (context, event) => {
  if (event.type !== "INPUT") return false;
  // TODO: Replace with actual agent PIN verification from config
  return event.input === guardLimits.pin.agentTestPin;
};

/**
 * Verifies SupaMoto PIN (placeholder implementation)
 */
// eslint-disable-next-line @typescript-eslint/no-unused-vars
export const isSupamotoPinCorrect: CombinedGuard = (context, event) => {
  if (event.type !== "INPUT") return false;
  // TODO: Replace with actual PIN verification
  return event.input === guardLimits.pin.testPin;
};

// =================================================================================================
// SUPAMOTO LOCATION GUARDS
// =================================================================================================

/**
 * Checks if user has selected a province/location
 */
// eslint-disable-next-line @typescript-eslint/no-unused-vars
export const hasSelectedProvince: CombinedGuard = (context, event) => {
  return !!context.supamoto.selectedProvince;
};

/**
 * Checks if user has selected an area
 */
// eslint-disable-next-line @typescript-eslint/no-unused-vars
export const hasSelectedArea: CombinedGuard = (context, event) => {
  return !!context.supamoto.selectedArea;
};

/**
 * Checks if user has selected a district
 */
// eslint-disable-next-line @typescript-eslint/no-unused-vars
export const hasSelectedDistrict: CombinedGuard = (context, event) => {
  return !!context.supamoto.selectedDistrict;
};

// =================================================================================================
// SUPAMOTO ORDER GUARDS
// =================================================================================================

/**
 * Checks if user has an active order
 */
// eslint-disable-next-line @typescript-eslint/no-unused-vars
export const hasActiveOrder: CombinedGuard = (context, event) => {
  return !!(
    context.supamoto.orderId && context.supamoto.orderStatus !== "completed"
  );
};

/**
 * Checks if user has a completed order
 */
// eslint-disable-next-line @typescript-eslint/no-unused-vars
export const hasCompletedOrder: CombinedGuard = (context, event) => {
  return !!(
    context.supamoto.orderId && context.supamoto.orderStatus === "completed"
  );
};

/**
 * Checks if user has an order in progress
 */
// eslint-disable-next-line @typescript-eslint/no-unused-vars
export const hasOrderInProgress: CombinedGuard = (context, event) => {
  return !!(
    context.supamoto.orderId &&
    context.supamoto.orderStatus &&
    context.supamoto.orderStatus !== "completed" &&
    context.supamoto.orderStatus !== "cancelled"
  );
};

// =================================================================================================
// SUPAMOTO PRODUCT GUARDS
// =================================================================================================

/**
 * Checks if user has selected a contract
 */
// eslint-disable-next-line @typescript-eslint/no-unused-vars
export const hasSelectedContract: CombinedGuard = (context, event) => {
  return !!context.supamoto.selectedContract;
};

/**
 * Checks if user has selected a bag
 */
// eslint-disable-next-line @typescript-eslint/no-unused-vars
export const hasSelectedBag: CombinedGuard = (context, event) => {
  return !!context.supamoto.selectedBag;
};

/**
 * Checks if user has selected an accessory
 */
// eslint-disable-next-line @typescript-eslint/no-unused-vars
export const hasSelectedAccessory: CombinedGuard = (context, event) => {
  return !!context.supamoto.selectedAccessory;
};

/**
 * Checks if user has selected a voucher
 */
// eslint-disable-next-line @typescript-eslint/no-unused-vars
export const hasSelectedVoucher: CombinedGuard = (context, event) => {
  return !!context.supamoto.selectedVoucher;
};

/**
 * Checks if user has available contracts
 */
// eslint-disable-next-line @typescript-eslint/no-unused-vars
export const hasAvailableContracts: CombinedGuard = (context, event) => {
  return !!(
    context.supamoto.contracts && context.supamoto.contracts.length > 0
  );
};

// =================================================================================================
// SUPAMOTO PAYMENT GUARDS
// =================================================================================================

/**
 * Checks if payment method is selected
 */
// eslint-disable-next-line @typescript-eslint/no-unused-vars
export const hasSelectedPaymentMethod: CombinedGuard = (context, event) => {
  return !!context.supamoto.paymentMethod;
};

/**
 * Checks if top-up amount is valid
 */
// eslint-disable-next-line @typescript-eslint/no-unused-vars
export const hasValidTopUpAmount: CombinedGuard = (context, event) => {
  const amount = parseFloat(context.supamoto.topUpAmount || "0");
  return amount > 0 && amount <= guardLimits.transaction.dailyLimit;
};

/**
 * Checks if mobile money number is provided
 */
// eslint-disable-next-line @typescript-eslint/no-unused-vars
export const hasMobileMoneyNumber: CombinedGuard = (context, event) => {
  return !!context.supamoto.mobileMoneyNumber;
};

// =================================================================================================
// SUPAMOTO GUARD COLLECTION
// =================================================================================================

/**
 * Collection of all SupaMoto guards for easy access
 */
export const supamoToGuards = {
  isWalletIdVerified,
  hasSufficientBalance,
  isAgentLoggedIn,
  isAgentPinCorrect,
  isSupamotoPinCorrect,
  hasSelectedProvince,
  hasSelectedArea,
  hasSelectedDistrict,
  hasActiveOrder,
  hasCompletedOrder,
  hasOrderInProgress,
  hasSelectedContract,
  hasSelectedBag,
  hasSelectedAccessory,
  hasSelectedVoucher,
  hasAvailableContracts,
  hasSelectedPaymentMethod,
  hasValidTopUpAmount,
  hasMobileMoneyNumber,
} as const;
