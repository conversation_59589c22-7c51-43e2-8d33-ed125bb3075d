import { setup, assign } from "xstate";

/**
 * [Machine Name] Machine - [Brief Description]
 *
 * Handles:
 * - [Responsibility 1]
 * - [Responsibility 2]
 * - [Responsibility 3]
 *
 * Entry Points: [EVENT_NAME]
 * Exit Points: [Description of outputs]
 */

export interface MachineNameContext {
  sessionId: string;
  phoneNumber: string;
  // Add your context fields here
  field1: string;
  field2: boolean;
  field3?: number;
}

export type MachineNameEvent =
  | { type: "START" }
  | { type: "SELECT_1" }
  | { type: "SELECT_2" }
  | { type: "SUBMIT_DATA"; data: string }
  | { type: "BACK" }
  | { type: "SUCCESS" }
  | { type: "ERROR"; error: string };

export const machineNameMachine = setup({
  types: {
    context: {} as MachineNameContext,
    events: {} as MachineNameEvent,
    input: {} as {
      sessionId: string;
      phoneNumber: string;
      // Add required input fields
    },
  },

  actions: {
    initializeContext: assign({
      field1: "defaultValue",
      field2: false,
    }),

    updateField: assign({
      field1: ({ event }) => (event.type === "SUBMIT_DATA" ? event.data : ""),
    }),

    handleSuccess: assign({
      field2: true,
    }),

    handleError: assign({
      field2: false,
    }),
  },

  guards: {
    isValidInput: ({ context }) =>
      Boolean(context.field1 && context.field1.length > 0),

    canProceed: ({ context }) => context.field2 === true,

    isBackNavigation: ({ event }) => event.type === "BACK",
  },
}).createMachine({
  id: "machineNameMachine",
  initial: "idle",

  context: ({ input }) => ({
    sessionId: input?.sessionId || "",
    phoneNumber: input?.phoneNumber || "",
    field1: "",
    field2: false,
  }),

  states: {
    // Initial state
    idle: {
      on: {
        START: {
          target: "active",
          actions: "initializeContext",
        },
      },
    },

    // Main active state
    active: {
      on: {
        SELECT_1: "processing",
        SELECT_2: "alternative",
        BACK: "completed",
      },
    },

    // Processing state
    processing: {
      on: {
        SUBMIT_DATA: {
          target: "validating",
          actions: "updateField",
        },
        BACK: "active",
      },
    },

    // Validation state
    validating: {
      on: {
        SUCCESS: {
          guard: "isValidInput",
          target: "success",
          actions: "handleSuccess",
        },
        ERROR: {
          target: "error",
          actions: "handleError",
        },
      },
    },

    // Alternative flow
    alternative: {
      on: {
        SUCCESS: "success",
        BACK: "active",
      },
    },

    // Success state
    success: {
      type: "final",
      output: {
        route: "success" as const,
        context: ({ context }: { context: MachineNameContext }) => context,
      },
    },

    // Error state
    error: {
      on: {
        START: "active", // Allow retry
        BACK: "active",
      },
    },

    // Completion state
    completed: {
      type: "final",
      output: {
        route: "completed" as const,
        context: ({ context }: { context: MachineNameContext }) => context,
      },
    },
  },
});

export type MachineNameMachine = typeof machineNameMachine;

// TODO: Remember to:
// 1. Replace all instances of "MachineName" with your actual machine name
// 2. Update the context interface with your specific fields
// 3. Define your specific events
// 4. Implement your business logic in actions and guards
// 5. Create corresponding test file: machineNameMachine.test.ts
// 6. Add exports to index.ts
// 7. Run: pnpm tsc --noEmit && pnpm lint
// 8. Delete this TODO section when done
