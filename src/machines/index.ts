// Main orchestrator machine
import { supamotoWalletMachine } from "./supamoto-wallet/supamotoWalletMachine.js";

// Core machines
import { welcomeMachine } from "./supamoto-wallet/core/welcomeMachine.js";

// Information machines
import { knowMoreMachine } from "./supamoto-wallet/information/knowMoreMachine.js";

// User service machines
import { accountMachine } from "./supamoto-wallet/user-services/accountMachine.js";
import { faultMachine } from "./supamoto-wallet/user-services/faultMachine.js";
import { orderMachine } from "./supamoto-wallet/user-services/orderMachine.js";
import { performanceMachine } from "./supamoto-wallet/user-services/performanceMachine.js";
import { purchaseMachine } from "./supamoto-wallet/user-services/purchaseMachine.js";
import { topupMachine } from "./supamoto-wallet/user-services/topupMachine.js";
import { userServicesMachine } from "./supamoto-wallet/user-services/userServicesMachine.js";
import { voucherMachine } from "./supamoto-wallet/user-services/voucherMachine.js";

// Agent machines
import { agentMachine } from "./supamoto-wallet/agent/agentMachine.js";

export type StateMachine = typeof supamotoWalletMachine;

export function getStateMachine(): StateMachine {
  return supamotoWalletMachine;
}

// Export all machines
export {
  supamotoWalletMachine,
  welcomeMachine,
  knowMoreMachine,
  accountMachine,
  faultMachine,
  orderMachine,
  performanceMachine,
  purchaseMachine,
  topupMachine,
  userServicesMachine,
  voucherMachine,
  agentMachine,
};
