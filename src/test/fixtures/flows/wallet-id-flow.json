{"flowName": "wallet-id-flow", "timestamp": "2024-01-15T11:00:00.000Z", "sessionId": "test-session-002", "phoneNumber": "+265888987654", "serviceCode": "*2233#", "turns": [{"textSent": "", "serverReply": "Welcome to IXO USSD\n\n1. <PERSON> More\n2. Enter Wallet ID\n3. Agent <PERSON><PERSON>\n\n0. Exit", "sessionId": "test-session-002", "timestamp": "2024-01-15T11:00:00.000Z"}, {"textSent": "2", "serverReply": "Enter your Wallet ID:\n\n00. Back to Main Menu\n0. Exit", "sessionId": "test-session-002", "timestamp": "2024-01-15T11:00:15.000Z"}, {"textSent": "C21009802", "serverReply": "Wallet ID: C21009802\n\nSelect an option:\n1. Check Balance\n2. Transaction History\n\n00. Back to Main Menu\n0. Exit", "sessionId": "test-session-002", "timestamp": "2024-01-15T11:00:30.000Z"}, {"textSent": "1", "serverReply": "Your current balance is 150.00 IXO tokens.\n\n00. Back to Main Menu\n0. Exit", "sessionId": "test-session-002", "timestamp": "2024-01-15T11:00:45.000Z"}, {"textSent": "0", "serverReply": "Thank you for using IXO USSD. Goodbye!", "sessionId": "test-session-002", "timestamp": "2024-01-15T11:01:00.000Z"}]}