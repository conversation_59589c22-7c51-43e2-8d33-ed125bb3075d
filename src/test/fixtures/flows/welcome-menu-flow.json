{"flowName": "welcome-menu-flow", "timestamp": "2024-01-15T10:30:00.000Z", "sessionId": "test-session-001", "phoneNumber": "+265888123456", "serviceCode": "*2233#", "turns": [{"textSent": "", "serverReply": "Welcome to IXO USSD\n\n1. <PERSON> More\n2. Enter Wallet ID\n3. Agent <PERSON><PERSON>\n\n0. Exit", "sessionId": "test-session-001", "timestamp": "2024-01-15T10:30:00.000Z"}, {"textSent": "1", "serverReply": "Know More about IXO\n\nIXO is a blockchain platform for impact measurement and verification.\n\n00. Back to Main Menu\n0. Exit", "sessionId": "test-session-001", "timestamp": "2024-01-15T10:30:15.000Z"}, {"textSent": "00", "serverReply": "Welcome to IXO USSD\n\n1. <PERSON> More\n2. Enter Wallet ID\n3. Agent <PERSON><PERSON>\n\n0. Exit", "sessionId": "test-session-001", "timestamp": "2024-01-15T10:30:30.000Z"}, {"textSent": "0", "serverReply": "Thank you for using IXO USSD. Goodbye!", "sessionId": "test-session-001", "timestamp": "2024-01-15T10:30:45.000Z"}]}