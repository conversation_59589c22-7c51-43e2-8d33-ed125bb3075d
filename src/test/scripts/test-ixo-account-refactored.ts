#!/usr/bin/env node
/**
 * Test Script for Refactored IXO Account Service
 *
 * Tests the new modular IXO account creation service with enhanced features:
 * - Individual module testing
 * - Full integration testing
 * - Performance comparison
 * - Configuration validation
 *
 * Usage:
 *   # Test full account creation
 *   pnpm exec ts-node src/test/scripts/test-ixo-account-refactored.ts
 *
 *   # Test individual modules
 *   pnpm exec ts-node src/test/scripts/test-ixo-account-refactored.ts --modules
 *
 *   # Test with custom user
 *   pnpm exec ts-node src/test/scripts/test-ixo-account-refactored.ts <EMAIL> 5678
 */

import dotenv from "dotenv";
import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";

// Load environment variables
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const envPath = path.resolve(__dirname, "../../../.env");

if (fs.existsSync(envPath)) {
  dotenv.config({ path: envPath });
  console.log("✅ Loaded .env file from", envPath);
} else {
  console.warn("⚠️  No .env file found at", envPath);
  dotenv.config();
}

// Import our refactored modules
import { getIxoConfig } from "../../services/ixo/config.js";
import {
  generateWallet,
  setupFeegrant,
  createDid,
} from "../../services/ixo/wallet-and-did.js";
import { handleMatrixOnboarding } from "../../services/ixo/matrix-storage.js";
import { createIxoAccount } from "../../services/ixo/ixo-account.js";

// Parse command line arguments
const args = process.argv.slice(2);
const testModules = args.includes("--modules");
const userId =
  args.find(arg => !arg.startsWith("--")) ||
  `test-user-${Date.now()}@example.com`;
const pin = args[1] || "1234";

/**
 * Test configuration validation
 */
async function testConfiguration() {
  console.log("\n🔧 Testing Configuration...");

  try {
    const config = getIxoConfig();

    console.log("✅ Configuration validation passed");
    console.log("📋 Config summary:");
    console.log(`   Chain RPC: ${config.chainRpcUrl}`);
    console.log(`   Feegrant URL: ${config.feegrantUrl}`);
    console.log(`   Matrix Server: ${config.matrixHomeserverUrl}`);
    console.log(`   Room Bot: ${config.roomBotUrl}`);

    return config;
  } catch (error) {
    console.error("❌ Configuration validation failed:");
    console.error(
      `   ${error instanceof Error ? error.message : String(error)}`
    );
    throw error;
  }
}

/**
 * Test individual modules
 */
async function testIndividualModules(config: any) {
  console.log("\n🧩 Testing Individual Modules...");

  // Test 1: Wallet Generation
  console.log("\n1️⃣ Testing wallet generation...");
  const startWallet = Date.now();
  const wallet = await generateWallet();
  const walletDuration = Date.now() - startWallet;

  console.log(`✅ Wallet generated in ${walletDuration}ms`);
  console.log(`   Address: ${wallet.address}`);
  console.log(`   Mnemonic words: ${wallet.mnemonic.split(" ").length}`);

  // Test 2: Fee Grant Setup
  console.log("\n2️⃣ Testing fee grant setup...");
  const startFeegrant = Date.now();
  await setupFeegrant(wallet.address, config);
  const feegrantDuration = Date.now() - startFeegrant;

  console.log(`✅ Fee grant setup completed in ${feegrantDuration}ms`);

  // Test 3: DID Creation
  console.log("\n3️⃣ Testing DID creation...");
  const startDid = Date.now();
  const did = await createDid(
    wallet.address,
    wallet.wallet,
    config.chainRpcUrl,
    config.feegrantGranter
  );
  const didDuration = Date.now() - startDid;

  console.log(`✅ DID created in ${didDuration}ms`);
  console.log(`   DID: ${did}`);

  // Test 4: Matrix Storage (optional - may fail in test environment)
  console.log("\n4️⃣ Testing Matrix storage...");
  try {
    const startMatrix = Date.now();
    const matrixResult = await handleMatrixOnboarding({
      address: wallet.address,
      did,
      wallet: wallet.wallet,
      config: {
        matrixHomeserverUrl: config.matrixHomeserverUrl,
        roomBotUrl: config.roomBotUrl,
      },
      pin,
    });
    const matrixDuration = Date.now() - startMatrix;

    console.log(`✅ Matrix onboarding completed in ${matrixDuration}ms`);
    console.log(`   Status: ${matrixResult.status}`);
    console.log(`   Matrix User: ${matrixResult.matrixUserId}`);
  } catch (error) {
    console.log(`⚠️  Matrix onboarding failed (expected in test environment)`);
    console.log(
      `   Error: ${error instanceof Error ? error.message : String(error)}`
    );
  }

  return {
    wallet,
    did,
    timings: {
      wallet: walletDuration,
      feegrant: feegrantDuration,
      did: didDuration,
    },
  };
}

/**
 * Test full integration
 */
async function testFullIntegration() {
  console.log("\n🔗 Testing Full Integration...");

  const startTime = Date.now();

  try {
    const account = await createIxoAccount({
      userId,
      pin,
      lastMenuLocation: "test-script",
      lastCompletedAction: "integration-test",
    });

    const duration = Date.now() - startTime;

    console.log(`✅ Full account creation completed in ${duration}ms`);
    console.log("\n📋 Account Details:");
    console.log("─".repeat(50));
    console.log(`User ID: ${account.userId}`);
    console.log(`Address: ${account.address}`);
    console.log(`DID: ${account.did}`);
    console.log(`Mnemonic: ${account.mnemonic.split(" ").length} words`);
    console.log("─".repeat(50));

    // Save test results
    const testResults = {
      userId: account.userId,
      address: account.address,
      did: account.did,
      mnemonic: account.mnemonic,
      duration,
      testType: "refactored-integration",
      timestamp: new Date().toISOString(),
    };

    const outputFile = `./test-results-${account.address}.json`;
    fs.writeFileSync(outputFile, JSON.stringify(testResults, null, 2));
    console.log(`\n💾 Test results saved to ${outputFile}`);

    return { account, duration };
  } catch (error) {
    console.error("❌ Full integration test failed:");
    console.error(
      `   ${error instanceof Error ? error.message : String(error)}`
    );
    throw error;
  }
}

/**
 * Main test runner
 */
async function main() {
  console.log("🧪 IXO Account Service - Refactored Testing");
  console.log("=".repeat(50));
  console.log(`User ID: ${userId}`);
  console.log(`PIN: ${"*".repeat(pin.length)}`);
  console.log(
    `Test Mode: ${testModules ? "Individual Modules" : "Full Integration"}`
  );

  try {
    // Always test configuration first
    const config = await testConfiguration();

    if (testModules) {
      // Test individual modules
      const moduleResults = await testIndividualModules(config);

      console.log("\n📊 Module Performance Summary:");
      console.log(`   Wallet Generation: ${moduleResults.timings.wallet}ms`);
      console.log(`   Fee Grant Setup: ${moduleResults.timings.feegrant}ms`);
      console.log(`   DID Creation: ${moduleResults.timings.did}ms`);
      console.log(
        `   Total Core Operations: ${Object.values(moduleResults.timings).reduce((a, b) => a + b, 0)}ms`
      );
    } else {
      // Test full integration
      const integrationResults = await testFullIntegration();

      console.log("\n📊 Integration Performance:");
      console.log(`   Total Duration: ${integrationResults.duration}ms`);
      console.log(
        `   Average per Operation: ${Math.round(integrationResults.duration / 4)}ms`
      );
    }

    console.log("\n🎉 All tests completed successfully!");
  } catch (error) {
    console.error("\n💥 Test failed:");
    console.error(error);
    process.exit(1);
  }
}

// Run the tests
main();
