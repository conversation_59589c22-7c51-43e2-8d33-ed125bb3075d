/**
 * Test Refactored Actor System
 *
 * Verifies that the refactored actor injection system works correctly
 */

import { createActor } from "xstate";
import { supamotoWalletMachine } from "../../machines/supamoto-wallet/supamotoWalletMachine.js";
import { createCurrentEnvironmentActors } from "../../config/actors.js";

console.log("🧪 Testing Refactored Actor System\n");

async function testActorInjection() {
  try {
    console.log("1️⃣ Creating actors for current environment...");
    const actors = createCurrentEnvironmentActors();
    console.log("✅ Actors created successfully");

    console.log("2️⃣ Configuring machine with actors...");
    const configuredMachine = supamotoWalletMachine.provide({
      actors: actors as any, // Type assertion to bypass strict typing
    });
    console.log("✅ Machine configured successfully");

    console.log("3️⃣ Creating actor instance...");
    const actor = createActor(configuredMachine, {
      input: {
        sessionId: "test-session-123",
        phoneNumber: "+1234567890",
        serviceCode: "*2233#",
      },
    });
    console.log("✅ Actor instance created successfully");

    console.log("4️⃣ Starting actor...");
    actor.start();
    console.log("✅ Actor started successfully");

    console.log("5️⃣ Testing basic flow...");
    actor.send({
      type: "DIAL_USSD",
      phoneNumber: "+1234567890",
      serviceCode: "*2233#",
    });

    const snapshot = actor.getSnapshot();
    console.log(`📍 Current state: ${snapshot.value}`);
    console.log(`💬 Message: ${snapshot.context.message}`);

    actor.stop();
    console.log("✅ Actor stopped successfully");

    console.log("\n🎉 Refactored actor system test completed successfully!");
  } catch (error) {
    console.error("❌ Test failed:", error);
    process.exit(1);
  }
}

testActorInjection();
