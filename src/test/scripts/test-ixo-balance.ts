/**
 * Test IXO Balance Check Integration
 *
 * This script tests the IXO balance checking functionality
 * without requiring a full USSD server setup.
 */

import { createActor } from "xstate";
import { ixoBalanceMachine } from "../../machines/supamoto-wallet/user-services/ixoBalanceMachine.js";

console.log("🧪 Testing IXO Balance Check Integration\n");

// Test input
const testInput = {
  sessionId: "test-session-123",
  phoneNumber: "+1234567890",
  serviceCode: "*2233#",
};

// Create actor
const actor = createActor(ixoBalanceMachine, { input: testInput });

// Subscribe to state changes
actor.subscribe(snapshot => {
  console.log(`📍 State: ${snapshot.value}`);
  if (snapshot.context.message) {
    console.log(`💬 Message: ${snapshot.context.message}`);
  }
  if (snapshot.context.error) {
    console.log(`❌ Error: ${snapshot.context.error}`);
  }
  if (snapshot.context.validationError) {
    console.log(`⚠️  Validation Error: ${snapshot.context.validationError}`);
  }
  console.log("─".repeat(50));
});

async function runTest() {
  console.log("1️⃣ Starting machine...");
  actor.start();

  await new Promise(resolve => setTimeout(resolve, 500));

  console.log("2️⃣ Sending START event...");
  actor.send({ type: "START" });

  await new Promise(resolve => setTimeout(resolve, 500));

  console.log("3️⃣ Testing invalid address...");
  actor.send({ type: "INPUT_ADDRESS", input: "invalid" });

  await new Promise(resolve => setTimeout(resolve, 500));

  console.log(
    "4️⃣ Testing valid address format (will likely show 'Account Not Found')..."
  );
  actor.send({
    type: "INPUT_ADDRESS",
    input: "ixo1234567890abcdef1234567890abcdef12345678",
  });

  await new Promise(resolve => setTimeout(resolve, 3000));

  console.log("5️⃣ Testing navigation back...");
  actor.send({ type: "BACK_TO_SERVICES" });

  await new Promise(resolve => setTimeout(resolve, 500));

  console.log("✅ Test completed!");
}

runTest().catch(console.error);
