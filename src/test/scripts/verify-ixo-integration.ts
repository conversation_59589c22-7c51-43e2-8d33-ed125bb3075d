/**
 * Verify IXO Balance Integration
 *
 * Quick verification that the IXO balance option appears in the user services menu
 */

import { createActor } from "xstate";
import { userServicesMachine } from "../../machines/supamoto-wallet/user-services/userServicesMachine.js";

console.log("🔍 Verifying IXO Balance Integration\n");

// Test input
const testInput = {
  sessionId: "test-session-123",
  phoneNumber: "+1234567890",
  serviceCode: "*2233#",
  customerName: "Test User",
  currentBalance: 100.5,
};

async function runTest() {
  // Create actor
  const actor = createActor(userServicesMachine, { input: testInput });

  console.log("1️⃣ Starting user services machine...");
  actor.start();

  console.log("2️⃣ Sending START event...");
  actor.send({ type: "START" });

  // Wait for the machine to finish loading balance and reach serviceSelection
  await new Promise(resolve => {
    const subscription = actor.subscribe(snapshot => {
      if (snapshot.value === "serviceSelection") {
        subscription.unsubscribe();
        resolve(undefined);
      }
    });
  });

  const snapshot = actor.getSnapshot();
  console.log(`📍 Current state: ${snapshot.value}`);
  console.log(`💬 Menu message:`);
  console.log(snapshot.context.message);

  // Check if the menu includes our new IXO balance option
  if (snapshot.context.message?.includes("8. Check IXO Account Balance")) {
    console.log("✅ SUCCESS: IXO Balance option found in menu!");
  } else {
    console.log("❌ FAILED: IXO Balance option not found in menu");
    console.log("Expected to find: '8. Check IXO Account Balance'");
  }

  console.log("\n3️⃣ Testing IXO balance selection...");
  actor.send({ type: "SELECT_IXO_BALANCE_CHECK" });

  const snapshot2 = actor.getSnapshot();
  console.log(`📍 After selection state: ${snapshot2.value}`);

  if (snapshot2.value === "ixoBalanceService") {
    console.log("✅ SUCCESS: IXO Balance service state reached!");
  } else {
    console.log("❌ FAILED: Expected 'ixoBalanceService' state");
  }

  console.log("\n🎉 Integration verification completed!");
}

runTest().catch(console.error);
