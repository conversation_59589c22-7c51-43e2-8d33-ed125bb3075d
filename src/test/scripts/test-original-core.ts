#!/usr/bin/env node
/**
 * Test Script for Original Core Fee Grant + DID Flow
 *
 * Tests just the core blockchain operations from the original implementation:
 * - Wallet generation
 * - Fee grant setup
 * - DID creation
 *
 * Skips database and Matrix operations to isolate the fee grant issue.
 */

import dotenv from "dotenv";
import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";

// Load environment variables
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const envPath = path.resolve(__dirname, "../../../.env");

if (fs.existsSync(envPath)) {
  dotenv.config({ path: envPath });
  console.log("✅ Loaded .env file from", envPath);
} else {
  console.warn("⚠️  No .env file found at", envPath);
  dotenv.config();
}

// Import core utilities directly
import { utils } from "@ixo/impactxclient-sdk";
import { getSecpClient } from "../../utils/secp.js";
import { ensureAddressFeegrant } from "../../utils/feegrant.js";
import { createIidDocumentIfNotExists } from "../../utils/did.js";
import { CHAIN_RPC_URL } from "../../constants/common.js";
import { OfflineSigner } from "@cosmjs/proto-signing";

// Configuration
const config = {
  chainRpcUrl: CHAIN_RPC_URL || process.env.CHAIN_RPC_URL,
  feegrantUrl: process.env.FEEGRANT_URL,
  feegrantAuth: process.env.FEEGRANT_AUTH,
  feegrantGranter: process.env.FEEGRANT_GRANTER,
};

// Validate required config
const missing = Object.entries(config)
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  .filter(([_, value]) => !value)
  .map(([key]) => key);

if (missing.length > 0) {
  console.error(`❌ Missing required config: ${missing.join(", ")}`);
  process.exit(1);
}

console.log("🔧 Configuration:");
console.log(`   Chain RPC: ${config.chainRpcUrl}`);
console.log(`   Feegrant URL: ${config.feegrantUrl}`);
console.log(`   Feegrant Granter: ${config.feegrantGranter}`);

/**
 * Generate blockchain wallet (from original implementation)
 */
async function generateWallet() {
  console.log("\n1️⃣ Generating blockchain wallet...");
  const start = Date.now();

  const mnemonic = utils.mnemonic.generateMnemonic();
  const wallet = await getSecpClient(mnemonic);
  const address = wallet.baseAccount.address;

  const duration = Date.now() - start;
  console.log(`✅ Wallet generated in ${duration}ms`);
  console.log(`   Address: ${address}`);
  console.log(`   Mnemonic words: ${mnemonic.split(" ").length}`);

  return { mnemonic, wallet, address };
}

/**
 * Setup fee grant (from original implementation)
 */
async function setupFeegrant(address: string) {
  console.log("\n2️⃣ Setting up fee grant...");
  const start = Date.now();

  await ensureAddressFeegrant(address, {
    feegrantUrl: config.feegrantUrl!,
    feegrantAuth: config.feegrantAuth!,
  });

  const duration = Date.now() - start;
  console.log(`✅ Fee grant setup completed in ${duration}ms`);
}

/**
 * Create DID (from original implementation)
 */
async function createDid(address: string, wallet: any) {
  console.log("\n3️⃣ Creating DID...");
  const start = Date.now();

  const did = await createIidDocumentIfNotExists({
    address,
    offlineSigner: wallet as OfflineSigner,
    chainRpcUrl: config.chainRpcUrl!,
    feegrantGranter: config.feegrantGranter!,
  });

  const duration = Date.now() - start;
  console.log(`✅ DID created in ${duration}ms`);
  console.log(`   DID: ${did}`);

  return did;
}

/**
 * Main test function - replicates original sequence exactly
 */
async function main() {
  console.log("🧪 Original Core Fee Grant + DID Test");
  console.log("=".repeat(50));

  const overallStart = Date.now();

  try {
    // Step 1: Generate wallet (same as original)
    const { mnemonic, wallet, address } = await generateWallet();

    // Step 2: Setup fee grant (same as original)
    await setupFeegrant(address);

    // Step 3: Create DID (same as original)
    const did = await createDid(address, wallet);

    const totalDuration = Date.now() - overallStart;

    console.log("\n🎉 Test completed successfully!");
    console.log("=".repeat(50));
    console.log(`📊 Total Duration: ${totalDuration}ms`);
    console.log(`📋 Results:`);
    console.log(`   Address: ${address}`);
    console.log(`   DID: ${did}`);
    console.log(`   Mnemonic: ${mnemonic.split(" ").length} words`);

    // Save results
    const results = {
      address,
      did,
      mnemonic,
      totalDuration,
      timestamp: new Date().toISOString(),
      testType: "original-core",
    };

    const outputFile = `./test-results-original-${address}.json`;
    fs.writeFileSync(outputFile, JSON.stringify(results, null, 2));
    console.log(`💾 Results saved to ${outputFile}`);
  } catch (error) {
    const totalDuration = Date.now() - overallStart;
    console.error("\n💥 Test failed:");
    console.error(`   Duration: ${totalDuration}ms`);
    console.error(
      `   Error: ${error instanceof Error ? error.message : String(error)}`
    );
    process.exit(1);
  }
}

// Run the test
main();
