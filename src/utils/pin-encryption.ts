/**
 * PIN Encryption Utilities
 * SECURITY CRITICAL: Handles PIN encryption/decryption operations
 */
import crypto from "crypto";
import { config } from "../config.js";
import { createModuleLogger } from "../services/logger.js";

// Create a module-specific logger
const logger = createModuleLogger("pin-encryption");

// Convert hex string to buffer if needed, or use raw string if it's already 32 bytes
function getKeyBuffer(key: string): Buffer {
  if (key.length === 32) {
    return Buffer.from(key);
  }
  if (key.length === 64) {
    return Buffer.from(key, "hex");
  }
  throw new Error(
    "PIN_ENCRYPTION_KEY must be either 32 bytes or 64 hex characters"
  );
}

const ENCRYPTION_KEY = getKeyBuffer(config.SYSTEM.PIN_ENCRYPTION_KEY);
const IV_LENGTH = 16;

export function encryptPin(pin: string): string {
  logger.debug(
    {
      keyLength: ENCRYPTION_KEY.length,
      keyConfigured: !!ENCRYPTION_KEY,
    },
    "PIN encryption initiated"
  );

  const iv = crypto.randomBytes(IV_LENGTH);
  const cipher = crypto.createCipheriv("aes-256-cbc", ENCRYPTION_KEY, iv);
  let encrypted = cipher.update(pin, "utf8", "hex");
  encrypted += cipher.final("hex");
  return iv.toString("hex") + ":" + encrypted;
}

export function decryptPin(encrypted: string): string {
  const [ivHex, encryptedPin] = encrypted.split(":");
  const iv = Buffer.from(ivHex, "hex");
  const decipher = crypto.createDecipheriv("aes-256-cbc", ENCRYPTION_KEY, iv);
  let decrypted = decipher.update(encryptedPin, "hex", "utf8");
  decrypted += decipher.final("utf8");
  return decrypted;
}

export function verifyPin(plainPin: string, encryptedPin: string): boolean {
  try {
    const decryptedPin = decryptPin(encryptedPin);
    return plainPin === decryptedPin;
  } catch (error) {
    logger.error(
      {
        error: error instanceof Error ? error.message : String(error),
      },
      "PIN verification failed"
    );
    return false;
  }
}
