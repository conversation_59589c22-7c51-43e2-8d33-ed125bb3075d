// utils/encryption.ts
import CryptoJ<PERSON> from "crypto-js";

export function encrypt(data: string, password: string): string {
  return CryptoJS.AES.encrypt(data, password).toString();
}

export function decrypt(ciphertext: string, password: string): string {
  const bytes = CryptoJS.AES.decrypt(ciphertext, password);
  return bytes.toString(CryptoJS.enc.Utf8);
}

/**
 * Encrypt a PIN using the configured encryption key
 */
export function encryptPin(pin: string): string {
  const encryptionKey =
    process.env.PIN_ENCRYPTION_KEY || "default-key-change-in-production";
  return encrypt(pin, encryptionKey);
}

/**
 * Decrypt a PIN using the configured encryption key
 */
export function decryptPin(encryptedPin: string): string {
  const encryptionKey =
    process.env.PIN_ENCRYPTION_KEY || "default-key-change-in-production";
  return decrypt(encryptedPin, encryptionKey);
}
