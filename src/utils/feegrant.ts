// utils/feegrant.ts
import axios from "axios";
import { createModuleLogger } from "../services/logger.js";

// Create a module-specific logger
const logger = createModuleLogger("feegrant");

// Configuration interface for fee grant operations
interface FeegrantConfig {
  feegrantUrl: string;
  feegrantAuth: string;
}

/**
 * Check if an address already has a feegrant from the granter
 */
export async function checkAddressFeegrant(
  address: string,
  config: FeegrantConfig
): Promise<boolean> {
  const checkUrl = `${config.feegrantUrl}/check/${address}`;

  logger.debug(
    {
      address,
      checkUrl,
      hasAuth: !!config.feegrantAuth,
    },
    "Checking address feegrant status"
  );

  try {
    const response = await axios.get(checkUrl, {
      headers: {
        Authorization: `Bearer ${config.feegrantAuth}`,
      },
      timeout: 10000, // 10 second timeout
    });

    logger.debug(
      {
        address,
        status: response.status,
        data: response.data,
      },
      "Feegrant check response received"
    );

    const hasFeegrant = response.data.hasFeegrant === true;

    logger.info(
      {
        address,
        hasFeegrant,
      },
      "Feegrant check completed"
    );

    return hasFeegrant;
  } catch (error) {
    if (axios.isAxiosError(error)) {
      logger.error(
        {
          address,
          checkUrl,
          status: error.response?.status,
          statusText: error.response?.statusText,
          data: error.response?.data,
          message: error.message,
        },
        "Axios error checking address feegrant status"
      );
    } else {
      logger.error(
        {
          error: error instanceof Error ? error.message : String(error),
          address,
          checkUrl,
        },
        "Error checking address feegrant status"
      );
    }
    throw error;
  }
}

/**
 * Grant a feegrant to the specified address
 */
export async function grantAddressFeegrant(
  address: string,
  config: FeegrantConfig
): Promise<void> {
  const grantUrl = `${config.feegrantUrl}/grant`;

  logger.debug(
    {
      address,
      grantUrl,
      hasAuth: !!config.feegrantAuth,
    },
    "Requesting feegrant for address"
  );

  try {
    const response = await axios.post(
      grantUrl,
      { address },
      {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${config.feegrantAuth}`,
        },
        timeout: 15000, // 15 second timeout for grant requests
      }
    );

    logger.debug(
      {
        status: response.status,
        address,
        grantUrl,
      },
      "Feegrant request response received"
    );

    logger.debug(
      {
        responseData: response.data,
        address,
      },
      "Feegrant response data"
    );

    if (response.status === 200 || response.status === 201) {
      logger.info(
        {
          address,
          status: response.status,
        },
        "Feegrant successfully granted"
      );
    } else {
      logger.warn(
        {
          address,
          status: response.status,
          data: response.data,
        },
        "Feegrant request completed with unexpected status"
      );
    }
  } catch (error: unknown) {
    if (axios.isAxiosError(error)) {
      logger.error(
        {
          address,
          grantUrl,
          status: error.response?.status,
          statusText: error.response?.statusText,
          data: error.response?.data,
          message: error.message,
          code: error.code,
        },
        "Axios error during feegrant request"
      );
    } else {
      logger.error(
        {
          error: error instanceof Error ? error.message : String(error),
          address,
          grantUrl,
        },
        "Error granting feegrant to address"
      );
    }
    throw error;
  }
}

/**
 * Ensure an address has a feegrant, granting one if needed
 */
export async function ensureAddressFeegrant(
  address: string,
  config: FeegrantConfig
): Promise<void> {
  try {
    logger.debug(
      {
        address,
        feegrantUrl: config.feegrantUrl,
      },
      "Checking if address already has a feegrant"
    );

    const hasFeegrant = await checkAddressFeegrant(address, config);
    if (hasFeegrant) {
      logger.debug(
        {
          address,
        },
        "Address already has a feegrant"
      );
      return;
    }

    logger.debug(
      {
        address,
      },
      "Address needs a feegrant, requesting one"
    );

    await grantAddressFeegrant(address, config);

    // Verify the feegrant was actually granted
    logger.debug(
      {
        address,
      },
      "Verifying feegrant was granted"
    );

    // Wait a moment for the grant to be processed
    await new Promise(resolve => setTimeout(resolve, 500));

    const hasFeegrantAfterGrant = await checkAddressFeegrant(address, config);
    if (!hasFeegrantAfterGrant) {
      logger.warn(
        {
          address,
        },
        "Feegrant verification failed - grant may not be active yet"
      );
    } else {
      logger.debug(
        {
          address,
        },
        "Feegrant verification successful"
      );
    }

    logger.debug(
      {
        address,
      },
      "Feegrant process completed"
    );
  } catch (error) {
    logger.error(
      {
        error: error instanceof Error ? error.message : String(error),
        address,
        feegrantUrl: config.feegrantUrl,
      },
      "Failed to ensure feegrant for address"
    );
    throw error;
  }
}
