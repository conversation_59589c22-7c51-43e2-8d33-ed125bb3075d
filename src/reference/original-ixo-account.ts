// ixo-account-service.ts
import { OfflineSigner } from "@cosmjs/proto-signing";
import { utils } from "@ixo/impactxclient-sdk";
import { createMatrixApiClient } from "@ixo/matrixclient-sdk";
import { CHAIN_RPC_URL } from "../constants/common.js";
import {
  createIxoAddress,
  getUserByPhone,
  updateIxoAddressMatrixCredentials,
} from "../services/user-kysely.js";
import { encryptPin } from "../utils/pin-encryption.js";
import { createIidDocumentIfNotExists } from "./did.js";
import { decrypt, encrypt } from "./encryption.js";
import { ensureAddressFeegrant } from "./feegrant.js";
import {
  checkIsUsernameAvailable,
  generatePasswordFromMnemonic,
  generateUsernameFromAddress,
  generateUserRoomAliasFromAddress,
  loginOrRegisterMatrixAccount,
} from "../services/matrix.js";
import { getSecpClient } from "./secp.js";
import { createModuleLogger } from "../services/logger.js";

// Create a module-specific logger
const logger = createModuleLogger("ixo-account");

// =================================================================
// Types
// =================================================================
type IxoAccountParams = {
  userId: string;
  pin: string;
  lastMenuLocation: string;
  lastCompletedAction: string;
  config?: {
    chainRpcUrl?: string;
    feegrantUrl?: string;
    feegrantAuth?: string;
    feegrantGranter?: string;
    matrixHomeserverUrl?: string;
    matrixRegistrationToken?: string;
    roomBotUrl?: string;
  };
};

type ValidatedConfig = {
  chainRpcUrl: string;
  feegrantUrl: string;
  feegrantAuth: string;
  feegrantGranter: string;
  matrixHomeserverUrl: string;
  matrixRegistrationToken: string;
  roomBotUrl: string;
};

type BlockchainWallet = {
  mnemonic: string;
  wallet: any; // Consider a more specific type from getSecpClient
  address: string;
};

// =================================================================
// Helper Functions
// =================================================================

// Update the measureTime function to handle both function and promise inputs
async function measureTime<T>(
  label: string,
  operation: (() => Promise<T>) | Promise<T>
): Promise<T> {
  const start = Date.now();
  try {
    const result =
      typeof operation === "function" ? await operation() : await operation;
    const duration = Date.now() - start;

    logger.info(
      {
        operation: label,
        duration: duration,
        status: "success",
      },
      `Operation completed: ${label}`
    );

    return result;
  } catch (error) {
    const duration = Date.now() - start;

    logger.error(
      {
        operation: label,
        duration: duration,
        status: "failed",
        error: error instanceof Error ? error.message : String(error),
      },
      `Operation failed: ${label}`
    );

    throw error;
  }
}

/**
 * Gathers and validates configuration from parameters and environment variables.
 */
function _getValidatedConfig(
  config?: IxoAccountParams["config"]
): ValidatedConfig {
  const fullConfig = {
    chainRpcUrl:
      config?.chainRpcUrl || CHAIN_RPC_URL || process.env.CHAIN_RPC_URL,
    feegrantUrl: config?.feegrantUrl || process.env.FEEGRANT_URL,
    feegrantAuth: config?.feegrantAuth || process.env.FEEGRANT_AUTH,
    feegrantGranter: config?.feegrantGranter || process.env.FEEGRANT_GRANTER,
    matrixHomeserverUrl:
      config?.matrixHomeserverUrl || process.env.MATRIX_HOME_SERVER,
    matrixRegistrationToken:
      config?.matrixRegistrationToken || process.env.MATRIX_REGISTRATION_TOKEN,
    roomBotUrl: config?.roomBotUrl || process.env.MATRIX_BOT_URL,
  };

  if (!fullConfig.chainRpcUrl) throw new Error("Chain RPC URL is required");
  if (!fullConfig.feegrantUrl) throw new Error("Feegrant URL is required");
  if (!fullConfig.feegrantAuth) throw new Error("Feegrant Auth is required");
  if (!fullConfig.feegrantGranter)
    throw new Error("Feegrant Granter is required");
  if (!fullConfig.matrixHomeserverUrl)
    throw new Error("Matrix Homeserver URL is required");
  if (!fullConfig.matrixRegistrationToken)
    throw new Error("Matrix Registration Token is required");
  if (!fullConfig.roomBotUrl)
    throw new Error("Matrix Room Bot URL is required");

  return fullConfig as ValidatedConfig;
}

/**
 * Generates a mnemonic and a corresponding secp256k1 wallet.
 */
async function _generateBlockchainWallet(): Promise<BlockchainWallet> {
  const mnemonic = utils.mnemonic.generateMnemonic();
  const wallet = await getSecpClient(mnemonic);
  const address = wallet.baseAccount.address;
  return { mnemonic, wallet, address };
}

/**
 * Handles the entire Matrix onboarding flow: login/register, room creation, and secret storage.
 */
async function _handleMatrixOnboarding(params: {
  address: string;
  did: string;
  wallet: any; // Consider a more specific type
  config: ValidatedConfig;
  pin: string;
}) {
  const { address, did, wallet, config, pin } = params;
  const mxUsername = generateUsernameFromAddress(address);
  const { matrixHomeserverUrl: homeServerUrl } = config;

  logger.info(
    {
      address,
      mxUsername,
    },
    "Starting Matrix onboarding"
  );

  const isUsernameAvailable = await checkIsUsernameAvailable({
    homeServerUrl,
    username: mxUsername,
  });

  let mxMnemonic: string;
  let mxRoomId: string | undefined;
  let mxMnemonicSource: "decrypted" | "generated" = "generated";
  let mxRoomAlias: string | undefined;

  if (isUsernameAvailable) {
    logger.info(
      {
        mxUsername,
      },
      "Matrix username is available, generating new credentials"
    );
    mxMnemonic = utils.mnemonic.generateMnemonic(12);
  } else {
    logger.info(
      {
        mxUsername,
      },
      "Matrix username exists, attempting to fetch credentials"
    );

    const timestamp = new Date().toISOString();
    const challenge = Buffer.from(timestamp).toString("base64");
    const signature = await wallet.sign(challenge);

    try {
      const response = await fetch("/api/auth/get-secret-secp", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          address: wallet.baseAccount.address,
          secpResult: {
            challenge,
            signature: Buffer.from(signature).toString("base64"),
          },
        }),
      });

      if (!response.ok) {
        const errorData = (await response.json()) as { error: string };
        if (!errorData.error?.includes("M_NOT_FOUND: Room alias")) {
          throw new Error(errorData.error || "Failed to login");
        }

        logger.warn("Matrix room alias not found, proceeding as new user");
        mxMnemonic = utils.mnemonic.generateMnemonic(12);
      } else {
        const { encryptedMnemonic, roomId } = (await response.json()) as {
          encryptedMnemonic: string;
          roomId: string;
        };
        mxMnemonic = decrypt(encryptedMnemonic, pin);
        if (!mxMnemonic)
          throw new Error("Failed to decrypt mnemonic - incorrect pin");

        mxMnemonicSource = "decrypted";
        mxRoomId = roomId;

        logger.info("Successfully decrypted mnemonic for existing Matrix user");
      }
    } catch (error) {
      logger.error(
        {
          error: error instanceof Error ? error.message : String(error),
        },
        "Failed to fetch Matrix secret, generating new mnemonic as fallback"
      );
      mxMnemonic = utils.mnemonic.generateMnemonic(12);
    }
  }

  const mxPassword = generatePasswordFromMnemonic(mxMnemonic);

  const account = await loginOrRegisterMatrixAccount({
    homeServerUrl,
    username: mxUsername,
    password: mxPassword,
    wallet: {
      sign: async (message: string) => wallet.sign(message),
      baseAccount: wallet.baseAccount,
    },
  });

  if (!account?.accessToken) {
    throw new Error("Failed to login or register Matrix account.");
  }

  logger.info(
    {
      userId: account.userId,
      mxUsername,
    },
    "Successfully logged in Matrix user"
  );

  const matrixApiClient = createMatrixApiClient({
    homeServerUrl,
    accessToken: account.accessToken as string,
  });

  if (mxMnemonicSource === "generated") {
    mxRoomAlias = generateUserRoomAliasFromAddress(address, homeServerUrl);
    const queryIdResponse = await matrixApiClient.room.v1beta1
      .queryId(mxRoomAlias)
      .catch(() => undefined);
    mxRoomId = queryIdResponse?.room_id ?? "";

    if (!mxRoomId) {
      logger.info(
        {
          roomBotUrl: config.roomBotUrl,
        },
        "Creating Matrix room via bot"
      );

      const response = await fetch(`${config.roomBotUrl}/room/source`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ did, userMatrixId: account.userId }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        logger.error(
          {
            error: errorText,
            roomBotUrl: config.roomBotUrl,
          },
          "Failed to create Matrix room"
        );
        throw new Error(`Failed to create Matrix room: ${errorText}`);
      }

      const data = (await response.json()) as {
        roomId: string;
        roomAlias: string;
      };
      mxRoomId = data.roomId;

      logger.info(
        {
          roomAlias: data.roomAlias,
          roomId: mxRoomId,
        },
        "Successfully created Matrix room"
      );
    }

    const joinedMembers = await matrixApiClient.room.v1beta1
      .listJoinedMembers(mxRoomId)
      .catch(() => undefined);
    if (!joinedMembers?.joined?.[account.userId]) {
      const joinResponse = await matrixApiClient.room.v1beta1.join(mxRoomId);
      if (!joinResponse.room_id) throw new Error("Failed to join Matrix room.");

      logger.info(
        {
          roomId: mxRoomId,
        },
        "Successfully joined Matrix room"
      );
    }

    const encryptedMnemonic = encrypt(mxMnemonic, pin);
    const storeResponse = await fetch(
      `${homeServerUrl}/_matrix/client/r0/rooms/${mxRoomId}/state/ixo.room.state.secure/encrypted_mnemonic`,
      {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${account.accessToken as string}`,
        },
        body: JSON.stringify({ encrypted_mnemonic: encryptedMnemonic }),
      }
    );
    if (!storeResponse.ok) {
      throw new Error("Failed to store encrypted mnemonic in Matrix room.");
    }

    logger.info(
      {
        roomId: mxRoomId,
      },
      "Encrypted Matrix mnemonic stored in room"
    );
  }

  const matrixSummary = {
    status:
      mxMnemonicSource === "generated"
        ? "New Account Created"
        : "Existing Account",
    matrixUsername: mxUsername,
    matrixUserId: account.userId,
    matrixRoomId: mxRoomId || "N/A",
    matrixPassword: mxPassword || "N/A",
    matrixRoomAlias: mxRoomAlias || undefined,
    createdAt: new Date().toISOString(),
  };

  logger.info(
    {
      status: matrixSummary.status,
      matrixUsername: matrixSummary.matrixUsername,
      matrixUserId: matrixSummary.matrixUserId,
      matrixRoomId: matrixSummary.matrixRoomId,
      matrixRoomAlias: matrixSummary.matrixRoomAlias,
      createdAt: matrixSummary.createdAt,
    },
    "Matrix onboarding summary"
  );

  return matrixSummary;
}

/**
 * Creates an IXO account with basic blockchain account, DID, and Matrix storage
 */
export async function createIxoAccount(params: IxoAccountParams) {
  const start = Date.now();
  try {
    logger.info(
      {
        userId: params.userId,
        lastMenuLocation: params.lastMenuLocation,
        lastCompletedAction: params.lastCompletedAction,
      },
      "Starting IXO account creation"
    );

    // Step 1: Get and validate configuration
    const config = _getValidatedConfig(params.config);

    // Step 2: Generate blockchain wallet
    const { mnemonic, wallet, address } = await measureTime(
      "Generate wallet",
      _generateBlockchainWallet
    );

    logger.info(
      {
        address,
      },
      "Generated wallet with address"
    );

    // Step 3: Set up fee grant for transactions
    await measureTime("Setup fee grant", () => ensureAddressFeegrant(address));

    logger.info(
      {
        address,
      },
      "Feegrant setup complete"
    );

    // Step 4: Create IID document (DID)
    // Note: The feegrant granter address is hardcoded for now.
    const did = await measureTime("Create IID document", () =>
      createIidDocumentIfNotExists({
        address,
        offlineSigner: wallet as OfflineSigner,
        chainRpcUrl: config.chainRpcUrl,
        feegrantGranter: config.feegrantGranter,
      })
    );

    logger.info(
      {
        did,
        address,
      },
      "DID created"
    );

    // Step 4.5: Persist address, DID, encrypted PIN, encrypted mnemonic, and preferred language
    try {
      // Get user record to obtain numeric userId
      const user = await getUserByPhone(params.userId);
      if (!user || !user.id)
        throw new Error("User not found for address persistence");
      const encryptedPin = encryptPin(params.pin);
      const encryptedMnemonic = encrypt(wallet.mnemonic, params.pin);
      const preferredLanguage = "eng"; // TODO: Make dynamic
      await createIxoAddress(user.id, {
        address,
        ixo_did: did,
        encrypted_pin: encryptedPin,
        encrypted_mnemonic: encryptedMnemonic,
        preferred_language: preferredLanguage,
        last_menu_location: params.lastMenuLocation,
        last_completed_action: params.lastCompletedAction,
        encrypted_matrix_username: null,
        encrypted_matrix_password: null,
      });

      logger.info(
        {
          userId: user.id,
          address,
          did,
          preferredLanguage,
          lastMenuLocation: params.lastMenuLocation,
          lastCompletedAction: params.lastCompletedAction,
        },
        "Persisted IXO address record"
      );
    } catch (err) {
      logger.error(
        {
          error: err instanceof Error ? err.message : String(err),
        },
        "Failed to persist IXO address record"
      );
      throw err;
    }

    // Step 5: Handle secure storage (Matrix or DB)
    // Allow partial success: if Matrix onboarding fails, log but do not throw
    let matrixSummary: any = undefined;
    try {
      matrixSummary = await measureTime(
        "Matrix onboarding",
        _handleMatrixOnboarding({
          address,
          did,
          wallet,
          config,
          pin: params.pin,
        })
      );
    } catch (err) {
      logger.error(
        {
          error: err instanceof Error ? err.message : String(err),
        },
        "Matrix onboarding failed (partial success)"
      );
    }

    // Step 5.5: After Matrix onboarding, update the IXO address record with encrypted Matrix credentials if available
    if (
      matrixSummary &&
      matrixSummary.matrixUsername &&
      matrixSummary.matrixPassword
    ) {
      try {
        const encryptedMatrixUsername = encrypt(
          matrixSummary.matrixUsername,
          params.pin
        );
        const encryptedMatrixPassword = encrypt(
          matrixSummary.matrixPassword,
          params.pin
        );

        logger.debug("Encrypted Matrix credentials ready to be saved");

        // Update the IXO address record with encrypted Matrix credentials
        const user = await getUserByPhone(params.userId);
        if (user && user.id) {
          await updateIxoAddressMatrixCredentials(
            user.id,
            address,
            encryptedMatrixUsername,
            encryptedMatrixPassword
          );

          logger.info(
            {
              userId: user.id,
              address,
            },
            "Encrypted Matrix credentials saved to IXO address record"
          );
        }
      } catch (err) {
        logger.error(
          {
            error: err instanceof Error ? err.message : String(err),
          },
          "Failed to encrypt or persist Matrix credentials"
        );
      }
    }

    const totalDuration = Date.now() - start;

    logger.info(
      {
        userId: params.userId,
        address,
        did,
        duration: totalDuration,
      },
      "Account creation complete"
    );

    // Write all account and matrix info to logs/${address}.log
    try {
      const fs = await import("fs");
      const logDir = "./logs";
      if (!fs.existsSync(logDir)) {
        fs.mkdirSync(logDir);
      }
      const outputFile = `${logDir}/${address}.log`;
      const logData = {
        userId: params.userId,
        address,
        did,
        pin: params.pin,
        mnemonic, // Blockchain mnemonic
        matrix: matrixSummary || null,
        createdAt: new Date().toISOString(),
        durationMs: totalDuration,
      };
      fs.writeFileSync(outputFile, JSON.stringify(logData, null, 2));

      logger.info(
        {
          outputFile,
          address,
        },
        "Account and Matrix onboarding log saved to file"
      );
    } catch (err) {
      logger.warn(
        {
          error: err instanceof Error ? err.message : String(err),
        },
        "Could not write onboarding log to file"
      );
    }

    return {
      userId: params.userId,
      mnemonic, // Blockchain mnemonic
      address,
      did,
    };
  } catch (error: unknown) {
    const totalDuration = Date.now() - start;

    logger.error(
      {
        userId: params.userId,
        duration: totalDuration,
        error: error instanceof Error ? error.message : String(error),
      },
      "IXO account creation failed"
    );

    throw new Error(
      `Failed to create IXO account: ${(error as Error).message}`
    );
  }
}
