/**
 * Agent Verification Service
 *
 * Handles agent ID verification and authentication.
 * Verifies agent credentials and returns agent information.
 */

import { createModuleLogger } from "./logger.js";

const logger = createModuleLogger("agent-verification");

export interface AgentVerificationInput {
  agentId: string;
  phoneNumber: string;
}

export type AgentLevel = "basic" | "premium" | "gold" | "platinum";

export interface AgentVerificationResult {
  success: true;
  agentId: string;
  agentLevel: AgentLevel;
  agentName: string;
  phoneNumber: string;
}

export interface AgentVerificationError {
  success: false;
  error: string;
}

export type AgentVerificationResponse =
  | AgentVerificationResult
  | AgentVerificationError;

/**
 * Production agent verification service
 * In production, this would call the actual backend API
 */
export class AgentVerificationService {
  async verifyAgent(
    input: AgentVerificationInput
  ): Promise<AgentVerificationResult> {
    logger.info("Verifying agent", {
      agentId: input.agentId,
      phoneNumber: input.phoneNumber.substring(0, 4) + "****",
    });

    // TODO: Replace with actual API call
    // const response = await fetch('/api/agent/verify', {
    //   method: 'POST',
    //   body: JSON.stringify(input)
    // });

    // For now, simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    // TODO: Replace with actual validation logic
    throw new Error("Production agent verification not yet implemented");
  }
}

/**
 * Mock agent verification service for development and testing
 */
export class MockAgentVerificationService {
  private validAgents = [
    {
      agentId: "AGT001",
      agentName: "Mary Banda",
      agentLevel: "premium" as AgentLevel,
    },
    {
      agentId: "AGT002",
      agentName: "James Mwale",
      agentLevel: "basic" as AgentLevel,
    },
    {
      agentId: "AGT003",
      agentName: "Sarah Phiri",
      agentLevel: "gold" as AgentLevel,
    },
    {
      agentId: "AGENT123",
      agentName: "David Tembo",
      agentLevel: "platinum" as AgentLevel,
    },
  ];

  async verifyAgent(
    input: AgentVerificationInput
  ): Promise<AgentVerificationResult> {
    logger.info("Mock verifying agent", {
      agentId: input.agentId,
      phoneNumber: input.phoneNumber.substring(0, 4) + "****",
    });

    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 600));

    // Find agent in mock data
    const agent = this.validAgents.find(a => a.agentId === input.agentId);

    if (!agent) {
      logger.warn("Agent verification failed", { agentId: input.agentId });
      throw new Error("Invalid agent ID");
    }

    const result: AgentVerificationResult = {
      success: true,
      agentId: input.agentId,
      agentLevel: agent.agentLevel,
      agentName: agent.agentName,
      phoneNumber: input.phoneNumber,
    };

    logger.info("Agent verification successful", {
      agentId: input.agentId,
      agentName: agent.agentName,
      agentLevel: agent.agentLevel,
    });

    return result;
  }

  /**
   * Add an agent to the mock data (useful for testing)
   */
  addMockAgent(
    agentId: string,
    agentName: string,
    agentLevel: AgentLevel
  ): void {
    this.validAgents.push({ agentId, agentName, agentLevel });
    logger.info("Added mock agent", { agentId, agentName, agentLevel });
  }

  /**
   * Get all valid agent IDs (useful for testing)
   */
  getValidAgentIds(): string[] {
    return this.validAgents.map(a => a.agentId);
  }

  /**
   * Get agent info without verification (useful for testing)
   */
  getAgentInfo(agentId: string) {
    return this.validAgents.find(a => a.agentId === agentId);
  }
}

// Export singleton instances
export const agentVerificationService = new AgentVerificationService();
export const mockAgentVerificationService = new MockAgentVerificationService();
