/**
 * IXO Account Service - Simplified Orchestrator
 *
 * Coordinates wallet creation, DID setup, and Matrix storage.
 * Much simpler than the original 599-line monolith!
 */

import { OfflineSigner } from "@cosmjs/proto-signing";
import fs from "fs";
import { createModuleLogger } from "../logger.js";
import { getIxoConfig, type IxoConfig } from "./config.js";
import { handleMatrixOnboarding } from "./matrix-storage.js";
import { createDid, generateWallet, setupFeegrant } from "./wallet-and-did.js";

const logger = createModuleLogger("ixo-account");

export interface IxoAccountParams {
  userId: string;
  pin: string;
  lastMenuLocation: string;
  lastCompletedAction: string;
  config?: Partial<IxoConfig>;
}

export interface IxoAccountResult {
  userId: string;
  mnemonic: string;
  address: string;
  did: string;
  matrix?: {
    username: string;
    password: string;
    userId: string;
    roomId: string;
    roomAlias?: string;
  };
}

/**
 * Creates a complete IXO account with blockchain wallet, DID, and Matrix storage
 */
export async function createIxoAccount(
  params: IxoAccountParams
): Promise<IxoAccountResult> {
  const start = Date.now();

  logger.info(
    {
      userId: params.userId,
      lastMenuLocation: params.lastMenuLocation,
      lastCompletedAction: params.lastCompletedAction,
    },
    "Starting IXO account creation"
  );

  try {
    // Step 1: Validate configuration
    const config = getIxoConfig(params.config);

    // Step 2: Generate blockchain wallet
    const { mnemonic, wallet, address } = await generateWallet();
    logger.info({ address }, "Generated blockchain wallet");

    // Step 3: Setup fee grant
    await setupFeegrant(address, config);
    logger.info({ address }, "Fee grant setup complete");

    // Step 4: Create DID
    const did = await createDid(
      address,
      wallet as OfflineSigner,
      config.chainRpcUrl,
      config.feegrantGranter
    );
    logger.info({ address, did }, "DID created");

    // Step 5: Handle Matrix storage (allow partial failure)
    let matrixSummary;
    try {
      matrixSummary = await handleMatrixOnboarding({
        address,
        did,
        wallet,
        config: {
          matrixHomeserverUrl: config.matrixHomeserverUrl,
          roomBotUrl: config.roomBotUrl,
        },
        pin: params.pin,
      });
      logger.info("Matrix onboarding completed");
    } catch (error) {
      logger.error(
        { error: error instanceof Error ? error.message : String(error) },
        "Matrix onboarding failed (partial success)"
      );
    }

    // Step 6: Save account details to log file
    await saveAccountLog({
      userId: params.userId,
      address,
      did,
      pin: params.pin,
      mnemonic,
      matrix: matrixSummary,
      duration: Date.now() - start,
    });

    const duration = Date.now() - start;
    logger.info(
      {
        userId: params.userId,
        address,
        did,
        duration,
      },
      "IXO account creation completed"
    );

    return {
      userId: params.userId,
      mnemonic,
      address,
      did,
      matrix: matrixSummary
        ? {
            username: matrixSummary.matrixUsername,
            password: matrixSummary.matrixPassword,
            userId: matrixSummary.matrixUserId,
            roomId: matrixSummary.matrixRoomId,
            roomAlias: matrixSummary.matrixRoomAlias,
          }
        : undefined,
    };
  } catch (error) {
    const duration = Date.now() - start;
    logger.error(
      {
        userId: params.userId,
        duration,
        error: error instanceof Error ? error.message : String(error),
      },
      "IXO account creation failed"
    );

    throw new Error(
      `Failed to create IXO account: ${error instanceof Error ? error.message : String(error)}`
    );
  }
}

/**
 * Saves account creation details to a log file
 */
async function saveAccountLog(data: {
  userId: string;
  address: string;
  did: string;
  pin: string;
  mnemonic: string;
  matrix?: any;
  duration: number;
}): Promise<void> {
  try {
    const logDir = "./logs";
    if (!fs.existsSync(logDir)) {
      fs.mkdirSync(logDir);
    }

    const outputFile = `${logDir}/${data.address}.log`;
    const logData = {
      ...data,
      createdAt: new Date().toISOString(),
    };

    fs.writeFileSync(outputFile, JSON.stringify(logData, null, 2));

    logger.info({ outputFile, address: data.address }, "Account log saved");
  } catch (error) {
    logger.warn(
      { error: error instanceof Error ? error.message : String(error) },
      "Could not write account log to file"
    );
  }
}
