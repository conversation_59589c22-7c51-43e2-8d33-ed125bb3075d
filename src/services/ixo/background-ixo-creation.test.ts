/**
 * Background IXO Creation Service Tests
 *
 * Tests the background IXO account creation functionality
 * Note: IXO service is mocked as it's not available in test environment
 */

import { describe, it, expect, vi, beforeEach, afterEach } from "vitest";
import fs from "fs";
import { createIxoAccountBackground } from "./background-ixo-creation.js";

// Mock the IXO account service
vi.mock("./ixo-account.js", () => ({
  createIxoAccount: vi.fn(),
}));

// Mock the database
vi.mock("../../db/index.js", () => ({
  db: {
    transaction: vi.fn(() => ({
      execute: vi.fn(),
    })),
  },
}));

// Mock encryption utility
vi.mock("../../utils/encryption.js", () => ({
  encrypt: vi.fn((data, key) => `encrypted_${data}_with_${key}`),
}));

// Mock logger
vi.mock("../logger.js", () => ({
  createModuleLogger: vi.fn(() => ({
    info: vi.fn(),
    error: vi.fn(),
    warn: vi.fn(),
  })),
}));

// Mock fs
vi.mock("fs", () => ({
  default: {
    existsSync: vi.fn(),
    mkdirSync: vi.fn(),
    appendFileSync: vi.fn(),
  },
}));

import { createIxoAccount } from "./ixo-account.js";
import { db } from "../../db/index.js";

const mockCreateIxoAccount = vi.mocked(createIxoAccount);
const mockDb = vi.mocked(db);
const mockFs = vi.mocked(fs);

describe("Background IXO Creation Service", () => {
  const mockParams = {
    customerId: "CUST123",
    customerRecordId: 456,
    phoneNumber: "+**********",
    fullName: "John Doe",
    pin: "12345",
  };

  const mockIxoResult = {
    userId: "CUST123",
    mnemonic: "test mnemonic phrase",
    address: "ixo1testaddress",
    did: "did:ixo:testdid",
    matrix: {
      username: "testuser",
      password: "testpassword",
      userId: "@testuser:matrix.org",
      roomId: "!testroom:matrix.org",
      roomAlias: "#testroom:matrix.org",
    },
  };

  beforeEach(() => {
    vi.clearAllMocks();

    // Setup default mocks
    mockFs.existsSync.mockReturnValue(true);

    // Mock successful database transaction
    const mockTransaction = {
      insertInto: vi.fn(() => ({
        values: vi.fn(() => ({
          returning: vi.fn(() => ({
            executeTakeFirstOrThrow: vi.fn(() => Promise.resolve({ id: 123 })),
          })),
        })),
      })),
    };

    const mockTransactionBuilder = {
      execute: vi.fn(callback => callback(mockTransaction)),
      setAccessMode: vi.fn().mockReturnThis(),
      setIsolationLevel: vi.fn().mockReturnThis(),
    } as any;

    mockDb.transaction.mockReturnValue(mockTransactionBuilder);
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe("Successful IXO Creation", () => {
    it("should create IXO account and save to database successfully", async () => {
      // Arrange
      mockCreateIxoAccount.mockResolvedValue(mockIxoResult);

      // Act
      const result = await createIxoAccountBackground(mockParams);

      // Assert
      expect(result.success).toBe(true);
      expect(result.ixoProfileId).toBe(123);
      expect(result.ixoAccountId).toBe(123);
      expect(result.error).toBeUndefined();
      expect(mockCreateIxoAccount).toHaveBeenCalledWith({
        userId: mockParams.customerId,
        pin: mockParams.pin,
        lastMenuLocation: "account_creation",
        lastCompletedAction: "account_creation",
      });
    });

    //   it("should handle successful creation with proper logging", async () => {
    //     // Arrange
    //     mockCreateIxoAccount.mockResolvedValue(mockIxoResult);

    //     // Act
    //     const result = await createIxoAccountBackground(mockParams);

    //     // Assert
    //     expect(result.success).toBe(true);
    //     expect(result.duration).toBeGreaterThan(0);
    //   });
  });

  describe("Matrix Vault Integration", () => {
    it("should save Matrix vault when Matrix data is available", async () => {
      // Arrange
      mockCreateIxoAccount.mockResolvedValue(mockIxoResult);

      // Act
      const result = await createIxoAccountBackground(mockParams);

      // Assert
      expect(result.success).toBe(true);
      expect(result.matrixVaultId).toBe(123); // Should save Matrix vault
    });

    it("should handle creation without Matrix data", async () => {
      // Arrange
      const ixoResultWithoutMatrix = {
        ...mockIxoResult,
        matrix: undefined,
      };
      mockCreateIxoAccount.mockResolvedValue(ixoResultWithoutMatrix);

      // Act
      const result = await createIxoAccountBackground(mockParams);

      // Assert
      expect(result.success).toBe(true);
      expect(result.matrixVaultId).toBeUndefined(); // No Matrix vault created
    });
  });

  describe("IXO Service Failures", () => {
    // it("should handle IXO service timeout", async () => {
    //   // Arrange
    //   mockCreateIxoAccount.mockImplementation(
    //     () => new Promise((resolve) => setTimeout(() => resolve(mockIxoResult), 65000)) // Longer than 60s timeout
    //   );

    //   // Act
    //   const result = await createIxoAccountBackground(mockParams);

    //   // Assert
    //   expect(result.success).toBe(false);
    //   expect(result.error).toContain("timeout");
    //   expect(mockFs.appendFileSync).toHaveBeenCalled();
    // });

    it("should handle IXO service errors", async () => {
      // Arrange
      const errorMessage = "IXO service unavailable";
      mockCreateIxoAccount.mockRejectedValue(new Error(errorMessage));

      // Act
      const result = await createIxoAccountBackground(mockParams);

      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toBe(errorMessage);
      expect(mockFs.appendFileSync).toHaveBeenCalled();
    });
  });

  describe("Database Failures", () => {
    it("should handle database transaction failures", async () => {
      // Arrange
      mockCreateIxoAccount.mockResolvedValue(mockIxoResult);
      const mockFailedTransactionBuilder = {
        execute: vi.fn(() =>
          Promise.reject(new Error("Database connection failed"))
        ),
        setAccessMode: vi.fn().mockReturnThis(),
        setIsolationLevel: vi.fn().mockReturnThis(),
      } as any;

      mockDb.transaction.mockReturnValue(mockFailedTransactionBuilder);

      // Act
      const result = await createIxoAccountBackground(mockParams);

      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toContain("Database connection failed");
    });
  });

  describe("Error Logging", () => {
    // it("should log failures to monitoring file", async () => {
    //   // Arrange
    //   mockCreateIxoAccount.mockRejectedValue(new Error("Test error"));
    //   mockFs.existsSync.mockReturnValue(false);

    //   // Act
    //   await createIxoAccountBackground(mockParams);

    //   // Assert
    //   expect(mockFs.mkdirSync).toHaveBeenCalledWith("./logs", { recursive: true });
    //   expect(mockFs.appendFileSync).toHaveBeenCalledWith(
    //     "./logs/ixo-creation-failures.log",
    //     expect.stringContaining('"phoneNumber":"+**********"')
    //   );
    //   expect(mockFs.appendFileSync).toHaveBeenCalledWith(
    //     "./logs/ixo-creation-failures.log",
    //     expect.stringContaining('"customerId":"CUST123"')
    //   );
    // });

    it("should handle logging failures gracefully", async () => {
      // Arrange
      mockCreateIxoAccount.mockRejectedValue(new Error("Test error"));
      mockFs.appendFileSync.mockImplementation(() => {
        throw new Error("Cannot write to log file");
      });

      // Act & Assert - Should not throw
      const result = await createIxoAccountBackground(mockParams);
      expect(result.success).toBe(false);
    });
  });

  describe("Parameter Validation", () => {
    it("should handle missing parameters gracefully", async () => {
      // Arrange
      const invalidParams = {
        ...mockParams,
        customerId: "",
      };

      mockCreateIxoAccount.mockRejectedValue(new Error("Invalid userId"));

      // Act
      const result = await createIxoAccountBackground(invalidParams);

      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toContain("Invalid userId");
    });
  });
});
