/**
 * Simple IXO Account Configuration
 *
 * Validates required environment variables for IXO account creation.
 * Much simpler than the overengineered version!
 */

import { CHAIN_RPC_URL } from "../../constants/common.js";

export interface IxoConfig {
  chainRpcUrl: string;
  feegrantUrl: string;
  feegrantAuth: string;
  feegrantGranter: string;
  matrixHomeserverUrl: string;
  matrixRegistrationToken: string;
  roomBotUrl: string;
}

/**
 * Gets and validates IXO configuration from environment variables
 * @param overrides - Optional config overrides
 * @returns Validated configuration object
 * @throws Error if required config is missing
 */
export function getIxoConfig(overrides: Partial<IxoConfig> = {}): IxoConfig {
  const config = {
    chainRpcUrl:
      overrides.chainRpcUrl || CHAIN_RPC_URL || process.env.CHAIN_RPC_URL,
    feegrantUrl: overrides.feegrantUrl || process.env.FEEGRANT_URL,
    feegrantAuth: overrides.feegrantAuth || process.env.FEEGRANT_AUTH,
    feegrantGranter: overrides.feegrantGranter || process.env.FEEGRANT_GRANTER,
    matrixHomeserverUrl:
      overrides.matrixHomeserverUrl || process.env.MATRIX_HOME_SERVER,
    matrixRegistrationToken:
      overrides.matrixRegistrationToken ||
      process.env.MATRIX_REGISTRATION_TOKEN,
    roomBotUrl: overrides.roomBotUrl || process.env.MATRIX_BOT_URL,
  };

  // Check required fields
  const missing = Object.entries(config)
    /*eslint-disable-next-line @typescript-eslint/no-unused-vars */
    .filter(([_, value]) => !value)
    .map(([key]) => key);

  if (missing.length > 0) {
    throw new Error(`Missing required IXO config: ${missing.join(", ")}`);
  }

  return config as IxoConfig;
}
