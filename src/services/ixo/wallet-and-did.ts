/**
 * Wallet and DID Operations
 *
 * Handles blockchain wallet generation and DID creation.
 * Combined because they're related blockchain operations.
 */

import { OfflineSigner } from "@cosmjs/proto-signing";
import { utils } from "@ixo/impactxclient-sdk";
import { createIidDocumentIfNotExists } from "../../utils/did.js";
import { getSecpClient } from "../../utils/secp.js";
import { sendIxo } from "../ixo-transactions.js";
import { createModuleLogger } from "../logger.js";
import type { IxoConfig } from "./config.js";

const logger = createModuleLogger("wallet-and-did");

export interface BlockchainWallet {
  mnemonic: string;
  wallet: any;
  address: string;
}

/**
 * Generates a new blockchain wallet with mnemonic
 */
export async function generateWallet(): Promise<BlockchainWallet> {
  logger.debug("Generating blockchain wallet");

  const mnemonic = utils.mnemonic.generateMnemonic();
  const wallet = await getSecpClient(mnemonic);
  const address = wallet.baseAccount.address;

  logger.info({ address }, "Generated blockchain wallet");

  return { mnemonic, wallet, address };
}

/**
 * Funds a new address with tokens (temporary workaround for fee grant issues)
 */
export async function fundNewAddress(
  address: string,
  config: IxoConfig
): Promise<void> {
  logger.debug({ address }, "Funding new address with tokens");

  const fundingMnemonic = process.env.FUNDING_WALLET_MNEMONIC;
  const fundingAddress = process.env.FUNDING_WALLET_ADDRESS;

  if (!fundingMnemonic || !fundingAddress) {
    throw new Error("Funding wallet configuration missing");
  }

  logger.info(
    {
      fromAddress: fundingAddress,
      toAddress: address,
      amount: "250000 uixo",
    },
    "Sending funding transaction"
  );

  try {
    const result = await sendIxo(
      fundingMnemonic,
      address,
      "250000",
      config.chainRpcUrl,
      "Initial funding for new IXO account"
    );

    logger.info(
      {
        address,
        transactionHash: result.transactionHash,
        height: result.height,
      },
      "Address funded successfully"
    );
  } catch (error) {
    logger.error(
      {
        address,
        fundingAddress,
        error: error instanceof Error ? error.message : String(error),
      },
      "Failed to fund address"
    );
    throw error;
  }
}

/**
 * Sets up fee grant for an address (temporarily bypassed)
 */
export async function setupFeegrant(
  address: string,
  config: IxoConfig
): Promise<void> {
  logger.debug(
    { address },
    "Setting up fee grant (bypassed - using direct funding)"
  );

  // Temporarily bypass fee grant and use direct funding instead
  await fundNewAddress(address, config);

  logger.info({ address }, "Fee grant setup complete (via direct funding)");
}

/**
 * Creates or gets existing DID for an address with retry logic for fee grant propagation
 */
export async function createDid(
  address: string,
  offlineSigner: OfflineSigner,
  chainRpcUrl: string,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  feegrantGranter?: string // Optional since we're using direct funding
): Promise<string> {
  logger.debug({ address }, "Creating DID");

  // Add minimal retry logic (should rarely be needed with proper finality wait)
  const maxRetries = 2;
  const retryDelay = 3000; // 3 seconds

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      logger.debug({ address, attempt, maxRetries }, "Attempting DID creation");

      const did = await createIidDocumentIfNotExists({
        address,
        offlineSigner,
        chainRpcUrl,
        feegrantGranter: undefined, // Don't use fee grant - account has its own tokens
      });

      logger.info({ address, did, attempt }, "DID created successfully");
      return did;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);

      // Check if it's the "account does not exist" error
      if (
        errorMessage.includes("does not exist on chain") &&
        attempt < maxRetries
      ) {
        logger.warn(
          {
            address,
            attempt,
            maxRetries,
            error: errorMessage,
            retryDelay,
          },
          "Account not found on chain, waiting for blockchain finality before retry"
        );

        // Wait before retrying
        await new Promise(resolve => setTimeout(resolve, retryDelay));
        continue;
      }

      // If it's the last attempt or a different error, throw it
      logger.error(
        {
          address,
          attempt,
          maxRetries,
          error: errorMessage,
        },
        "DID creation failed"
      );
      throw error;
    }
  }

  throw new Error(`Failed to create DID after ${maxRetries} attempts`);
}
