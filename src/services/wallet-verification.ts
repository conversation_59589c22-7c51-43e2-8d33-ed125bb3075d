/**
 * Wallet Verification Service
 *
 * Handles wallet ID verification and authentication.
 * Verifies that the provided MSISDN belongs to the given Wallet ID.
 */

import { createModuleLogger } from "./logger.js";

const logger = createModuleLogger("wallet-verification");

export interface WalletVerificationInput {
  walletId: string;
  phoneNumber: string;
}

export interface WalletVerificationResult {
  success: true;
  walletId: string;
  balance: number;
  customerName: string;
  phoneNumber: string;
}

export interface WalletVerificationError {
  success: false;
  error: string;
}

export type WalletVerificationResponse =
  | WalletVerificationResult
  | WalletVerificationError;

/**
 * Production wallet verification service
 * In production, this would call the actual backend API
 */
export class WalletVerificationService {
  async verifyWallet(
    input: WalletVerificationInput
  ): Promise<WalletVerificationResult> {
    logger.info("Verifying wallet", {
      walletId: input.walletId,
      phoneNumber: input.phoneNumber.substring(0, 4) + "****",
    });

    // TODO: Replace with actual API call
    // const response = await fetch('/api/wallet/verify', {
    //   method: 'POST',
    //   body: JSON.stringify(input)
    // });

    // For now, simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    // TODO: Replace with actual validation logic
    throw new Error("Production wallet verification not yet implemented");
  }
}

/**
 * Mock wallet verification service for development and testing
 */
export class MockWalletVerificationService {
  private validWallets = [
    {
      walletId: "C21009802",
      customerName: "John Mwanza",
      balance: 250.75,
    },
    {
      walletId: "C21009803",
      customerName: "Mary Banda",
      balance: 180.5,
    },
    {
      walletId: "C21009804",
      customerName: "Peter Phiri",
      balance: 95.25,
    },
    {
      walletId: "C21009805",
      customerName: "Grace Tembo",
      balance: 420.0,
    },
  ];

  async verifyWallet(
    input: WalletVerificationInput
  ): Promise<WalletVerificationResult> {
    logger.info("Mock verifying wallet", {
      walletId: input.walletId,
      phoneNumber: input.phoneNumber.substring(0, 4) + "****",
    });

    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 800));

    // Find wallet in mock data
    const wallet = this.validWallets.find(w => w.walletId === input.walletId);

    if (!wallet) {
      logger.warn("Wallet verification failed", { walletId: input.walletId });
      throw new Error(
        "Invalid wallet ID or phone number does not match wallet"
      );
    }

    // Add some randomness to balance for testing
    const balanceVariation = (Math.random() - 0.5) * 50; // ±25
    const finalBalance = Math.max(0, wallet.balance + balanceVariation);

    const result: WalletVerificationResult = {
      success: true,
      walletId: input.walletId,
      balance: finalBalance,
      customerName: wallet.customerName,
      phoneNumber: input.phoneNumber,
    };

    logger.info("Wallet verification successful", {
      walletId: input.walletId,
      customerName: wallet.customerName,
      balance: finalBalance,
    });

    return result;
  }

  /**
   * Add a wallet to the mock data (useful for testing)
   */
  addMockWallet(walletId: string, customerName: string, balance: number): void {
    this.validWallets.push({ walletId, customerName, balance });
    logger.info("Added mock wallet", { walletId, customerName, balance });
  }

  /**
   * Get all valid wallet IDs (useful for testing)
   */
  getValidWalletIds(): string[] {
    return this.validWallets.map(w => w.walletId);
  }
}

// Export singleton instances
export const walletVerificationService = new WalletVerificationService();
export const mockWalletVerificationService =
  new MockWalletVerificationService();
